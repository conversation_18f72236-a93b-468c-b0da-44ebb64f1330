<?php

declare(strict_types=1);

namespace App\Service\Email\EmailTemplate;

use App\Models\Email\EmailTemplate;

final class EmailTemplateService extends Base
{
    public function preProcessInput(&$input)
    {
        $input['text_html'] = self::extractInlineImage($input['text_html'] ?? null);
    }

    public function create(array $input): EmailTemplate
    {
        $this->preProcessInput($input);

        /** @var EmailTemplate $row */
        $row = EmailTemplate::create($input);

        if ($input['gen_sort'] ?? false) {
            $row->sort = intval(EmailTemplate::query()
                    ->where('category_id', $row->category_id)
                    ->where('id', '!=', $row->id)
                    ->max('sort')
                ) + 1;
            $row->save();
        }

        return $row;
    }

    public function update($id, $input): EmailTemplate
    {
        $this->preProcessInput($input);

        /** @var EmailTemplate $row */
        $row = EmailTemplate::findOrFail($id);
        $row->update($input);

        if ($input['gen_sort'] ?? false) {
            $row->sort = intval(EmailTemplate::query()
                    ->where('category_id', $row->category_id)
                    ->where('id', '!=', $row->id)
                    ->max('sort')
                ) + 1;
            $row->save();
        }

        return $row;
    }

    public function getOne(int $id, array $params = []): EmailTemplate
    {
        return $this->emailTemplateRepository->getQueryBuilder()->where('id', $id)->first();
    }

    public function getEmailTemplatesByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($perPage < 1) {
            $perPage = self::DEFAULT_PER_PAGE_PAGINATION;
        }

        return $this->emailTemplateRepository->getEmailTemplatesByPage(
            $page,
            $perPage,
            $params
        );
    }

    public function delete($ids): void
    {
        $arr = is_array($ids) ? $ids : explode(',', $ids);
        $this->emailTemplateRepository->getQueryBuilder()->whereIn('id', $arr)->delete();
    }
}

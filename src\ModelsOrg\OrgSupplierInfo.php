<?php

namespace App\ModelsOrg;

/**
 * @property string $id
 * @property string $supplier_id
 * @property string $comment
 * @property string $asp
 * @property string $created_on
 */
class OrgSupplierInfo extends BaseModel
{
    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'supplier_info';

    /**
     * The "type" of the auto-incrementing ID.
     * 
     * @var string
     */
    protected $keyType = 'string';

    /**
     * Indicates if the IDs are auto-incrementing.
     * 
     * @var bool
     */
    public $incrementing = false;

    /**
     * @var array
     */
    protected $fillable = ['supplier_id', 'comment', 'asp', 'created_on'];
}

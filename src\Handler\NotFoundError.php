<?php

declare(strict_types=1);

namespace App\Handler;

use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;

final class NotFoundError
{
    public function __invoke(
        Request $request,
        Response $response
    ): Response {

        $statusCode = 404;
        $data = [
            'code' => $statusCode,
            'status' => 'error',
            'message' => "Not found",
            'class' => "NotFoundError",
        ];
        $body = json_encode($data, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT);
        $response->getBody()->write((string) $body);

        $newResp = $response
            ->withStatus(500)
            ->withHeader('Access-Control-Allow-Headers', 'X-Requested-With, Content-Type, Accept, Origin, Authorization, AccessToken')
            ->withHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS');

        $newResp = setCorsOrigin($request, $newResp);
        return $newResp;
    }
}

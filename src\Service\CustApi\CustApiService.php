<?php

namespace App\Service\CustApi;

use App\Exception\BaseException;
use App\Lib\Func;
use App\Lib\FuncModel;
use App\Models\Email\EmailReceiver;
use App\Models\SyncLog;
use App\Models\Sys\SysLog;
use App\Service\BaseApiService;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\RequestOptions;
use Slim\Container;

class CustApiService extends BaseApiService
{
    public function __construct(Container $container)
    {
        parent::__construct($container);

        $baseUrl = $container->get('settings')['custApiUrl'] ?? '';
        $baseUrl = rtrim($baseUrl, '/') . '/';

        $this->apiToken = $container->get('settings')['custApiToken'] ?? '';
        $this->apiSetting = ['endpoint' => $baseUrl];
        if (!$baseUrl || !$this->apiToken) {
            BaseException::raiseInternalServerError('Media API setting is required!');
        }

        $this->getApiClient();
    }

    public function getClientHeaderOption()
    {
        return [
            'headers' => [
                'Api-Token' => $this->apiToken,
                'content-type' => 'multipart/form-data'
            ]
        ];
    }

    /**
     * Get Email tracking histories list from Media Service of WHC.
     *
     * @throws GuzzleException
     */
    public function getEmailTrackingHistory(int $page, int $perPage, $params = [])
    {
        $query = array_merge(compact('page', 'perPage'), $params);
        $query['category'] = 'Email Tracking supplier';

        $res = $this->apiClient->get('rest/sys/sys-log', [RequestOptions::QUERY => $query]);

        $this->isSuccess = $res->getStatusCode() >= 200 && $res->getStatusCode() <= 300;

        if ($this->isSuccess) {
            return json_decode($res->getBody()->__toString(), true);
        } else {
            BaseException::raiseInternalServerError($res->getBody()->__toString());
        }
    }

    /**
     * Down sync latest Email Tracking History
     *
     * @param array $input
     * @return void
     * @throws GuzzleException
     */
    public function dsEmailTrackingHistory(array $input = []): void
    {
        FuncModel::$batchCode = Func::randomTs();

        $maxId = SysLog::query()
            ->whereIn('category', [SysLog::CATEGORY_EMAIL_TRACKING, SysLog::CATEGORY_EMAIL_TRACKING_CALL, SysLog::CATEGORY_EMAIL_TRACKING_OFFER_TPL])
            ->max('sync_origin_id') ?? 0;


        $note = "Started from Original ID: {$maxId}.";
        $logIdGlobal = FuncModel::createSyncLog(SyncLog::SYNC_TYPE_DOWN, SyncLog::NAME_WHC_EMAIL_TRACKING_LOG_SYNC, $note);

        $pageSize = $input['pageSize'] ?? 200;
        $queryParams = [
            'gt_id' => $maxId,
            'sort' => [
                'id' => 'descend',
            ],
        ];

        $totalCount = 0;
        do {
            $logId = FuncModel::createSyncLog(SyncLog::SYNC_TYPE_DOWN, SyncLog::NAME_WHC_EMAIL_TRACKING_LOG_SYNC);
            try {
                $subCount = 0;

                $page = 1;
                $queryParams['gt_id'] = $maxId;
                $resData = $this->getEmailTrackingHistory($page, $pageSize, $queryParams);

                if ($this->isSuccess) {
                    if (($items = $resData['message']['data'] ?? [])) {
                        $fetchedNote = "Fetched " . count($items) . ' items.';
                        FuncModel::updateSyncLog($logId, SyncLog::STATUS_PROCESSING, $fetchedNote);

                        $dbRows = [];
                        $emailIds = [];
                        foreach ($items as $ind => $row) {
                            $dbRowTmp = SysLog::getBoundData($row, false);
                            $dbRowTmp['sync_origin_id'] = $row['id'];

                            $dbRows[] = $dbRowTmp;
                            $emailIds[] = $dbRowTmp['ean_id'];  // email ID.
                            $subCount++;
                        }

                        SysLog::insert($dbRows);

                        if ($subCount) {
                            $qb1 = SysLog::query()
                                ->select([])
                                ->selectRaw("ean_id AS email_id")
                                ->selectRaw("ref2 AS email")
                                ->selectRaw("COUNT(*) AS open_count")
                                ->selectRaw("MIN(created_on) AS open_first_seen_on")
                                ->selectRaw("MAX(created_on) AS open_seen_updated_on")
                                ->whereIn('category', [SysLog::CATEGORY_EMAIL_TRACKING, SysLog::CATEGORY_EMAIL_TRACKING_CALL, SysLog::CATEGORY_EMAIL_TRACKING_OFFER_TPL])
                                ->whereIn('ean_id', $emailIds)
                                ->groupBy('ean_id')
                                ->groupBy('ref2');

                            $data = $qb1->get()->toArray();

                            EmailReceiver::upsert($data, ['email_id', 'email']);
                        }

                        FuncModel::updateSyncLog($logId, SyncLog::STATUS_SUCCESS, ['note' => $fetchedNote . " Synced {$subCount}."]);

                        $totalCount += count($items);
                        if (!$resData['message']['pagination']['hasMore']) {
                            break;
                        }
                    } else {
                        FuncModel::updateSyncLog($logId, SyncLog::STATUS_SUCCESS, ['note' => "Nothing to do."]);
                        break;
                    }
                } else {
                    FuncModel::updateSyncLog($logId, SyncLog::STATUS_ERROR, json_encode($this->errorDetail));
                    break;
                }
                // sleeping for 0.3s
                usleep(300);
            } catch (\Exception $exception) {
                FuncModel::updateSyncLog($logId, SyncLog::STATUS_ERROR, $exception->getMessage());
                throw $exception;
            }
        } while (true);

        FuncModel::updateSyncLog($logIdGlobal, SyncLog::STATUS_SUCCESS, ['note' => $note . " Processed {$totalCount}"]);
    }


    public function extractInlineImage(string $html, $src = 'supplier')
    {
        if (!Func::hasInlineImage($html)) {
            return $html;
        }

        $res = $this->apiClient->post('rest/file/extractInlineImage', [RequestOptions::JSON => [
            'html' => $html,
            'src' => $src
        ]]);

        $this->isSuccess = $res->getStatusCode() >= 200 && $res->getStatusCode() <= 300;

        if ($this->isSuccess) {
            $tmp = json_decode($res->getBody()->__toString(), true);
            return $tmp ? $tmp['message'] ?? null : null;
        } else {
            BaseException::raiseInternalServerError($res->getBody()->__toString());
        }
    }
}
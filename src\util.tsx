import { notification } from "antd";
import type { RcFile } from "antd/lib/upload";
import _, { isArray, isNumber, isObject, round } from "lodash";
import type { KeyboardEvent } from "react";
import { CURRENT_YEAR, DT_FORMAT_DMY, DT_FORMAT_TIME_MAX_S, DT_FORMAT_YMD, LS_PREFIX } from "./constants";
import type { ArgsProps } from "antd/lib/notification";
import { DefaultOptionType } from "antd/lib/select";
import dayjs from "dayjs";
import isoWeek from "dayjs/plugin/isoWeek";

const safeInt = (valParam: any) => {
  if (!valParam) return 0;
  const val = parseInt(valParam, 10);
  if (isNaN(val)) return 0;
  else return val;
};

/* const safeNumber = (valParam: any, decimals?: number) => {
  if (!valParam) return 0;
  const val = Number(valParam);
  if (isNaN(val)) return 0;
  else return typeof decimals !== 'undefined' ? round(val, decimals) : val;
};
 */

/**
 * Safely converts a value to a number with optional decimal precision
 * @param valParam - The value to convert to a number
 * @param decimals - Optional number of decimal places to round to
 * @returns A safe number (0 if conversion fails)
 */
export const safeNumber = (valParam: unknown, decimals?: number): number => {
  if (valParam == null || valParam === "" || valParam === false) return 0;

  if (valParam === true) return 1;
  const val = Number(valParam);
  if (!Number.isFinite(val)) return 0;

  return decimals !== undefined ? round(val, decimals) : val;
};

export const sn = (valParam: any, decimals?: number) => {
  return safeNumber(valParam, decimals);
};

export const isNumeric = (val: any) => {
  if (!val) return false;
  const tmp = Number(val);
  return !isNaN(tmp);
};

function isZero(valueParam: number, digits?: number) {
  const value = round(valueParam, (digits ?? 0) + 1);
  return !(value > 0 || value < 0);
}

export const numTo2Digits = (num?: string | number): string => {
  const n = sn(num);
  if (n < 10) return `0${n}`;
  else return `${n}`;
};

/**
 * Converts a number to a 3-digit zero-padded string
 * @param num - The number to convert
 * @returns A 3-digit zero-padded string (e.g., 23 -> "023", 1 -> "001")
 */
export const lpadNumber = (num: unknown, length = 3, padStr = "0"): string => {
  return sn(num).toString().padStart(length, padStr);
};

const numberFormat = (valueParam: any, zero_show?: boolean, digits = 0, removeLeadingZero = false) => {
  let value = valueParam;
  if (isZero(value)) {
    return typeof zero_show == "undefined" || !zero_show ? "" : removeLeadingZero && !zero_show ? "" : 0;
  }

  const config: Intl.NumberFormatOptions = {
    minimumFractionDigits: removeLeadingZero ? 0 : digits,
    maximumFractionDigits: digits,
    signDisplay: "auto",
  };

  // value = round(value, digits);
  if (isZero(value, digits)) value = 0;

  const x = new Intl.NumberFormat("de-DE", {
    ...config,
    trailingZeroDisplay: "auto",
  } as any).format(value);

  if (x == "-0") return zero_show ? 0 : "";

  return x;
};

const numberFormatEn = (value: any, zero_show?: boolean, digits = 0) => {
  if (isZero(value)) return typeof zero_show == "undefined" || !zero_show ? "" : 0;
  const x = new Intl.NumberFormat("en-US", {
    minimumFractionDigits: digits,
    maximumFractionDigits: digits,
  }).format(value);

  return x;
};

export const ni = (value: any, zero_show?: boolean) => {
  return numberFormat(value, zero_show);
};

export const nf2 = (value: any, zero_show?: boolean, removeLeadingZero?: boolean) => {
  return numberFormat(value, zero_show, 2, removeLeadingZero);
};

export const nf3 = (value: any, zero_show?: boolean, removeLeadingZero?: boolean) => {
  return numberFormat(value, zero_show, 3, removeLeadingZero);
};

export const skuToItemId = (sku?: string) => {
  return (sku ?? "").split("_")[0] ?? "";
};

export const csv2Arr = (str?: any) => {
  if (str) {
    if (typeof str === "string") {
      return str.split(",");
    } else {
      return str ?? [];
    }
  }
  return [];
};

const error = (errorMsg: string | any, duration = 0) => {
  if (!errorMsg) return;
  const notProp: ArgsProps = { message: null, placement: "top" };
  if (typeof errorMsg === "string") {
    notProp.message = errorMsg;
  } else {
    const obj: any = errorMsg.data ?? errorMsg;
    if (obj.message) {
      // isAxios Error?
      if (obj.response) {
        const resData = obj.response.data;
        if (resData) {
          notProp.message = resData.message;
          if (process.env.NODE_ENV === "development" && resData?.trace) {
            notProp.description = JSON.stringify(resData?.trace || "");
          }
        }
      } else {
        if (typeof obj.message === "string") {
          notProp.message = obj.message;
        }
        if (process.env.NODE_ENV === "development" && obj?.trace) {
          notProp.description = JSON.stringify(obj?.trace || "");
        }
      }
    }

    // We suppress trace detail in 401 or 403
    if (obj?.code === 401 || obj?.code === 403) {
      notProp.description = null;
      notProp.duration = 5;
    }
  }
  if (notProp.message || notProp.description) {
    notification.error({ duration, ...notProp });
  }
};

export const notifySuccess = (msg: string | any, duration = 0) => {
  let msgEle = msg;

  if (msgEle && typeof msgEle == "string") {
    const arr = msgEle.split("\n");
    // eslint-disable-next-line react/no-array-index-key
    msgEle = arr.map((x, ind) => <div key={ind}>{x}</div>);
  }
  notification.success({ duration: duration || 5, message: msgEle || "Success", placement: "top" });
};

const safeJsonParse = (val: any) => {
  if (!val) return null;
  if (typeof val == "string") {
    try {
      return JSON.parse(val);
    } catch (e) {
      return null;
    }
  } else {
    return val;
  }
};

const waitTime = async (time: number = 100) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true);
    }, time);
  });
};

const LS_APP_SETTING = "FS_APP_SETTING";
const lsUpdate = (path: any, value: any, override?: boolean) => {
  const old = localStorage.getItem(LS_APP_SETTING) ?? "{}";
  const oldObj = safeJsonParse(old);
  if (override) _.set(oldObj, path, typeof (value === "string") ? value : JSON.stringify(value));
  else _.set(oldObj, path, typeof (value === "string") ? value : JSON.stringify(value));
  // console.log(oldObj);
  localStorage.setItem(LS_APP_SETTING, JSON.stringify(oldObj));
};
const lsGet = (path: any) => {
  const old = localStorage.getItem(LS_APP_SETTING) ?? "{}";
  const oldObj = safeJsonParse(old);
  return _.get(oldObj, path);
};

export const dtParseGA = (value: string) => {
  return `${value.substring(0, 4)}-${value.substring(4, 6)}-${value.substring(6)}`;
};

/**
 * Date formatter value. Hybrid usage by `format`
 *
 * @param value
 * @param format
 * @param defaultInvalidValue
 * @returns
 */
const dtToDMY = (value: any, format?: string, defaultInvalidValue?: any, tzoffset?: number) => {
  if (!value) return defaultInvalidValue;
  const m = dayjs(value);
  if (m.isValid() && tzoffset) {
    m.add(tzoffset, "hour");
  }

  if (m.isValid()) return m.format(format ?? DT_FORMAT_DMY);
  return defaultInvalidValue;
};

const dtToDMYHHMM = (value: any, invalidValue?: any) => {
  return dtToDMY(value, `${DT_FORMAT_DMY} HH:mm`, invalidValue);
};

const dtToHHMM = (value: any, invalidValue?: any) => {
  return dtToDMY(value, `HH:mm`, invalidValue);
};

const dtBrowserTzOffset = () => -new Date().getTimezoneOffset() / 60;

/**
 * Get local date from datetime in GMT-0 timezone.
 *
 * @param value
 * @param invalidValue
 * @returns
 */
const dtToDMYHHMMTz = (value: any, invalidValue?: any) => {
  // const tzoffset = dtBrowserTzOffset();
  const tzoffset = 0;
  return dtToDMY(value, `${DT_FORMAT_DMY} HH:mm`, invalidValue, tzoffset);
};

/**
 * Note: srcFormat may be variable.
 * @param value
 * @param srcFormat
 * @returns
 */
const dtToYMD = (value: any, srcFormat = DT_FORMAT_DMY) => {
  if (!value) return undefined;
  const m = typeof value === "string" ? dayjs(value, value.includes("-") ? DT_FORMAT_YMD : srcFormat) : dayjs(value);
  if (m.isValid()) return m.format(DT_FORMAT_YMD);
  return undefined;
};

export const dtYw = (value: string) => {
  const arr = `${value}`.split("|");
  if (arr.length > 2) {
    return `${dtToDMY(arr[1], "DD.MM.")} ~ ${dtToDMY(arr[2], "DD.MM.")}`;
  }
  return "";
};

export const dtYwNo = (value: string) => {
  const arr = `${value}`;
  if (arr.length >= 6) {
    const year = +arr.substring(0, 4);
    if (!year) return "";
    return `${+arr.substring(4, 6)}` + (year != CURRENT_YEAR ? " '" + Math.abs(year - 2000) : "");
  }
  return "";
};

const getBase64 = (file: RcFile): Promise<string> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (errorInner) => reject(errorInner);
  });

function isTabPressed(e: KeyboardEvent) {
  return (e.which && e.which == 9) || (e.keyCode && e.keyCode == 9) || e.key == "Tab";
}

function isEnterKey(e: KeyboardEvent) {
  return (e.which && e.which == 13) || (e.keyCode && e.keyCode == 13) || e.key == "Enter";
}

function isFirefox() {
  return navigator.userAgent.search("Firefox") > -1;
}

/**
 * Get formatted prices: gross and netPrice
 * @param priceParam
 * @param vat  % unit
 * @returns
 */
/* function fPrices(netPriceParam: any, vat: any, zeroShow = false, isNet = true): string[] {
  const price = safeNumber(netPriceParam);
  const grossPrice = isNet
    ? price * (1 + safeNumber(vat) / 100)
    : price / (1 + safeNumber(vat) / 100);
  const prices = ['0', '0'];

  if (zeroShow) {
    prices[0] = price.toFixed(2);
    prices[1] = grossPrice.toFixed(2);
  } else {
    prices[0] = isZero(price) ? '' : price.toFixed(2);
    prices[1] = isZero(grossPrice) ? '' : grossPrice.toFixed(2);
  }
  return isNet ? prices : [prices[1], prices[0]];
} */

export const toRound = function (number: number, precision: number) {
  if (precision < 0) {
    const factor = Math.pow(10, precision);
    return Math.round(number * factor) / factor;
  } else return +(Math.round(Number(number + "e+" + precision)) + "e-" + precision);
};

function fPrices(netPriceParam: any, vat: any, zeroShow = false, isNet = true): string[] {
  const precision = 7;
  const price = safeNumber(netPriceParam);
  const grossPrice = isNet ? price * (1 + safeNumber(vat) / 100) : price / (1 + safeNumber(vat) / 100);
  const prices: any[] = ["0", "0"];

  if (zeroShow) {
    prices[0] = toRound(price, precision);
    prices[1] = toRound(grossPrice, precision);
  } else {
    prices[0] = isZero(price) ? "" : toRound(price, precision);
    prices[1] = isZero(grossPrice) ? "" : toRound(grossPrice, precision);
  }
  return isNet ? prices : [prices[1], prices[0]];
}

const genNewKey = (prefix?: string) => (prefix ?? `key_${_.now()}_`) + _.uniqueId();

/**
 * Safely get divided value
 * @param price any
 * @param case_qty any
 */
export function casePrice(price: any, caseQty: any) {
  const val = sn(price);
  const safeCaseQty = sn(caseQty);
  return caseQty ? val / safeCaseQty : val;
}

export const sCap = (str: string | undefined | null) => {
  return `${str || ""}`.replace(/\w+/g, (word: string) => {
    return word.charAt(0).toUpperCase() + word.slice(1);
  });
};

export const sFirstWord = (str: string | undefined | null) => {
  return str ? str.slice(0, str.indexOf(" ")) : "";
};

export const inCsvStr = (keyStr: string, csvStr: string) => {
  return `,${csvStr},`.includes(`,${keyStr},`);
};

export function sEllipsed(str = "", width = 10, endWidth?: number): string {
  if (str && str.length <= width) {
    return str;
  }
  if (endWidth) return str ? `${str.slice(0, width)}...${str.slice(-endWidth)}` : "";
  else return str ? `${str.slice(0, width)}...` : "";
}

export function sFileName(path = ""): string {
  const pos = path.lastIndexOf("/");
  if (pos >= 0) {
    return path.substring(pos + 1);
  } else {
    return path;
  }
}

export function sLeftPad(str: any, maxLength: number, padStr: string, prefix?: string) {
  return str ? (prefix ?? " - ") + `${str}`.padStart(maxLength, padStr) : "";
}

export const nl2br = (str: any) => {
  return (str ?? "").replaceAll(/\n/gi, "<br />");
};

/**
 * Search Form setting
 * @param key
 * @param defaultValue
 * @returns
 */
const getSfValues = (key: string, defaultValue?: any, override?: any) => {
  const oldObj = safeJsonParse(localStorage.getItem(`${LS_PREFIX}${key}`));
  const ret = oldObj ? oldObj : (defaultValue ?? null);
  if (override) {
    for (const okey in override) {
      if (override[okey]) {
        ret[okey] = override[okey];
      }
    }
  }

  return ret;
};

const setSfValues = (key: string, data?: any, raw?: boolean) => {
  localStorage.setItem(`${LS_PREFIX}${key}`, !raw ? JSON.stringify(data) : data);
};

const mergeGSearch = (localValues: any) => {
  // @version 1. Disable it now.
  /* const gSearch = getSfValues('cu_sf_gSearch', {});
  for (const key in localValues ?? {}) {
    gSearch[key] = localValues[key];
  }
  return gSearch;
   */
  return localValues;
};

export type DateRangeType = {
  from?: string;
  to?: string;
  title?: string;
};

const isEmptyValueObject = (val: any) => {
  const keys = Object.keys(val ?? {});
  for (const k of keys) {
    const tmp = (val ?? {})[k];
    if (isArray(tmp)) {
      if (tmp.length > 0) return false;
    } else {
      if (tmp) return false;
    }
  }
  return true;
};

/**
 * Convert object to FormData.
 *
 * @param object
 * @returns
 */
export const getFormData = (object: any) => {
  return Object.keys(object).reduce((formData, key) => {
    const valueObj = object[key];
    let newValue = valueObj;
    if (newValue !== undefined) {
      if (typeof valueObj == "boolean") {
        newValue = valueObj ? 1 : 0;
      } else if (Array.isArray(valueObj)) {
        valueObj.forEach((item, index) => {
          formData.set(`${key}[${index}]`, item ? item : "");
        });

        return formData;
      }
      formData.set(key, newValue ? newValue : "");
    }
    return formData;
  }, new FormData());
};

/**
 * Build date range by Range type
 *
 * @param intervalType
 * @param lastInterval
 * @returns
 */
const dtBuildRanges = (intervalType: "d" | "w" | "m", lastInterval: number, offset?: number, isReverse?: boolean) => {
  const ranges: DateRangeType[] = [];
  const startInd = offset ?? 0;

  for (let ind = startInd; ind < lastInterval + startInd; ind++) {
    let range: DateRangeType = {};
    if (intervalType == "d") {
      range = {
        from: dayjs().subtract(ind, "days").format(DT_FORMAT_YMD),
        to: dayjs().subtract(ind, "days").format(DT_FORMAT_YMD) + DT_FORMAT_TIME_MAX_S,
      };
      range.title = dtToDMY(range.from, "DD.MM.", "");
    } else if (intervalType == "w") {
      range = {
        from: dayjs().startOf("isoWeek").subtract(ind, "w").format(DT_FORMAT_YMD),
        to: dayjs().endOf("isoWeek").subtract(ind, "w").format(DT_FORMAT_YMD) + DT_FORMAT_TIME_MAX_S,
      };
      range.title = dtToDMY(range.from, "DD.MM.", "") + "~" + dtToDMY(range.to, "DD.MM.", "");
    } else {
      range = {
        from: dayjs().startOf("month").subtract(ind, "months").format(DT_FORMAT_YMD),
        to: dayjs().endOf("month").subtract(ind, "months").format(DT_FORMAT_YMD) + DT_FORMAT_TIME_MAX_S,
      };
      range.title = dtToDMY(range.from, "MMM `YY", "");
    }
    ranges.push(range);
  }

  return isReverse ? ranges.reverse() : ranges;
};

export const sUrlByTpl = (tpl: string, vars: any) => {
  let ret = tpl || "";
  Object.keys(vars).forEach((key) => {
    ret = ret.replaceAll(`{${key}}`, `${vars[key]}`);
  });
  return ret;
};

/* export const fullAddress = (order: API.Order, type?: 'shipping' | 'invoice') => {
  let addr = '';
  if (type == 'shipping') {
    // if (order.sa_fullname) addr += order.sa_fullname;
    if (order.sa_street) addr += ' ' + order.sa_street;
    if (order.sa_city) addr += ', ' + order.sa_city;
    if (order.sa_zip) addr += ', ' + order.sa_zip;
    if (order.sa_country_code) addr += ', ' + order.sa_country_code;
  } else {
    if (order?.detail?.billing_address) {
      const addrObj = order.detail.billing_address;
      // if (addrObj.sa_fullname) addr += addrObj.sa_fullname;
      if (addrObj.street)
        addr += ' ' + (isArray(addrObj.street) ? addrObj.street.join(', ') : addrObj.street);
      if (addrObj.city) addr += ', ' + addrObj.city;
      if (addrObj.postcode) addr += ', ' + addrObj.postcode;
      if (addrObj.country_id) addr += ', ' + addrObj.country_id;
    }
  }
  return addr;
}; */

const dtAbsoluteMonths = (str: string) => {
  const months = Number(dayjs(str).format("MM"));
  const years = Number(dayjs(str).format("YYYY"));
  return months + years * 12;
};

const dtMonthDiff = (start: string, end: string) => {
  const startAbsMonth = dtAbsoluteMonths(start);
  const endAbsMonth = dtAbsoluteMonths(end);
  return endAbsMonth - startAbsMonth;
};

const dtBuildRangesFromLastMonth = (intervalType: "d" | "w" | "m", lastInterval: number, lastMonthStr: string, isReverse?: boolean) => {
  const ranges: DateRangeType[] = [];
  const startInd = 0;

  for (let ind = startInd; ind < lastInterval + startInd; ind++) {
    let range: DateRangeType = {};
    if (intervalType == "d") {
      range = {
        from: dayjs(lastMonthStr).subtract(ind, "days").format(DT_FORMAT_YMD),
        to: dayjs(lastMonthStr).subtract(ind, "days").format(DT_FORMAT_YMD) + DT_FORMAT_TIME_MAX_S,
      };
      range.title = dtToDMY(range.from, "DD.MM.", "");
    } else if (intervalType == "w") {
      range = {
        from: dayjs(lastMonthStr).startOf("isoWeek").subtract(ind, "w").format(DT_FORMAT_YMD),
        to: dayjs(lastMonthStr).endOf("isoWeek").subtract(ind, "w").format(DT_FORMAT_YMD) + DT_FORMAT_TIME_MAX_S,
      };
      range.title = dtToDMY(range.from, "DD.MM.", "") + "~" + dtToDMY(range.to, "DD.MM.", "");
    } else {
      range = {
        from: dayjs(lastMonthStr).startOf("month").subtract(ind, "months").format(DT_FORMAT_YMD),
        to: dayjs(lastMonthStr).endOf("month").subtract(ind, "months").format(DT_FORMAT_YMD) + DT_FORMAT_TIME_MAX_S,
      };
      range.title = dtToDMY(range.from, "MMM `YY", "");
    }
    ranges.push(range);
  }

  return isReverse ? ranges.reverse() : ranges;
};

function pad(a: number, b: number) {
  return (1e15 + a + "").slice(-b);
}

const dtBuildRangesInFy = (now?: Date | string | number, isReverse?: boolean, includeLastMonth?: boolean): DateRangeType[] => {
  const baseDt = now ? (typeof now == "string" ? new Date(now) : typeof now === "number" ? new Date(`${1 + now}-06-30`) : now) : new Date();
  // Year & Month of 'Now' ('Now' depends on input parameter)
  const year = baseDt.getFullYear();
  const month = baseDt.getMonth();

  // Year & Month of start month of fiscal year based on year/month
  const fyYear = month >= 6 ? year : year - 1;
  const fyMonth = 6; // July
  const ranges: DateRangeType[] = [];

  const todayYear = new Date().getFullYear();
  const todayMonth = new Date().getMonth();
  let fyLastMonth = month + (includeLastMonth ? 1 : 0);
  if (year == todayYear && fyLastMonth > todayMonth) {
    fyLastMonth = todayMonth;
  }

  let m = fyMonth;
  for (let y = fyYear; y <= year; y++) {
    while (!(y == year && m == fyLastMonth)) {
      const mm = pad(m + 1, 2);
      const range = {
        from: `${y}-${mm}-01`,
        to: `${y}-${mm}-${new Date(y, m + 1, 0).getDate()}`,
        title: dtToDMY(`${y}-${mm}-01`, "MMM `YY", ""),
      };
      ranges.push(range);
      if (m == 11) {
        y++;
      }
      // increase month
      m = (m + 1) % 12;
    }
  }

  return isReverse ? ranges.reverse() : ranges;
};

const dtCurrentFy = (today?: string | Date): number => {
  let dt;
  if (today) {
    dt = typeof today === "string" ? new Date(today) : today;
  } else {
    dt = new Date();
  }
  return dt.getMonth() < 6 ? dt.getFullYear() - 1 : dt.getFullYear();
};

const dtBuildFyKv = (lastYear?: number, length?: number): Record<number | string, string> => {
  const ret: Record<number, string> = {};
  for (let i = 0; i < (length ?? 10); i++) {
    const y = (lastYear ?? dtCurrentFy() + 1) - i;
    ret[y] = `${y % 2000}/${(y + 1) % 2000}`;
  }
  return ret;
};

/**
 * Build fiscal years list.
 * @param now
 * @param isReverse
 * @param includeLastMonth
 */
const dtBuildFyList = (lastYear?: number, length?: number, lowestFirst?: boolean): DefaultOptionType[] => {
  const kv = dtBuildFyKv(lastYear, length);
  return Object.keys(kv)
    .sort((a, b) => (a < b ? 1 : -1) * (lowestFirst ? -1 : 1))
    .map((y) => ({
      value: +y,
      label: kv[`${y}`],
    }));
};

export const getLexOfficeLink = (type?: string, id?: string) => {
  let subLink = "invoices";
  if (type == "creditnote") subLink = "credit-notes";
  else if (type == "order_confirmation") subLink = "order-confirmations";
  else if (type == "quotation") subLink = "quotations";
  return `https://app.lexoffice.de/permalink/${subLink}/view/${id}`;
};

export const urlLODocumentOnSystem = (order_no?: number, id?: string) => {
  return `${API_URL}/data/lo-invoices/${order_no}-${id}.pdf`;
};

export const urlFull = (route?: string, withoutOrigin?: boolean) => {
  /* const routerBase = (window as any).routerBase;
  return (
    (withoutOrigin ? '' : window.location.origin) +
    routerBase +
    (route[0] == '/' ? route.substring(1) : route)
  ); */

  return `${withoutOrigin ? "" : window.location.origin}${PUBLIC_PATH}${route || ""}`;
};

export const kvToOptions = (kv: Record<string | number, any>, getLabel?: any): DefaultOptionType[] => {
  return Object.keys(kv).map((k) => ({
    value: k,
    label: getLabel ? getLabel(k) : kv[k],
  }));
};

export const getFileExt = (fileName: string) => {
  const arr = fileName.split(".");

  return arr.length ? arr[arr.length - 1].toLowerCase() : "";
};

export const isImageFile = (fileName: string) => {
  const ext = getFileExt(fileName);
  return ext === "png" || ext === "jpg" || ext === "jpeg" || ext === "bmp" || ext === "gif";
};

export const isPdfFile = (fileName: string) => {
  const ext = getFileExt(fileName);
  return ext === "pdf";
};

/* const flashSysMsg = (resData: API.BaseResult) => {
  const flashMsg = resData?.messageFlash;
  if (flashMsg) {
    for (const key in flashMsg) {
      const msgStr = flashMsg[key].join('\n');
      if (key == 'w') {
        // message.warning(msgStr, 10);
        notification.warning({ message: null, description: msgStr, duration: 0, placement: 'top' });
      } else if (key == 'e') {
        // message.error(msgStr, 10);
        notification.error({ message: null, description: msgStr, duration: 0, placement: 'top' });
      } else if (key == 's') {
        notifySuccess(msgStr, 10);
      } else if (key == 'i') {
        // message.info(msgStr, 10);
        notification.error({ message: null, description: msgStr, duration: 0, placement: 'top' });
      }
    }
  }
}; */

/**
 * Format bytes as human-readable text.
 *
 * @param bytes Number of bytes.
 * @param si True to use metric (SI) units, aka powers of 1000. False to use
 *           binary (IEC), aka powers of 1024.
 * @param dp Number of decimal places to display.
 *
 * @return Formatted string.
 */
function humanFileSize(bytes: number, si = true, dp = 1) {
  // const thresh = si ? 1000 : 1024;
  const thresh = si ? 1024 : 1000;

  if (Math.abs(bytes) < thresh) {
    return bytes + " B";
  }

  const units = si ? ["KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"] : ["KiB", "MiB", "GiB", "TiB", "PiB", "EiB", "ZiB", "YiB"];
  let u = -1;
  const r = 10 ** dp;

  do {
    // eslint-disable-next-line no-param-reassign
    bytes /= thresh;
    ++u;
  } while (Math.round(Math.abs(bytes) * r) / r >= thresh && u < units.length - 1);

  return bytes.toFixed(dp) + " " + units[u];
}

function removeTags(str?: any) {
  if (str === null || str === "" || !str) return "";

  // Regular expression to identify HTML tags in
  // the input string. Replacing the identified
  // HTML tag with a null string.
  return str.toString().replace(/(<([^>]+)>)/gi, "");
}

function stripTags(html?: any) {
  let doc = new DOMParser().parseFromString(html ?? "", "text/html");
  return doc.body.textContent || "";
}

const arrToStr = (arr: any[], sep?: string) => {
  let str = "";
  arr.forEach((x) => {
    if (x) {
      if (str) str += sep;
      str += x;
    }
  });
  return str;
};

export const sShortImportDbTableName = (str?: string, ellipse?: boolean) => {
  const shortName = str?.substring(18);
  return ellipse ? sEllipsed(shortName, 5, 6) : shortName;
};

export function generateAlphanumericArray() {
  const alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ".split("");
  const numeric = "0123456789".split("");
  return [...alphabet, ...numeric];
}

function emailParseStr(emailString: string) {
  const split = emailString.split(" <");
  let name, email;
  if (split.length > 1) {
    name = split[0].trim();
    email = split[1] ?? "";
    email = email.replaceAll(">", "").trim();
  } else {
    email = split[0].trim();
    name = "";
  }
  if (email) {
    return [email, name];
  } else return [];
}

function emailParseStrList(emailString: string) {
  const toArr = emailString.split(",");
  const validEmails: any[] = [];
  if (toArr?.length) {
    toArr.forEach((to) => {
      const emailArr = emailParseStr(to.trim());
      if (emailArr?.length) {
        validEmails.push(emailArr);
      }
    });
  }
  return validEmails;
}

function emailParseStrListKv(emailString: string) {
  const toArr = emailString.split(",");
  const validEmails: any = {};
  if (toArr?.length) {
    toArr.forEach((to) => {
      const emailArr = emailParseStr(to.trim());
      if (emailArr?.length) {
        validEmails[emailArr[0]] = emailArr;
      }
    });
  }
  return validEmails;
}

function emailBuildSender(email?: string, name?: string) {
  if (!email) return "";
  return name ? name + " <" + email + ">" : email;
}

export const getQueryParamInUrl = (url: string, key: string) => {
  const urlParams = new URL(url).searchParams;
  return urlParams.get(key);
};

const Util = {
  waitTime,
  safeInt,
  isZero,
  safeNumber,
  numberFormat,
  numberFormatEn,
  error,
  lsUpdate,
  lsGet,
  safeJsonParse,
  dtToDMY,
  dtToDMYHHMM,
  dtToHHMM,
  dtToDMYHHMMTz,
  dtBrowserTzOffset,
  dtToYMD,
  dtCurrentFy,
  dtBuildFyKv,
  dtBuildFyList,
  dtBuildRangesInFy,
  getBase64,
  isTabPressed,
  isEnterKey,
  fPrices,
  arrToStr,

  sCap,
  sFileName,
  inCsvStr,
  getSfValues,
  setSfValues,

  genNewKey,
  dtBuildRanges,

  mergeGSearch,
  isEmptyValueObject,
  // flashSysMsg,

  humanFileSize,
  removeTags,
  stripTags,
  numTo2Digits,
  isFirefox,

  // Email
  emailParseStr,
  emailParseStrList,
  emailParseStrListKv,
  emailBuildSender,
};
export default Util;

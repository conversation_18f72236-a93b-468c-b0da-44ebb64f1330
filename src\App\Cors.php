<?php

declare(strict_types=1);

use Slim\App;

return static function (App $app): void {
    // Cors middleware
    $corsOptions = array(
        "origin" => explode(',', $_SERVER['CORS_HOSTS']),
        // "exposeHeaders" => array("X-Requested-With", "Content-Type", "Authorization"),
        // "maxAge" => 1728000,
        "maxAge" => 86400 * 20, // prod
        // "maxAge" => 0,
        // "allowCredentials" => True,
        "allowHeaders" => ["X-Requested-With", "Content-Type", "Accept", "Origin", "Authorization", "X-API-Key", "AccessToken"],
        "allowMethods" => ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
    );
    $app->add(new \CorsSlim\CorsSlim($corsOptions));
};

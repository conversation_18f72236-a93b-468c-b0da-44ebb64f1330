<?php

declare(strict_types=1);

namespace App\Controller\File;

use App\Models\File;
use Slim\Http\Request;
use Slim\Http\Response;

final class GetOne extends Base
{
    public function __invoke(Request $request, Response $response, array $args): Response
    {
        $id = intval($args['id'] ?? null);
        if (!$id) {
            \App\Exception\Base::raiseInvalidRequest('Order No is required!');
        }

        $data = File::findOrFail($id);

        return $this->jsonResponse($response, 'success', $data, 200);
    }
}

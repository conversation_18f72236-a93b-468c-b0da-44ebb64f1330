<?php

declare(strict_types=1);

namespace App\Controller\Email\Email;

use App\Lib\FuncModel;
use App\Models\Sys\Dict;
use Slim\Http\Request;
use Slim\Http\Response;

final class Update extends Base
{
    public function __invoke(Request $request, Response $response, array $args): Response
    {
        $id = $args['id'] ?? null;
        if (!$id) {
            \App\Exception\Base::raiseNotFound('Email');
        }

        $input = $request->getParsedBody();
        $action = $input['action'] ?? null;
        if ($action === 'noActionNeeded') {
            $input = ['status' => FuncModel::getDictValue(Dict::CODE_EMAIL_STATUS_NO_ACTION)];
        }

		$row = $this->emailService->update($id, $input);

        return $this->jsonResponse($response, 'success', $row, 200);
    }
}

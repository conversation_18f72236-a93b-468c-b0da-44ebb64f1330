<?php


namespace App\Lib;

use App\Models\File;
use JetBrains\PhpStorm\ArrayShape;
use Mpdf\Mpdf;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Reader\Csv;
use PhpOffice\PhpSpreadsheet\Reader\IReader;
use PhpOffice\PhpSpreadsheet\Reader\IReadFilter;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use setasign\Fpdi\PdfReader\PageBoundaries;

final class FileLib
{
    const AVATAR_PATH = '/avatar';
    const VIDEO_PATH = '/video';
    const ORIGINAL_PATH = '/original';  // 800 x 800
    const SMALL_PATH = '/small';       // 370 x 270
    const THUMB_PATH = '/thumb';        // 50 x 50

    const LEVEL_IMG_EXT = '.png';

    // File dimensions
    const ORIGINAL_DIM_X = 800;
    const ORIGINAL_DIM_Y = 800;
    const SMALL_DIM_X = 370;
    const SMALL_DIM_Y = 370;
    const THUMB_DIM_X = 50;
    const THUMB_DIM_Y = 50;

    const MPDF_DEFAULT_OPTION = [
        'mode' => '',
        'format' => 'A4',
        'default_font' => '',
        'margin_left' => 8,
        'margin_right' => 8,
        'margin_top' => 12,
        'margin_bottom' => 2,
        'margin_header' => 2,
        // 'margin_footer' => 1,
        'margin_footer' => 3,
        'orientation' => 'P',
    ];

    const MPDF_PICKLIST_OPTION = [
        'mode' => '',
        'format' => 'A4',
        'default_font' => '',
        'margin_left' => 6,
        'margin_right' => 6,
        'margin_top' => 12,
        'margin_bottom' => 2,
        'margin_header' => 2,
        // 'margin_footer' => 1,
        'margin_footer' => 3,
        'orientation' => 'P',
    ];

    protected $basePath;

    public function __construct($basePath)
    {
        $this->basePath = $basePath;
    }

    public function getCleanName($name)
    {
        if (!$name) return '';
        // remove illegal file system characters https://en.wikipedia.org/wiki/Filename#Reserved_characters_and_words
        $name = str_replace(array_merge(
            array_map('chr', range(0, 31)),
            array('<', '>', ':', '"', '/', '\\', '|', '?', '*')
        ), '', $name);
        // maximise filename length to 255 bytes http://serverfault.com/a/9548/44086
        $ext = pathinfo($name, PATHINFO_EXTENSION);
        $name = mb_strcut(pathinfo($name, PATHINFO_FILENAME), 0, 255 - ($ext ? strlen($ext) + 1 : 0), mb_detect_encoding($name)) . ($ext ? '.' . $ext : '');
        return $name;
    }

    /**
     * Return directory or file path
     *
     * @param null $fileName
     * @param bool $isRelative
     * @return string
     */
    public function getOriginalFilePath($fileName = null, $isRelative = true)
    {
        $path = ($isRelative ? '' : $this->basePath) . self::ORIGINAL_PATH;
        return $fileName ? $path . $fileName : $path;
    }

    public function getThumbFilePath($fileName = null, $isRelative = true)
    {
        $path = ($isRelative ? '' : $this->basePath) . self::THUMB_PATH;
        return $fileName ? $path . $fileName : $path;
    }

    public function getSmallFilePath($fileName = null, $isRelative = true)
    {
        $path = ($isRelative ? '' : $this->basePath) . self::SMALL_PATH;
        return $fileName ? $path . $fileName : $path;
    }

    public function getFullPath($path)
    {
        return $this->basePath . $path;
    }

    public function getVideoFilePath($fileName = null, $isRelative = true)
    {
        $path = ($isRelative ? '' : $this->basePath) . self::VIDEO_PATH;
        return $fileName ? $path . $fileName : $path;
    }

    public function getAvatarFilePath($fileName = null, $isRelative = true)
    {
        $path = ($isRelative ? '' : $this->basePath) . self::AVATAR_PATH;
        return $fileName ? $path . $fileName : $path;
    }

    public function getEANFileName($itemId, $eanId, $type): string
    {
        return $itemId . '_' . $eanId . '_' . uniqid() . '.' . $this->mime2ext($type);
    }

    public function getUniqueFileName($type, $prefix = ''): string
    {
        return $prefix . uniqid() . '.' . $this->mime2ext($type);
    }

    public function getUniqueFileNameWithThumb($address, $type)
    {
        $str = uniqid();
        $extension = $this->mime2ext($type);
        return [
            $address . '_' . $str . '.' . $extension,
            $address . '_' . $str . '_thumb.' . $extension,
        ];
    }

    public function mime2ext($mime, $reverse = false)
    {
        $mime_map = [
            'video/3gpp2' => '3g2',
            'video/3gp' => '3gp',
            'video/3gpp' => '3gp',
            'application/x-compressed' => '7zip',
            'audio/x-acc' => 'aac',
            'audio/ac3' => 'ac3',
            'application/postscript' => 'ai',
            'audio/x-aiff' => 'aif',
            'audio/aiff' => 'aif',
            'audio/x-au' => 'au',
            'video/x-msvideo' => 'avi',
            'video/msvideo' => 'avi',
            'video/avi' => 'avi',
            'application/x-troff-msvideo' => 'avi',
            'application/macbinary' => 'bin',
            'application/mac-binary' => 'bin',
            'application/x-binary' => 'bin',
            'application/x-macbinary' => 'bin',
            'image/bmp' => 'bmp',
            'image/x-bmp' => 'bmp',
            'image/x-bitmap' => 'bmp',
            'image/x-xbitmap' => 'bmp',
            'image/x-win-bitmap' => 'bmp',
            'image/x-windows-bmp' => 'bmp',
            'image/ms-bmp' => 'bmp',
            'image/x-ms-bmp' => 'bmp',
            'application/bmp' => 'bmp',
            'application/x-bmp' => 'bmp',
            'application/x-win-bitmap' => 'bmp',
            'application/cdr' => 'cdr',
            'application/coreldraw' => 'cdr',
            'application/x-cdr' => 'cdr',
            'application/x-coreldraw' => 'cdr',
            'image/cdr' => 'cdr',
            'image/x-cdr' => 'cdr',
            'zz-application/zz-winassoc-cdr' => 'cdr',
            'application/mac-compactpro' => 'cpt',
            'application/pkix-crl' => 'crl',
            'application/pkcs-crl' => 'crl',
            'application/x-x509-ca-cert' => 'crt',
            'application/pkix-cert' => 'crt',
            'text/css' => 'css',
            'text/csv' => 'csv',
            'text/x-comma-separated-values' => 'csv',
            'text/comma-separated-values' => 'csv',
            'application/vnd.msexcel' => 'csv',
            'application/x-director' => 'dcr',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document' => 'docx',
            'application/x-dvi' => 'dvi',
            'message/rfc822' => 'eml',
            'application/x-msdownload' => 'exe',
            'video/x-f4v' => 'f4v',
            'audio/x-flac' => 'flac',
            'video/x-flv' => 'flv',
            'image/gif' => 'gif',
            'application/gpg-keys' => 'gpg',
            'application/x-gtar' => 'gtar',
            'application/x-gzip' => 'gzip',
            'application/mac-binhex40' => 'hqx',
            'application/mac-binhex' => 'hqx',
            'application/x-binhex40' => 'hqx',
            'application/x-mac-binhex40' => 'hqx',
            'text/html' => 'html',
            'image/x-icon' => 'ico',
            'image/x-ico' => 'ico',
            'image/vnd.microsoft.icon' => 'ico',
            'text/calendar' => 'ics',
            'application/java-archive' => 'jar',
            'application/x-java-application' => 'jar',
            'application/x-jar' => 'jar',
            'image/jp2' => 'jp2',
            'video/mj2' => 'jp2',
            'image/jpx' => 'jp2',
            'image/jpm' => 'jp2',
            'image/jpeg' => 'jpeg',
            'image/pjpeg' => 'jpeg',
            'application/x-javascript' => 'js',
            'application/json' => 'json',
            'text/json' => 'json',
            'application/vnd.google-earth.kml+xml' => 'kml',
            'application/vnd.google-earth.kmz' => 'kmz',
            'text/x-log' => 'log',
            'audio/x-m4a' => 'm4a',
            'audio/mp4' => 'm4a',
            'application/vnd.mpegurl' => 'm4u',
            'audio/midi' => 'mid',
            'application/vnd.mif' => 'mif',
            'video/quicktime' => 'mov',
            'video/x-sgi-movie' => 'movie',
            'audio/mpeg' => 'mp3',
            'audio/mpg' => 'mp3',
            'audio/mpeg3' => 'mp3',
            'audio/mp3' => 'mp3',
            'video/mp4' => 'mp4',
            'video/mpeg' => 'mpeg',
            'application/oda' => 'oda',
            'audio/ogg' => 'ogg',
            'video/ogg' => 'ogg',
            'application/ogg' => 'ogg',
            'font/otf' => 'otf',
            'application/x-pkcs10' => 'p10',
            'application/pkcs10' => 'p10',
            'application/x-pkcs12' => 'p12',
            'application/x-pkcs7-signature' => 'p7a',
            'application/pkcs7-mime' => 'p7c',
            'application/x-pkcs7-mime' => 'p7c',
            'application/x-pkcs7-certreqresp' => 'p7r',
            'application/pkcs7-signature' => 'p7s',
            'application/pdf' => 'pdf',
            'application/octet-stream' => 'pdf',
            'application/x-x509-user-cert' => 'pem',
            'application/x-pem-file' => 'pem',
            'application/pgp' => 'pgp',
            'application/x-httpd-php' => 'php',
            'application/php' => 'php',
            'application/x-php' => 'php',
            'text/php' => 'php',
            'text/x-php' => 'php',
            'application/x-httpd-php-source' => 'php',
            'image/png' => 'png',
            'image/x-png' => 'png',
            'application/powerpoint' => 'ppt',
            'application/vnd.ms-powerpoint' => 'ppt',
            'application/vnd.ms-office' => 'ppt',
            'application/msword' => 'doc',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation' => 'pptx',
            'application/x-photoshop' => 'psd',
            'image/vnd.adobe.photoshop' => 'psd',
            'audio/x-realaudio' => 'ra',
            'audio/x-pn-realaudio' => 'ram',
            'application/x-rar' => 'rar',
            'application/rar' => 'rar',
            'application/x-rar-compressed' => 'rar',
            'audio/x-pn-realaudio-plugin' => 'rpm',
            'application/x-pkcs7' => 'rsa',
            'text/rtf' => 'rtf',
            'text/richtext' => 'rtx',
            'video/vnd.rn-realvideo' => 'rv',
            'application/x-stuffit' => 'sit',
            'application/smil' => 'smil',
            'text/srt' => 'srt',
            'image/svg+xml' => 'svg',
            'application/x-shockwave-flash' => 'swf',
            'application/x-tar' => 'tar',
            'application/x-gzip-compressed' => 'tgz',
            'image/tiff' => 'tiff',
            'font/ttf' => 'ttf',
            'text/plain' => 'txt',
            'text/x-vcard' => 'vcf',
            'application/videolan' => 'vlc',
            'text/vtt' => 'vtt',
            'audio/x-wav' => 'wav',
            'audio/wave' => 'wav',
            'audio/wav' => 'wav',
            'application/wbxml' => 'wbxml',
            'video/webm' => 'webm',
            'image/webp' => 'webp',
            'audio/x-ms-wma' => 'wma',
            'application/wmlc' => 'wmlc',
            'video/x-ms-wmv' => 'wmv',
            'video/x-ms-asf' => 'wmv',
            'font/woff' => 'woff',
            'font/woff2' => 'woff2',
            'application/xhtml+xml' => 'xhtml',
            'application/excel' => 'xl',
            'application/msexcel' => 'xls',
            'application/x-msexcel' => 'xls',
            'application/x-ms-excel' => 'xls',
            'application/x-excel' => 'xls',
            'application/x-dos_ms_excel' => 'xls',
            'application/xls' => 'xls',
            'application/x-xls' => 'xls',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' => 'xlsx',
            'application/vnd.ms-excel' => 'xls',
            'application/xml' => 'xml',
            'text/xml' => 'xml',
            'text/xsl' => 'xsl',
            'application/xspf+xml' => 'xspf',
            'application/x-compress' => 'z',
            'application/x-zip' => 'zip',
            'application/zip' => 'zip',
            'application/x-zip-compressed' => 'zip',
            'application/s-compressed' => 'zip',
            'multipart/x-zip' => 'zip',
            'text/x-scriptzsh' => 'zsh',
        ];

        if ($reverse) {
            foreach ($mime_map as $key => $value) {
                if ($value == $mime) {
                    return $key;
                }
            }
        } else {
            return isset($mime_map[$mime]) ? $mime_map[$mime] : false;
        }
    }

    public function getBase64EncodedImage($path)
    {
        // $type = pathinfo($path, PATHINFO_EXTENSION);
        $data = file_get_contents($path);
        // $base64 = 'data:image/' . $type . ';base64,' . base64_encode($data);
        $base64 = base64_encode($data);
        return $base64;
    }

    public function readSpreadsheet(File $file): Spreadsheet
    {
        $extension = $this->mime2ext($file->type);
        $objReader = IOFactory::createReader(ucfirst($extension));
        $objReader->setReadDataOnly(true);
        $objPHPExcel = $objReader->load($this->getFullPath($file->org_path));

        return $objPHPExcel;
    }

    public function readSpreadsheetByFileName(File $file, $options = []): Spreadsheet
    {
        $filePath = $this->getFullPath($file->org_path);
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        if ($extension == 'txt') {
            $extension = 'csv';
        }

        $objReader = IOFactory::createReader(ucfirst($extension));
        if (/*$extension == 'csv'*/ $objReader instanceof Csv) {
            $objReader->setDelimiter($options['delimiter'] ?? ';');
        }
        $objReader->setReadDataOnly(true);
        $objPHPExcel = $objReader->load($this->getFullPath($file->org_path));

        return $objPHPExcel;
    }

    public function checkOrCreateDir($path, $isRelativePath = true)
    {
        if ($isRelativePath) {
            $path = $this->getFullPath($path);
        }
        if (!file_exists($path)) {
            mkdir($path, 0755, true);
        }
    }

    /**
     * Download a file.
     *
     * @param $filePath
     * @param $params
     * @return void
     * @throws \Exception
     */
    public static function downloadFile($filePath, $params=[])
    {
        $force = $params['force'] ?? null;

        if (file_exists($filePath)) {
            $tmpFileLib = new FileLib('');
            $mime = $tmpFileLib->mime2ext('type', true);

            $info = pathinfo($filePath);
            $filename = $info['basename'];
            $x = explode('.', $filename);
            $extension = end($x);

            $filesize = @filesize($filePath);

            /*if ($set_mime === TRUE) {
                if (count($x) === 1 or $extension === '') {
                    /* If we're going to detect the MIME type,
                     * we'll need a file extension.
                     * /
                    return;
                }
            }*/

            /* It was reported that browsers on Android 2.1 (and possibly older as well)
             * need to have the filename extension upper-cased in order to be able to
             * download it.
             *
             * Reference: http://digiblog.de/2011/04/19/android-and-the-download-file-headers/
             */
            if (count($x) !== 1 && isset($_SERVER['HTTP_USER_AGENT']) && preg_match('/Android\s(1|2\.[01])/', $_SERVER['HTTP_USER_AGENT'])) {
                $x[count($x) - 1] = strtoupper($extension);
                $filename = implode('.', $x);
            }

            if (($fp = @fopen($filePath, 'rb')) === FALSE) {
                \App\Exception\Base::raiseInvalidRequest('Internal server error while opening file.', 500);
            }

            // Clean output buffer
            if (ob_get_level() !== 0 && @ob_end_clean() === FALSE) {
                @ob_clean();
            }

            // Generate the server headers
            header('Content-Type: ' . $mime);
            if ($force)
                header('Content-Disposition: attachment; filename="' . $filename . '"');
            else
                header('Content-Disposition: inline; filename="' . $filename . '"');
            header('Expires: 0');
            header('Content-Transfer-Encoding: binary');
            header('Content-Length: ' . $filesize);
            header('Cache-Control: private, no-transform, no-store, must-revalidate');

            header("Access-Control-Allow-Origin: *");

            // Flush 1MB chunks of data
            while (!feof($fp) && ($data = fread($fp, 1048576)) !== FALSE) {
                echo $data;
            }

            fclose($fp);

            exit;
        }
    }

    /**
     * @param string $type fileType e.g. csv,pdf
     * @param string $pathKey relative path
     * @return array
     */
    #[ArrayShape(['type' => "", 'key' => "", 'url' => "string"])]
    public static function getDownloadableFileObject(string $type, string $pathKey, $force = false): array
    {
        $rawUrl = "download?type=$type&key=" . urlencode($pathKey);
        if ($force) {
            $rawUrl .= "&force=1";
        } else {
            if ($type == 'csv') {
                $rawUrl .= "&force=1";
            }
        }
        $result = [
            'type' => $type,
            'key' => $pathKey,
            'url' => $rawUrl,
        ];

        return $result;
    }

    public static function checkAndCreateDir($path)
    {
        if (!file_exists($path)) {
            \Safe\mkdir($path, 0755, true);
        }
    }

    static function mergePDFFiles(array $filenames, string $outFile): void
    {
        if ($filenames) {
            $defaultConfig = [
                'mode' => '',
                'format' => [105, 202],
                'default_font_size' => 0,
                'default_font' => '',
                'margin_left' => 0,
                'margin_right' => 0,
                'margin_top' => 0,
                'margin_bottom' => 0,
                'margin_header' => 0,
                'margin_footer' => 0,
                'orientation' => 'P',
            ];
            $mpdf = new mPDF($defaultConfig);

            $filesTotal = sizeof($filenames);
            $fileNumber = 1;
//            $mpdf->SetImportUse();
            if (!file_exists($outFile)) {
                $handle = fopen($outFile, 'w');
                fclose($handle);
            }
            foreach ($filenames as $fileName) {
                if (file_exists($fileName)) {
                    $pagesInFile = $mpdf->SetSourceFile($fileName);
                    for ($i = 1; $i <= $pagesInFile; $i++) {
                        $tplId = $mpdf->ImportPage($i);
//                        $arrSize = $mpdf->getTemplateSize($tplId);
                        $mpdf->UseTemplate($tplId, 0, 0, null, null, true);
                        if (($fileNumber < $filesTotal) || ($i != $pagesInFile)) {
                            $mpdf->WriteHTML('<pagebreak />');
                        }
                    }
                }
                $fileNumber++;
            }
            $mpdf->Output($outFile);
        }
    }

    public static function deleteOldFiles($path, $hours = 72)
    {
        if ($handle = opendir($path)) {
            while (false !== ($file = readdir($handle))) {
                $fileLastModified = filemtime($path . $file);
                if ((time() - $fileLastModified) > $hours * 3600) {
                    unlink($path . $file);
                }

            }
            closedir($handle);
        }
    }

    public static function resizePdfFile($basePath, $fileName) {
        $fileNameOnly = Func::getFileNames($fileName, false);

        $newFileName =  $fileNameOnly . '_small.pdf';
        file_put_contents($basePath.$newFileName, base64_decode(base64_encode(file_get_contents($basePath . $fileName)), true));

        /*$mpdf = new Mpdf();

        // Bug
        // $mpdf->SetImportUse();
        $pagesInFile = $mpdf->SetSourceFile($basePath . $fileName);
        for ($i = 1; $i <= $pagesInFile; $i++) {
            $tplId = $mpdf->ImportPage($i);
             $arrSize = $mpdf->getTemplateSize($tplId);

            $mpdf->UseTemplate($tplId, 0, 0, 100, 150, true);
            if (($i != $pagesInFile)) {
                $mpdf->WriteHTML('<pagebreak />');
            }
        }

        $newFileName =  $fileNameOnly . '_small.pdf';
        $mpdf->Output($basePath . $newFileName);*/
    }
}

class ReadFilter implements IReadFilter
{
    private $_startRow = 0;

    private $_endRow = 0;

    private $_columns = array();
    private $_filters = array();
    private Worksheet $sheet;

    public function __construct($filters)
    {
        $this->_filters = $filters;
    }

    public function setSheet($sheet)
    {
        $this->sheet = $sheet;
    }

    public function readCell($column, $row, $worksheetName = '')
    {
        if ($row >= $this->_startRow && $row <= $this->_endRow) {
            if (in_array($column, $this->_columns)) {
                return true;
            }
        }
        return false;
    }


}

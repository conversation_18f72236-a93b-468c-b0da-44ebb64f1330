<?php


namespace App\Service\Offer\OrgOffer;

use App\ModelsOrg\OrgOffer;
use App\Repository\Supplier\SupplierRepository;
use App\Repository\UserRepository;
use App\Service\BaseService;
use Slim\Container;

class OrgOfferService extends BaseService
{
    public UserRepository $userRepository;

    public function __construct(Container $container)
    {
        $this->userRepository = $container->get(UserRepository::class);
    }

    public function getOrgOfferQuery($params)
    {
        $query = OrgOffer::query();

        // Filter by supp_supplier_id through supplier relationship
        if (!empty($params['supp_supplier_id'])) {
            $query->whereHas('supplier', function ($q) use ($params) {
                $q->where('supp_supplier_id', $params['supp_supplier_id']);
            });
        }

        // Apply other filters
        if (!empty($params['offer_sid'])) {
            $query->where('offer_sid', 'like', '%' . $params['offer_sid'] . '%');
        }

        if (!empty($params['status'])) {
            $query->where('status', $params['status']);
        }

        // Apply relationships
        if (!empty($params['with'])) {
            $withParams = explode(',', $params['with']);
            if (in_array('supplier', $withParams)) {
                $query->with('supplier');
            }
        }


        // Apply sorting
        if (!empty($params['sort'])) {
            foreach ($params['sort'] as $field => $direction) {
                $query->orderBy($field, $direction === 'ascend' ? 'asc' : 'desc');
            }
        } else {
            $query->orderBy('offer_sid', 'desc');
        }

        return $query;
    }

    public function getOrgOffersByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($perPage < 1) {
            $perPage = self::DEFAULT_PER_PAGE_PAGINATION;
        }

        $query = $this->getOrgOfferQuery($params);

        $total = $query->count();

        $result = $this->userRepository->getResultsWithPagination($query, $page, $perPage, $total);

        return $result;
    }
}
import SProFormDigit from "@/components/SProFormDigit";
import { deleteFile } from "@/services/app/File/file";
import {
  copyFileAndResize,
  deleteOfferNewsletter,
  getOfferNewsletterListByPage,
  updateOrCreateOfferNewsletterByOfferNo,
  usOfferNewsletterToWhcCust,
} from "@/services/app/Offer/offer-newsletter";
import Util, { sn } from "@/util";
import { ProColumns, ProFormInstance, ProFormUploadDragger, EditableProTable } from "@ant-design/pro-components";
import { Button, message, Modal, Space, Spin, Image, Popconfirm } from "antd";
import { RcFile } from "antd/es/upload";
import { useCallback, useEffect, useRef, useState } from "react";
import styles from "./OfferNewsletterUpdateForm.less";
import { DeleteOutlined, FullscreenExitOutlined } from "@ant-design/icons";

type FormValueType = API.OfferNewsletter;
type RecordType = API.OfferNewsletter & { uid?: string };

export type OfferNewsletterUpdateFormProps = {
  offer_no: string;
  orgOffer: Partial<APIOrg.Offer>;
  onSubmit?: (formData: FormValueType) => void;
  reloadTick?: number;
  loadOrgOfferDetail?: () => void;
};

const OfferNewsletterUpdateForm: React.FC<OfferNewsletterUpdateFormProps> = ({ offer_no, orgOffer, onSubmit, reloadTick, loadOrgOfferDetail }) => {
  const [loading, setLoading] = useState<boolean>(false);
  const editableFormRef = useRef<ProFormInstance>();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [dataSource, setDataSource] = useState<RecordType[]>([]);

  const loadOfferNewsletters = useCallback(() => {
    setLoading(true);
    getOfferNewsletterListByPage({ offer_no, with: "files" })
      .then((res) => {
        if (res.data.length) {
          const newDs = res.data.map((x) => ({
            ...x,
            uid: `${x.id}`,
          }));

          setDataSource(newDs);
          setEditableRowKeys(newDs.map((x) => `${x.uid}`));
        } else {
          setDataSource([]);
          setEditableRowKeys([]);
        }
      })
      .catch(Util.error)
      .finally(() => setLoading(false));
  }, [offer_no]);

  useEffect(() => {
    loadOfferNewsletters();
  }, [loadOfferNewsletters]);

  useEffect(() => {
    if (reloadTick) {
      loadOfferNewsletters();
    }
  }, [reloadTick, loadOfferNewsletters]);

  // Define columns for the ProEditableTable
  const columns: ProColumns[] = [
    {
      dataIndex: "id",
      key: "id",
      width: 20,
      hideInTable: true,
    },
    {
      title: "Product",
      dataIndex: "product_title",
      key: "product_title",
      width: 300,
      formItemProps: {
        rules: [
          {
            required: true,
            message: "Newsletter title is required",
          },
        ],
      },
    },
    {
      title: "Pcs / Case",
      dataIndex: "case_qty",
      key: "case_qty",
      width: 100,
      valueType: "digit",
      formItemProps: {
        style: { marginBottom: 0 },
      },
      fieldProps: { precision: 0 },
      renderFormItem(schema, config, form, action) {
        return <SProFormDigit allowClear formItemProps={{ ...schema.formItemProps }} />;
      },
    },
    {
      title: "Cases / Pallet",
      dataIndex: "ve_pallet",
      key: "ve_pallet",
      width: 100,
      valueType: "digit",
      formItemProps: {
        style: { marginBottom: 0 },
      },
      fieldProps: { precision: 0 },
      renderFormItem(schema, config, form, action) {
        return <SProFormDigit allowClear formItemProps={{ ...schema.formItemProps }} />;
      },
    },
    {
      title: "Price",
      dataIndex: "price",
      key: "price",
      width: 100,
      valueType: "digit",
      formItemProps: {
        style: { marginBottom: 0 },
      },
      renderFormItem(schema, config, form, action) {
        return <SProFormDigit allowClear formItemProps={{ ...schema.formItemProps }} fieldProps={{ precision: 3 }} />;
      },
    },
    {
      title: "Pic",
      dataIndex: "files",
      key: "files",
      width: 530,
      editable: false,
      render(dom, entity) {
        return (
          <ProFormUploadDragger
            description="Please select newsletter files or drag & drop"
            wrapperCol={{ span: 24 }}
            accept="image/*"
            title={false}
            style={{ display: "flex" }}
            formItemProps={{ style: { marginBottom: 0 } }}
            fieldProps={{
              multiple: true,
              listType: "picture",
              name: "files",
              fileList: entity.files ?? [],
              style: { marginBottom: 0 },
              height: 120,
              beforeUpload: (file: RcFile, fileList: RcFile[]) => {
                console.log("beforeUpload", file, fileList);
                setDataSource((prev) => {
                  const newDs = [...prev];

                  const found = newDs.find((x) => x.uid == entity.uid);
                  if (found) {
                    found.files = [...(found.files || []), file];
                  }

                  return newDs;
                });
                return false;
              },
              onRemove: async (file: API.File | RcFile) => {
                console.log("[onRemove]", file);
                if ((file as API.File).id) {
                  const { confirm } = Modal;
                  return new Promise((resolve, reject) => {
                    confirm({
                      title: "Are you sure you want to delete?",
                      onOk: async () => {
                        resolve(true);
                        const hide = message.loading(`Deleting a file '${(file as API.File).file_name}'.`, 0);
                        const res = await deleteFile(sn((file as API.File).id));
                        hide();
                        if (res) {
                          // loadOfferNewsletters();
                          setDataSource((prev) => {
                            const newDs = [...prev];
                            const found = newDs.find((x) => x.uid == entity.uid);
                            if (found) {
                              found.files = found.files?.filter((x) => x.uid != file.uid);
                            }
                            return newDs;
                          });

                          message.success(`Deleted successfully!`);
                        } else {
                          Util.error(`Delete failed, please try again!`);
                        }

                        return res;
                      },
                      onCancel: () => {
                        reject(true);
                      },
                    });
                  });
                } else {
                  setDataSource((prev) => {
                    const newDs = [...prev];
                    const found = newDs.find((x) => x.uid == entity.uid);
                    if (found) {
                      found.files = found.files?.filter((x) => x.uid != file.uid);
                    }
                    return newDs;
                  });
                  return true;
                }
              },

              itemRender: (originNode: React.ReactElement, file, fileList, actions) => {
                return (
                  <Space direction="vertical" size={6}>
                    <Image
                      src={(file as API.File).id ? (file as API.File).thumb_url : file.thumbUrl}
                      preview={{
                        src: file.url,
                      }}
                      width={80}
                      height={80}
                    />
                    <Space size={12} style={{ justifyContent: "center", width: "100%" }}>
                      {!!(file as API.File).id && (
                        <Popconfirm
                          title={<>Are you sure you want to duplicate this image by 80 x 80 px?</>}
                          okText="Yes"
                          cancelText="No"
                          styles={{ root: { maxWidth: 300 } }}
                          onConfirm={() => {
                            const hide = message.loading("Creating a new file with 80x80...", 0);
                            copyFileAndResize(offer_no, sn((file as API.File).id))
                              .then((res) => {
                                hide();
                                message.success("Created successfully.");
                                loadOfferNewsletters();
                                loadOrgOfferDetail?.();
                              })
                              .catch(Util.error)
                              .finally(() => {
                                hide();
                              });
                          }}
                        >
                          <Button size="small" variant="outlined" color="blue" icon={<FullscreenExitOutlined />} title="Resize" />
                        </Popconfirm>
                      )}
                      <Button size="small" variant="outlined" color="danger" icon={<DeleteOutlined />} title="Delete" onClick={actions.remove} />
                    </Space>
                  </Space>
                );
              },
            }}
          />
        );
      },
    },
    {
      title: "Actions",
      valueType: "option",
      width: 80,
      render() {
        return null;
      },
    },
  ];

  const newsletter_dt = orgOffer?.ext?.details?.us_newsletter_dt;

  return (
    <div className={styles.offerNewsletterUpdateForm}>
      <Spin spinning={loading} style={{ width: "100%" }}>
        <Space direction="vertical" style={{ width: "100%" }}>
          <EditableProTable<RecordType>
            rowKey="uid"
            editableFormRef={editableFormRef}
            headerTitle={false}
            maxLength={50}
            recordCreatorProps={{
              newRecordType: "dataSource",
              record(index, dataSource) {
                const newNewsletter: RecordType = {
                  uid: "uid_" + Date.now().toString(), // Temporary ID for new items
                };

                return newNewsletter;
              },
            }}
            columns={columns}
            value={dataSource}
            onChange={setDataSource as any}
            controlled={true}
            editable={{
              type: "multiple",
              editableKeys,
              onChange: setEditableRowKeys,
              deletePopconfirmMessage: "Are you sure you want to delete?",
              onlyAddOneLineAlertMessage: "You can only add one.",
              actionRender: (row, config, dom) => [dom.delete],
              onValuesChange(record, recordList) {
                console.log("onValuesChange", record, recordList);
                const newRecordList = [...recordList];
                setDataSource(newRecordList);
              },
              onDelete(key, record) {
                if (`${key}`.startsWith("uid_")) {
                  return Promise.resolve(true);
                } else {
                  return deleteOfferNewsletter(key as number)
                    .then((res) => {
                      message.success("Deleted successfully.");
                      setDataSource((prev) => prev.filter((x) => x.uid != key));
                      loadOrgOfferDetail?.();
                    })
                    .catch(Util.error);
                }
              },
            }}
            pagination={{
              pageSize: 100,
              hideOnSinglePage: true,
              showSizeChanger: true,
              showQuickJumper: true,
            }}
            scroll={{ x: 1000 }}
            size="small"
          />
          <Space style={{ width: "100%", justifyContent: "flex-end" }} size={16}>
            <Button
              variant="filled"
              color="green"
              disabled={!(orgOffer.ext?.is_b_group_appr == 1 && (orgOffer.ext?.status == 1 || orgOffer.ext?.status == 2))}
              onClick={() => {
                Modal.confirm({
                  title: "Are you sure you want to upsync?",
                  content: "Make sure you already saved newsletter! Otherwise changes will be lost.",
                  onOk: () => {
                    const hide = message.loading("Up syncing newsletter data...", 0);
                    usOfferNewsletterToWhcCust({ offer_no })
                      .then((res) => {
                        loadOrgOfferDetail?.();
                        message.success("Up synced successfully.");
                      })
                      .catch(Util.error)
                      .finally(hide);
                  },
                });
              }}
              style={{ marginRight: 64 }}
            >
              Up Sync to WHC_Cust{newsletter_dt ? ` (${Util.dtToDMYHHMM(newsletter_dt)})` : ""}
            </Button>
            <Button
              type="default"
              onClick={() => {
                setDataSource((prev) => {
                  const newDs = [...prev];
                  if (newDs) {
                    newDs.forEach((x, ind) => {
                      newDs[ind].case_qty = null;
                      newDs[ind].ve_pallet = null;
                      newDs[ind].product_title = "";
                      newDs[ind].price = null;
                    });
                  }
                  return newDs;
                });
              }}
            >
              Reset
            </Button>
            <Button
              type="primary"
              onClick={() => {
                console.log("dataSource", dataSource);

                const data = new FormData();
                editableFormRef.current?.validateFields().then((res) => {
                  data.append(`offer_no`, offer_no);

                  dataSource.forEach((x, ind) => {
                    if (!x.uid) {
                      return;
                    }

                    data.append(`rows[${ind}][offer_no]`, offer_no);

                    if (x.id) {
                      data.append(`rows[${ind}][id]`, `${x.id}`);
                    }

                    if (x.product_title) {
                      data.append(`rows[${ind}][product_title]`, x.product_title);
                    }
                    if (x.case_qty) {
                      data.append(`rows[${ind}][case_qty]`, x.case_qty);
                    }
                    if (x.ve_pallet) {
                      data.append(`rows[${ind}][ve_pallet]`, x.ve_pallet);
                    }
                    if (x.price) {
                      data.append(`rows[${ind}][price]`, x.price);
                    }

                    if (x.files && x.files.length) {
                      console.log("files", x.files);
                      x.files.forEach((file: any, ind2: number) => {
                        if (!sn(file.uid)) {
                          data.append(`rows[${ind}][files][${ind2}]`, new Blob([file], { type: file.type }), file.name);
                        }
                      });
                    }
                  });

                  setLoading(true);
                  const hide = message.loading("Saving blog data...", 0);
                  updateOrCreateOfferNewsletterByOfferNo(data)
                    .then((res) => {
                      hide();
                      message.success("Saved successfully.");
                      loadOfferNewsletters();
                      if (onSubmit) onSubmit(res);
                      loadOrgOfferDetail?.();
                    })
                    .catch(Util.error)
                    .finally(() => {
                      hide();
                      setLoading(false);
                    });
                });
              }}
            >
              Save Newsletters
            </Button>
          </Space>
        </Space>
      </Spin>
    </div>
  );
};
export default OfferNewsletterUpdateForm;

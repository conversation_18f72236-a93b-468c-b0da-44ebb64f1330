<?php

declare(strict_types=1);

namespace App\Service\Offer\OfferBlog;

use App\Service\BaseService;
use App\Repository\Offer\OfferBlogRepository;
use Slim\Container;

abstract class Base extends BaseService
{
    private const REDIS_KEY = 'Offer:%s';
    protected OfferBlogRepository $offerBlogRepository;

    public function __construct(Container $container)
    {
        $this->offerBlogRepository = $container->get(OfferBlogRepository::class);
    }

    public function getOfferBlogRepository()
    {
        return $this->offerBlogRepository;
    }
}


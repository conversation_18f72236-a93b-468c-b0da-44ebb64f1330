create table customer
(
    id         int unsigned auto_increment comment 'PK: Customer ID' primary key,
    name       varchar(255)                                  not null,
    org_a      varchar(1)        default ''                  not null,
    org_b      int(5)            default 0                   not null,
    is_active  smallint unsigned default 1                   not null comment 'Is Active',
    created_on timestamp         default current_timestamp() not null comment 'Created At',
    created_by int                                           null,
    updated_on timestamp                                     null comment 'Updated At',
    updated_by int                                           null
) comment 'Customer' charset = utf8;

create index IDX_customer_name
    on customer (name);

create index IDX_customer_org_a
    on customer (org_a);

create index IDX_customer_org_b
    on customer (org_b);

create index IDX_customer_updated_on
    on customer (updated_on);


create table customer_address
(
    id          int unsigned auto_increment comment 'PK: Address ID'
        primary key,
    customer_id int unsigned                          null comment 'Customer ID.',
    company     varchar(255)                          null comment 'Company',
    country_id  varchar(255)                          not null comment 'Country',
    city        varchar(255)                          not null comment 'City',
    fax         varchar(255)                          null comment 'Fax',
    postcode    varchar(255)                          null comment 'Zip/Postal Code',
    region      varchar(255)                          null comment 'State/Province',
    region_id   int unsigned                          null comment 'State/Province',
    street1     varchar(255)                          not null comment 'Street Address',
    street2     varchar(255)                          null comment 'Street Address',
    telephone   varchar(255)                          null comment 'Phone Number',
    created_on  timestamp default current_timestamp() not null comment 'Created At',
    updated_on  timestamp default current_timestamp() not null on update current_timestamp() comment 'Updated At'
)
    comment 'Customer Address' charset = utf8;

create index IDX_customer_address_customer_id on customer_address (customer_id);

create table customer_contact
(
    id          int unsigned auto_increment comment 'PK: Contact ID'
        primary key,
    customer_id int unsigned                   null comment 'Customer ID.',
    department  varchar(255)                   null comment 'Job Title / Department',
    email       varchar(255)                   not null comment 'Email',
    is_active   smallint unsigned default 1    not null comment 'Is Active',
    is_default  smallint          default 0    null comment 'Is Default Contact?',
    firstname   varchar(255)                   not null comment 'First Name',
    lastname    varchar(255)                   not null comment 'Last Name',
    middlename  varchar(255)                   null comment 'Middle Name',
    prefix      varchar(40)                    null comment 'Name Prefix',
    suffix      varchar(40)                    null comment 'Name Suffix',
    telephone   varchar(255)                   null comment 'Phone Number',
    created_on  datetime                       null comment 'Created On',
    updated_on  datetime                       null comment 'Updated On',
    note        text                           null comment 'Note',
    lang        varchar(3)        default 'DE' null comment 'Lang: DE/EN',
    salutation  varchar(255)                   null comment 'Salutation',
    is_blocked  tinyint(1)        default 0    null,
    constraint FK_customer_contact_customer_id
        foreign key (customer_id) references customer (id)
            on update set null on delete cascade
)
    comment 'Customer Address' charset = utf8;

create index IDX_customer_contact_email
    on customer_contact (email);

create index IDX_customer_contact_firstname
    on customer_contact (firstname);

create index IDX_customer_contact_lastname
    on customer_contact (lastname);

create index IDX_customer_contact_customer_id
    on customer_contact (customer_id);



CREATE TABLE `customer_ext`
(
    `id`          int(10) unsigned NOT NULL AUTO_INCREMENT,
    `customer_id` int(10) unsigned DEFAULT NULL COMMENT 'Customer ID.',
    `note`        longtext         DEFAULT NULL,
    `user_id`     int(10) unsigned DEFAULT NULL COMMENT 'WHC User''s ID',
    `relevance`   smallint(6)      DEFAULT NULL,
    `created_on`  datetime         DEFAULT NULL,
    `created_by`  int(11)          DEFAULT NULL,
    `updated_on`  datetime         DEFAULT NULL,
    `updated_by`  int(11)          DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `IDX_customer_ext_customer_id` (`customer_id`),
    KEY `IDX_customer_ext_user_id` (`user_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 5
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;

-- Add customer_id as an index.
ALTER TABLE `email` ADD COLUMN `customer_id` INT NULL COMMENT 'Customer ID.' AFTER `supplier_id`;
ALTER TABLE `email` ADD INDEX `IDX_email_customer_id` (`customer_id`);
ALTER TABLE `offer_template_file`
    DROP FOREIGN KEY `FK_offer_template_file_offer_template_id`;
ALTER TABLE `offer_template_file`
    ADD CONSTRAINT `FK_offer_template_file_offer_template_id` FOREIGN KEY (`offer_template_id`) REFERENCES `offer_template` (`id`) ON UPDATE CASCADE ON DELETE CASCADE;


ALTER TABLE `email`
    ADD COLUMN `offer_no` VARCHAR(15) NULL COMMENT 'Org OfferNo' AFTER `email_template_id`,
    ADD INDEX `IDX_email_offer_no` (`offer_no`);


insert into `sys_dict`(`code`, `type`, `label`, `sort`, `value`)
values ('OFFER_DEFAULT_ANREDE_DE', 'Offer', 'Offer Default Anrede (DE)', 5, '')
     , ('OFFER_DEFAULT_ANREDE_EN', 'Offer', 'Offer Default <PERSON> (EN)', 6, '')
;

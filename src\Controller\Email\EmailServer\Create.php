<?php

declare(strict_types=1);

namespace App\Controller\Email\EmailServer;

use App\Exception\BaseException;
use App\Lib\Func;
use App\Service\Email\EmailAccount\EmailAccountService;
use Slim\Http\Request;
use Slim\Http\Response;

final class Create extends Base
{
    public function __invoke(Request $request, Response $response): Response
    {
        $input = (array)$request->getParsedBody();

		$this->validate($input);

        if (($input['smtp_user'] ?? null) && !($input['smtp_password'] ?? null)) {
            BaseException::raiseInvalidRequest('SMTP password is required!');
        }
        if ($input['smtp_password']) {
            $input['smtp_password'] = $input['smtp_password'] = Func::mcrypt(
                'encrypt'
                , $input['smtp_password']
                , EmailAccountService::SEC_KEY
                , EmailAccountService::SEC_IV
            );
        }

        $row = $this->emailServerService->create($input);

        return $this->jsonResponse($response, 'success', $row, 201);
    }
}

<?php

namespace App\Lib;

use App\Models\Sys\SysXlsColNameMap;

/**
 * Class Func for Xls file management: Utility functions here.
 *
 * @package App\Lib
 * <AUTHOR>
 */
final class FuncXls
{
    /**
     * @param array $xlsRow
     * @param $definition
     * @return array
     */
    public static function getBoundData(array &$xlsRow, &$definition, &$settings): array
    {
        $ignores = $settings['ignores'];
        $dbRow = [];
        /** @var SysXlsColNameMap $dbFieldDef */
        foreach ($definition as $xlsColInd => $dbFieldDef) {
            if (!in_array($xlsColInd-1, $ignores)) {
                $val = $xlsRow[$xlsColInd-1] ? $xlsRow[$xlsColInd-1] : null;

                if ($val) {
                    switch ($dbFieldDef->db_field_type) {
                        case 'bigint':
                        case 'int':
                            $val = Func::safeInt($val);
                            break;
                        case 'float':
                        case 'double':
                            $val = Func::safeDouble($val);
                            break;
                        case 'numeric':
                            $val = +$val;
                            break;
                        case 'date':
                            if (is_numeric($val)) {
                                $val = date(DATE_FORMAT_YMD, \PhpOffice\PhpSpreadsheet\Shared\Date::excelToTimestamp($val));
                            } else {
                                $val = Func::parseDtStrToDbDtDeep($val);
                            }
                            break;
                    }
                }
                $dbRow[$dbFieldDef->db_field] = $val;
            }
        }

        return $dbRow;
    }
}

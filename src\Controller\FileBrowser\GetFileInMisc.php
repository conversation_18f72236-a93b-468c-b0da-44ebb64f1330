<?php

namespace App\Controller\FileBrowser;

use App\Exception\BaseException;
use App\Lib\FileBrowserLib;
use App\Lib\FileLib;
use App\Lib\Func;
use App\Lib\FuncModel;
use Slim\Http\Request;
use Slim\Http\Response;

class GetFileInMisc extends Base
{
    /**
     * @throws \Exception
     */
    public function __invoke(Request $request, Response $response, array $args): Response
    {
        $params = $request->getParams();
        $id = $params['id'] ?? '';
        $basePath = FileBrowserLib::id2path($id);
        if ($basePath == '!') $basePath = '';
        if (!$basePath) BaseException::raiseInvalidRequest('Path invalid.');

        $rootPath = FuncModel::getDictValue('');
        $absPath = Func::pathJoin($rootPath, $basePath);
        if ($absPath && file_exists($absPath)) {
            FileLib::downloadFile($absPath);
        } else {
            BaseException::raiseInvalidRequest('Path invalid.');
        }

        return $this->jsonResponse($response, 'success',$params, 200);
    }
}
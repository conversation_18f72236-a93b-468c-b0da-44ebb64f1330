import { EditOutlined, PlusOutlined, SettingOutlined } from "@ant-design/icons";
import { Button, message, Drawer, Space, Tag } from "antd";
import React, { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON>ontainer, FooterToolbar } from "@ant-design/pro-layout";
import type { ProColumns, ActionType } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import type { ProFormInstance } from "@ant-design/pro-form";
import { ModalForm } from "@ant-design/pro-form";
import type { ProDescriptionsItemProps } from "@ant-design/pro-descriptions";
import ProDescriptions from "@ant-design/pro-descriptions";
import type { FormValueType } from "./components/UpdateForm";
import UpdateForm from "./components/UpdateForm";
import { addUser, updateUser, removeUser, updateUserPartial } from "@/services/app/user";
import { users } from "@/services/app/user";
import Util from "@/util";
import * as ConstVar from "@/constants";
import CreateForm from "./components/CreateForm";
import UserSettingForm from "./components/UserSettingForm";

export const UserRoleOptionKv = ConstVar.UserRoleOptions.reduce((prev, x) => {
  // @ts-ignore
  prev[x.value] = x.label;
  return prev;
}, {});

export const UserStatus = (status: ConstVar.UserStatus) => {
  let statusText = "Enabled";
  let color = "green";

  switch (Util.safeInt(status)) {
    case 0:
      statusText = "Disabled";
      color = "red";
      break;
  }
  return (
    <Space>
      <Tag color={color}>{statusText}</Tag>
    </Space>
  );
};

/**
 * @en-US Add node
 * @param fields
 */
const handleAdd = async (fields: API.UserListItem) => {
  const hide = message.loading("Adding...", 0);
  const data = { ...fields };
  delete data.confirmPassword;
  try {
    await addUser(data);
    message.success("Added successfully");
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};
/**
 * @en-US Update node
 *
 * @param fields
 */

const handleUpdate = async (fields: FormValueType) => {
  const hide = message.loading("Updating...", 0);

  try {
    await updateUser(fields);
    hide();
    message.success("Updated successfully.");
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};
/**
 *  Delete node
 *
 * @param selectedRows
 */

const handleRemove = async (selectedRows: API.UserListItem[]) => {
  const hide = message.loading("Deleting");
  if (!selectedRows) return true;

  try {
    await removeUser({
      user_id: selectedRows.map((row) => row.user_id),
    });
    hide();
    message.success("Deleted successfully and will refresh soon");
    return true;
  } catch (error) {
    hide();
    Util.error("Delete failed, please try again!");
    return false;
  }
};

const UsersList: React.FC = () => {
  const [createModalVisible, handleModalVisible] = useState<boolean>(false);
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);
  const [settingModalVisible, handleSettingModalVisible] = useState<boolean>(false);

  const [showDetail, setShowDetail] = useState<boolean>(false);
  const actionRef = useRef<ActionType>();
  const createFormRef = useRef<ProFormInstance>();
  const updateFormRef = useRef<ProFormInstance>();
  const updateSettingFormRef = useRef<ProFormInstance>();
  const [currentRow, setCurrentRow] = useState<API.UserListItem>();
  const [selectedRowsState, setSelectedRows] = useState<API.UserListItem[]>([]);

  const columns: ProColumns<API.UserListItem>[] = [
    {
      title: "User Name",
      dataIndex: "username",
      tip: "The username is the unique key",
      colSize: 1,
      filtered: true,
      render: (dom, entity) => {
        return (
          <a
            onClick={() => {
              setCurrentRow(entity);
              setShowDetail(true);
            }}
          >
            {dom}
          </a>
        );
      },
    },
    {
      title: "Name",
      dataIndex: "name",
      sorter: true,
      filtered: true,
    },
    {
      title: "Initials",
      dataIndex: "initials",
      sorter: true,
      filtered: true,
    },
    {
      title: "Email",
      dataIndex: "email",
      sorter: true,
      hideInForm: true,
      renderText: (val: string) => `${val}`,
    },
    {
      title: "Role",
      dataIndex: "role",
      hideInForm: false,
      sorter: true,
      filters: true,
      valueEnum: UserRoleOptionKv,
    },
    {
      title: "Status",
      dataIndex: "status",
      hideInForm: false,
      sorter: true,
      filters: true,
      valueEnum: {
        "0": {
          text: "Disabled",
        },
        "1": {
          text: "Enabled",
        },
      },
      render: (_, record) => (record ? UserStatus(record.status as ConstVar.UserStatus) : ""),
    },
    {
      title: "Created On",
      sorter: true,
      dataIndex: "created_on",
      valueType: "dateTime",
      search: false,
      width: 130,
      render(__, record) {
        return Util.dtToDMYHHMM(record.created_on);
      },
    },
    {
      title: "Last Login On",
      sorter: true,
      dataIndex: "last_login_on",
      valueType: "dateTime",
      search: false,
      width: 130,
      render(__, record) {
        return Util.dtToDMYHHMM(record.last_login_on);
      },
    },
    {
      title: "Option",
      dataIndex: "option",
      valueType: "option",
      render: (_, record) => [
        <Button
          key="edit"
          size="small"
          variant="outlined"
          color="primary"
          icon={<EditOutlined />}
          onClick={() => {
            handleUpdateModalVisible(true);
            setCurrentRow(record);
          }}
        />,
        <Button
          key="settings"
          size="small"
          variant="outlined"
          color="primary"
          icon={<SettingOutlined />}
          onClick={() => {
            handleSettingModalVisible(true);
            setCurrentRow(record);
          }}
        />,
      ],
    },
  ];

  useEffect(() => {
    if (updateFormRef && updateFormRef.current) {
      const newValues = { ...(currentRow || {}) };
      newValues.password = "";
      newValues.confirmPassword = "";
      updateFormRef.current.setFieldsValue(newValues);
    }
  }, [updateFormRef, currentRow]);

  return (
    <PageContainer>
      <ProTable<API.UserListItem, API.PageParams>
        headerTitle={"Users list"}
        actionRef={actionRef}
        rowKey="user_id"
        revalidateOnFocus={false}
        size="small"
        search={{
          labelWidth: "auto",
          searchText: "Search",
          span: 6,
          filterType: "query",
        }}
        toolBarRender={() => [
          <Button
            type="primary"
            key="primary"
            onClick={() => {
              handleModalVisible(true);
            }}
          >
            <PlusOutlined /> New
          </Button>,
        ]}
        request={users}
        onRequestError={Util.error}
        columns={columns}
        rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }}
        columnEmptyText=""
      />
      {selectedRowsState?.length > 0 && (
        <FooterToolbar
          extra={
            <div>
              chosen{" "}
              <a
                style={{
                  fontWeight: 600,
                }}
              >
                {selectedRowsState.length}
              </a>{" "}
              user &nbsp;&nbsp;
            </div>
          }
        >
          <Button
            onClick={async () => {
              await handleRemove(selectedRowsState);
              setSelectedRows([]);
              actionRef.current?.reloadAndRest?.();
            }}
          >
            Batch deletion
          </Button>
          {/* <Button type="primary">batch approval</Button> */}
        </FooterToolbar>
      )}

      <ModalForm
        title={"New user"}
        width="500px"
        open={createModalVisible}
        onOpenChange={handleModalVisible}
        layout="horizontal"
        labelAlign="left"
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 12 }}
        formRef={createFormRef}
        onFinish={async (value) => {
          const success = await handleAdd(value as API.UserListItem);

          if (success) {
            handleModalVisible(false);
            if (createFormRef.current) createFormRef.current.resetFields();

            if (actionRef.current) {
              actionRef.current.reload();
            }
          }
        }}
      >
        <CreateForm onCancel={() => handleModalVisible(false)} formRef={createFormRef} />
      </ModalForm>

      <ModalForm
        title={"Update user"}
        width="500px"
        open={updateModalVisible}
        onOpenChange={handleUpdateModalVisible}
        layout="horizontal"
        labelAlign="left"
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 12 }}
        initialValues={currentRow || {}}
        formRef={updateFormRef}
        onFinish={async (value) => {
          if (!currentRow?.user_id) return;
          const success = await handleUpdate({ ...value, user_id: currentRow?.user_id });

          if (success) {
            handleUpdateModalVisible(false);
            setCurrentRow(undefined);

            if (actionRef.current) {
              actionRef.current.reload();
            }
          }
        }}
      >
        <UpdateForm
          values={currentRow || {}}
          onCancel={() => {
            handleUpdateModalVisible(false);

            if (!showDetail) {
              setCurrentRow(undefined);
            }
          }}
        />
      </ModalForm>

      <ModalForm
        title={"Update user settings"}
        width="500px"
        open={settingModalVisible}
        onOpenChange={(open) => {
          handleSettingModalVisible(open);
          if (open) {
            updateSettingFormRef.current?.setFieldsValue(currentRow || {});
          }
        }}
        layout="horizontal"
        labelAlign="left"
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 16 }}
        initialValues={currentRow || {}}
        formRef={updateSettingFormRef}
        onFinish={async (value) => {
          if (!currentRow?.user_id) return;
          const newValues = { ...value, settings: { ...currentRow.settings, ...value.settings }, user_id: currentRow?.user_id };
          const hide = message.loading("Updating user settings...", 0);
          try {
            await updateUserPartial(newValues);
            hide();
            message.success("Updated successfully.");
            handleSettingModalVisible(false);
            setCurrentRow(undefined);

            if (actionRef.current) {
              actionRef.current.reload();
            }
          } catch (error) {
            hide();
            Util.error(error);
          }
        }}
      >
        <UserSettingForm values={currentRow || {}} />
      </ModalForm>

      <Drawer
        width={600}
        open={showDetail}
        onClose={() => {
          setCurrentRow(undefined);
          setShowDetail(false);
        }}
        closable={false}
      >
        {currentRow?.name && (
          <ProDescriptions<API.UserListItem>
            column={2}
            title={currentRow?.name}
            request={async () => ({
              data: currentRow || {},
            })}
            params={{
              id: currentRow?.name,
            }}
            columns={columns as ProDescriptionsItemProps<API.UserListItem>[]}
          />
        )}
      </Drawer>
    </PageContainer>
  );
};

export default UsersList;

<?php

declare(strict_types=1);

namespace App\Repository\Sys;

use App\Lib\Func;
use App\Lib\FuncModel;
use App\Models\Sys\SysLog;
use App\Repository\BaseRepositoryORM;
use Illuminate\Database\Eloquent\Builder;

final class SysLogRepository extends BaseRepositoryORM
{
    private function getQuerySysLogsByPage($params = []): Builder
    {
        $qb = $this->getQueryBuilder();

        if (!empty($params)) {
            if ($params['created_by'] ?? null) {
                $qb->where('created_by', $params['created_by']);
            }

            if ($params['category'] ?? null) {
                $qb->where('category', $params['category']);
            }
            if ($params['categories'] ?? null) {
                $qb->whereIn('category', $params['categories']);
            }

            if ($params['name'] ?? null) {
                $qb->where('name', 'LIKE', FuncModel::likeValue($params['name']));
            }

            if ($params['nameLike'] ?? null)
                $qb->where('name', 'LIKE', FuncModel::likeValue($params['nameLike'], '%'));

            if ($params['note'] ?? null)
                $qb->where('note', 'LIKE', FuncModel::likeValue($params['note'], '%'));

            if ($params['statuses'] ?? null)
                $qb->whereIn('status', $params['statuses']);

            // customized specific filters
            if ($params['ibo_pre_management_id'] ?? null) {
                // @version 1
                // $qb->where('note', 'LIKE', FuncModel::likeValue("IBO Pre #{$params['ibo_pre_management_id']}", '%'));
                $qb->where('ref2', 'LIKE', FuncModel::likeValue("IBO Pre #{$params['ibo_pre_management_id']}"));
            }

            if ($params['offer_id'] ?? null) {
                // @version 1
                // $qb->where('note', 'LIKE', FuncModel::likeValue("Offer #{$params['offer_id']}", '%'));
                $qb->where('ref2', 'LIKE', FuncModel::likeValue("Offer #{$params['offer_id']}"));
            }

            if (Func::keyExistsInWithParam($params, 'user'))
                $qb->with('user:user_id,username');

            $qb = $this->applyOrderBy($qb, $params);
        }

        return $qb;
    }

    /**
     * Get lists by pagination.
     *
     * @param int $page
     * @param int $perPage
     * @param array|null $params
     * @return array
     */
    public function getSysLogsByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        $query = $this->getQuerySysLogsByPage($params);
        $total = $this->getCountByQuery($query);

        $result = $this->getResultsWithPagination(
            $query,
            $page,
            $perPage,
            $total
        );

        return $result;
    }

    public function getQueryBuilder(): Builder
    {
        return SysLog::query();
    }
}
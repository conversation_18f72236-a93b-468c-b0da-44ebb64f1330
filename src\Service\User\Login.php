<?php

declare(strict_types=1);

namespace App\Service\User;

use App\Exception\User;
use App\Lib\Func;
use App\Models\Sys\SysLog;
use App\Service\Sys\SysLog\SysLogService;
use Firebase\JWT\JWT;
use Respect\Validation\Validator as v;

final class Login extends Base
{
    public function login(array $input): string
    {
        $data = json_decode((string)json_encode($input), true);
        if (empty($data['username'])) {
            throw new User('The field "username" is required.', 400);
        }
        if (empty($data['password'])) {
            throw new User('The field "password" is required.', 400);
        }

        $username = $data['username'];
        $email = filter_var($username, FILTER_SANITIZE_EMAIL);
        if (v::email()->validate($email)) {
            $user = $this->userRepository->getUserByEmail($email, 1);
        } else {
            $user = $this->userRepository->getUserByUsername($username, 1);
        }

        if (!Func::verifyPassword($data['password'], $user['password'])) {
            SysLogService::saveLog(SysLog::CATEGORY_LOGIN, 'Username or password invalid.', SysLog::STATUS_ERROR, "user: $username");
            throw new User('Username or password invalid.', 400);
        }

        if ($user && !$user['status']) {
            SysLogService::saveLog(SysLog::CATEGORY_LOGIN, 'Login is not allowed.', SysLog::STATUS_ERROR, "user: $username");
            throw new User('You are not allowed to login. Please contact to administrator.', 403);
        }

        $nonce = $this->userRepository->getNonce();
        $this->userRepository->update([
            'nonce' => $nonce,
            'client_detail' => $data['client_detail'] ?? '',
            'last_login_on' => Func::dtDbDatetimeStr(),
        ], $user['user_id']);

        $token = [
            'user_id' => $user['user_id'],
            'role' => $user['role'],
            'status' => $user['status'],
            'username' => $user['username'],
            'email' => $user['email'],
            'name' => $user['name'],
            'iss' => 'FoodStore',
            'nonce' => $nonce,
            'iat' => time(),
            // to do
            // 'exp' => time() + (1 * 30),    // 30s
            'exp' => time() + (8 * 60 * 60),    // 8hrs
        ];

        // save token into database
        // not yet

        // logging
        SysLogService::saveLog(SysLog::CATEGORY_LOGIN, 'logged in', SysLog::STATUS_SUCCESS, "user: $username");

        return JWT::encode($token, $_SERVER['SECRET_KEY'], 'HS512');
    }
}

import { <PERSON><PERSON>, <PERSON>, <PERSON>, message, Popover, Row, Space, Tag, Typography } from "antd";
import React, { useEffect, useRef, useState } from "react";
import { PageContainer } from "@ant-design/pro-layout";
import type { ProColumns, ActionType } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";

import { DEFAULT_PER_PAGE_PAGINATION } from "@/constants";
import Util, { ni, nl2br, sn, urlFull } from "@/util";
import type { ProFormInstance } from "@ant-design/pro-form";
import { ProFormSelect, ProFormText, ProFormDigit } from "@ant-design/pro-form";
import ProForm from "@ant-design/pro-form";
import {
  CheckCircleOutlined,
  CloseOutlined,
  EditOutlined,
  HeartTwoTone,
  InfoCircleOutlined,
  MailOutlined,
  SendOutlined,
  SmileOutlined,
} from "@ant-design/icons";
import { getSupplierListByPage, deleteSupplier } from "@/services/app/Supplier/supplier";
import CreateEmailForm from "@/pages/Email/EmailList/components/CreateEmailForm";
import { FooterToolbar, ProFormCheckbox } from "@ant-design/pro-components";
import SFooterToolbarExtra from "@/components/Table/SFooterToolbarExtra";
import EditableCell from "@/components/EditableCell";
import { updateSupplierExtBySupplierId } from "@/services/app/Supplier/supplier-ext";
import { useModel } from "@umijs/max";
import useUserOptions from "@/hooks/BasicData/useUserOptions";
import UpdateContactForm from "./components/UpdateContactForm";
import CreateForm from "./components/CreateForm";
import UpdateForm from "./components/UpdateForm";
import BatchDeleteAction from "@/components/Table/BatchDeleteAction";
import { ProductTrademarkOptions, SupplierTypeOptions } from "./SupplierDetail";
import { getProductCategoryACList } from "@/services/app/Sys/product-category";
import { whcRelevanceOptions } from "@/pages/Customer/Customer";
import useOrgAOptions from "@/hooks/BasicData/useOrgAOptions";

export const OrgAOptions = ["A", "B", "C", "D", "M"];

export const DefaultContactIconComp: React.FC<{ is_default?: ZeroOrOne; is_asp?: ZeroOrOne; is_asp_reg?: ZeroOrOne }> = ({
  is_default,
  is_asp,
  is_asp_reg,
}) => {
  // return is_default == 1 ? <HeartTwoTone twoToneColor="#eb2f96" title="Default contact" /> : null;
  if (is_asp) {
    return <HeartTwoTone twoToneColor="#eb2f96" title="ASP (Leftover)" />;
  } else if (is_asp_reg) {
    return <SmileOutlined title="ASP Reg." />;
  }
};

export const SupplierAddressComp: React.FC<{
  address: API.SupplierAddress;
  ind?: number;
  mode?: number;
}> = ({ address, ind, mode }) => {
  return mode == 2 ? (
    <Row wrap={false} style={{ borderTop: sn(ind) > 0 ? "1px solid #eee" : "none" }}>
      <Col flex="150px">{address.fullname}</Col>
      <Col flex="180px">{address.street}</Col>
      <Col flex="80px">{address.postcode}</Col>
      <Col flex="80px">{address.city}</Col>
      <Col flex="80px">{address.region?.region}</Col>
      <Col flex="auto">{address.country?.name}</Col>
    </Row>
  ) : (
    <Space direction="vertical" style={{ lineHeight: 1.2 }}>
      <div>
        {address.street} {address.postcode} {address.city}{" "}
      </div>
      <div>
        {address.region?.region ?? ""} {address.country?.name} {address.telephone}
      </div>
    </Space>
  );
};

export type SearchFormValueType = Partial<API.Supplier>;
export type OfferSelectionFormValueType = { offer_id?: number };

const SupplierList: React.FC = () => {
  const { initialState } = useModel("@@initialState");

  const searchFormRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();

  const [loading, setLoading] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<API.Supplier>();

  const [receiver, setReceiver] = useState<string>("");
  const [replyModalVisible, handleReplyModalVisible] = useState<boolean>(false); // to Supplier
  const [openBulkSendMailModal, setOpenBulkSendMailModal] = useState<boolean>(false); // bulk Email
  const [openStaffEmailModal, setOpenStaffEmailModal] = useState<boolean>(false); // to Staff

  const [openCreateModal, setOpenCreateModal] = useState<boolean>(false);
  const [openUpdateModal, setOpenUpdateModal] = useState<boolean>(false);
  const [openUpdateContactsModal, setOpenUpdateContactsModal] = useState<boolean>(false);

  const [selectedRowsState, setSelectedRows] = useState<API.Supplier[]>([]);

  const { userOptions } = useUserOptions();
  const { setSelectedOrgA, orgAOptions, orgAList, selectedOrgA } = useOrgAOptions();

  const columns: ProColumns<API.Supplier>[] = [
    {
      title: "ID",
      dataIndex: "id",
      sorter: true,
      hideInSearch: true,
      align: "center",
      width: 50,
    },
    {
      title: "OrgA",
      dataIndex: ["org_supplier", "org_a"],
      width: 50,
      align: "center",
    },
    {
      title: "Rel.",
      dataIndex: "relevance",
      width: 50,
      showSorterTooltip: false,
      tooltip: "Relevance. Click to edit",
      align: "center",
      className: "p-0",
      render(__, entity) {
        const relevance = entity.ext?.relevance;

        return (
          <EditableCell
            dataType="select"
            defaultValue={entity.ext?.relevance}
            style={{ marginRight: 0 }}
            fieldProps={{ style: { lineHeight: 1 } }}
            dataOptions={whcRelevanceOptions}
            isDefaultEditing={false}
            triggerUpdate={async (newValue: any, cancelEdit) => {
              const hide = message.loading("Saving...", 0);
              updateSupplierExtBySupplierId(entity?.id, {
                relevance: newValue ?? null,
              })
                .then((res) => {
                  message.success("Saved successfully.");
                  actionRef.current?.reload();
                  cancelEdit?.();
                })
                .catch(Util.error)
                .finally(() => {
                  setLoading(false);
                  hide();
                });
            }}
            allowClear
            showSearch
          >
            {`${relevance ?? ""}` ? relevance : ""} &nbsp;
          </EditableCell>
        );
      },
    },

    {
      title: "Active?",
      dataIndex: "is_active",
      align: "center",
      width: 30,
      ellipsis: true,
      showSorterTooltip: false,
      render: (__, record) => {
        let ele = null;
        // eslint-disable-next-line eqeqeq
        if (record.is_active == 1) {
          ele = <CheckCircleOutlined style={{ color: "green" }} />;
        } else {
          ele = <CloseOutlined style={{ color: "gray" }} />;
        }
        return ele;
      },
    },
    {
      title: "Name",
      dataIndex: "name",
      width: 200,
      showSorterTooltip: false,
      copyable: false,
      render(__, entity) {
        return (
          <Row wrap={false} gutter={4}>
            <Col flex="auto">
              <Typography.Link href={`${PUBLIC_PATH}suppliers/detail/${entity.id}`} target="_blank">
                <span>{entity.name || " - "}</span>
                {entity.internal_name ? <span className="text-sm">{` / ${entity.internal_name}`}</span> : null}
              </Typography.Link>
            </Col>
            <Col flex="24px">
              {entity.ext?.note || entity.meta?.length ? (
                <Popover
                  title={`${entity.name}`}
                  placement="right"
                  content={
                    <div style={{ maxHeight: 650, overflowY: "auto" }} dangerouslySetInnerHTML={{ __html: nl2br(entity.ext?.note || "") }}></div>
                  }
                  styles={{ root: { maxWidth: 1200 } }}
                >
                  <InfoCircleOutlined />
                </Popover>
              ) : null}
            </Col>
          </Row>
        );
      },
    },
    {
      title: "Contacts",
      dataIndex: "contacts",
      width: 550,
      showSorterTooltip: false,
      copyable: false,
      className: "p-0",
      render(__, entity) {
        const contacts = entity.contacts || [];
        return (
          <Row>
            <Col flex="auto">
              {contacts.map((x) => {
                const styleDefaultColor = { style: { color: x.is_default ? "black" : "grey" } };
                return (
                  <Row key={x.id}>
                    <Col flex={"20px"}>
                      <DefaultContactIconComp is_default={x.is_default} is_asp={x.is_asp} is_asp_reg={x.is_asp_reg} />
                    </Col>
                    <Col flex="auto" {...styleDefaultColor}>
                      {x.fullname}
                    </Col>
                    <Col flex={"24px"}>
                      {!!x.note && (
                        <Popover content={x.note} styles={{ body: { maxWidth: 300 } }}>
                          <InfoCircleOutlined {...styleDefaultColor} />
                        </Popover>
                      )}
                    </Col>
                    <Col flex="220px">
                      <Typography.Link
                        onClick={() => {
                          setReceiver(Util.emailBuildSender(x.email, x.fullname));
                          setCurrentRow(entity);
                          handleReplyModalVisible(true);
                        }}
                        style={{ opacity: x.is_default ? 1 : 0.6 }}
                      >
                        {x.email}
                      </Typography.Link>
                    </Col>
                    <Col flex="130px" {...styleDefaultColor}>
                      {x.telephone}
                    </Col>
                  </Row>
                );
              })}
            </Col>
            <Col flex="20px">
              <Button
                type="link"
                icon={<EditOutlined />}
                onClick={() => {
                  setCurrentRow(entity);
                  setOpenUpdateContactsModal(true);
                }}
              />
            </Col>
          </Row>
        );
      },
    },
    {
      title: "Initials",
      dataIndex: ["ext", "user", "initials"],
      width: 80,
      showSorterTooltip: false,
      tooltip: "Click to Edit",
      className: "py-0",
      render(dom, entity) {
        const userId = entity.ext?.user?.user_id;
        const initials = entity.ext?.user?.initials ?? entity.ext?.user?.username ?? "-";
        return (
          <Row wrap={false}>
            <Col flex="auto">
              <EditableCell
                dataType="select"
                defaultValue={userId}
                style={{ marginRight: 0 }}
                fieldProps={{ style: { lineHeight: 1 } }}
                dataOptions={userOptions}
                isDefaultEditing={false}
                triggerUpdate={async (newValue: any, cancelEdit) => {
                  if (!newValue && !userId) {
                    cancelEdit?.();
                    return;
                  }

                  const hide = message.loading("Saving...", 0);
                  updateSupplierExtBySupplierId(entity?.id, {
                    user_id: newValue,
                  })
                    .then((res) => {
                      message.success("Saved successfully.");
                      actionRef.current?.reload();
                      cancelEdit?.();
                    })
                    .catch(Util.error)
                    .finally(() => {
                      setLoading(false);
                      hide();
                    });
                }}
                allowClear
                showSearch
              >
                {initials}
              </EditableCell>
            </Col>
            <Col flex="20px">
              <Button
                type="link"
                size="small"
                icon={<SendOutlined />}
                title={`Send email to ${initials}`}
                onClick={() => {
                  setCurrentRow(entity);
                  setOpenStaffEmailModal(true);
                }}
              />
            </Col>
          </Row>
        );
      },
    },
    {
      title: "Qty Phone Calls",
      dataIndex: "calls_phone_count",
      width: 70,
      align: "center",
      showSorterTooltip: false,
      render(__, entity) {
        return ni(entity.calls_phone_count);
      },
    },
    {
      title: "Qty. Offers",
      dataIndex: ["org_supplier", "distinct_offers_count"],
      width: 60,
      align: "center",
      showSorterTooltip: false,
      render(__, entity) {
        return ni(entity.org_supplier?.distinct_offers_count);
      },
    },
    {
      title: "Supplier Type",
      dataIndex: "meta",
      width: 90,
      showSorterTooltip: false,
      render(__, entity) {
        return entity.meta ? (
          <Space wrap={true} direction="vertical">
            <div>
              {entity.meta
                .filter((x) => x.type == "supplier_type")
                .map((x) => (
                  <Tag key={x.value} color="pink">
                    {x.value}
                  </Tag>
                ))}
            </div>
          </Space>
        ) : null;
      },
    },
    {
      title: "Trademark / Categories",
      dataIndex: "meta",
      width: 200,
      showSorterTooltip: false,
      render(__, entity) {
        return entity.meta ? (
          <Space wrap={true} direction="vertical">
            <div>
              {entity.meta
                .filter((x) => x.type == "product_trademark")
                .map((x) => (
                  <Tag key={x.value} color="purple">
                    {x.value}
                  </Tag>
                ))}
            </div>
            <div>
              {entity.meta
                .filter((x) => x.type == "product_category")
                .map((x) => (
                  <Tag key={x.value} color="green">
                    {x.label ?? x.value}
                  </Tag>
                ))}
            </div>
          </Space>
        ) : null;
      },
    },
    {
      title: "Address",
      dataIndex: "address",
      width: 400,
      showSorterTooltip: false,
      render(__, entity) {
        return (
          <Row>
            <Col flex="auto">{entity.address ? <SupplierAddressComp address={entity.address} key={1} ind={1} /> : null}</Col>
            <Col flex="20px">
              <Button
                type="link"
                icon={<EditOutlined />}
                onClick={() => {
                  setCurrentRow(entity);
                  setOpenUpdateModal(true);
                }}
              />
            </Col>
          </Row>
        );
      },
    },

    {
      title: "Created on",
      dataIndex: ["created_on"],
      sorter: true,
      width: 70,
      ellipsis: true,
      className: "text-sm c-grey",
      showSorterTooltip: false,
      defaultSortOrder: "descend",
      render: (dom, record) => Util.dtToDMYHHMMTz(record.created_on),
    },
    {
      title: "Updated on",
      dataIndex: ["updated_on"],
      sorter: true,
      width: 70,
      ellipsis: true,
      className: "text-sm c-grey",
      showSorterTooltip: false,
      render: (dom, record) => Util.dtToDMYHHMMTz(record.updated_on),
    },
  ];

  useEffect(() => {
    if (orgAList) {
      const formValues = Util.getSfValues("cu_sf_supplier", {});
      if (!("in_org_a" in formValues)) {
        searchFormRef.current?.setFieldValue("in_org_a", orgAList);
        setSelectedOrgA(orgAList);
        actionRef.current?.reload();
      }
    }
  }, [orgAList, setSelectedOrgA]);

  return (
    <PageContainer
    /* extra={
        <Space size={24}>
          <Button
            variant="outlined"
            color="green"
            title="Down syncing all suppliers data"
            onClick={() => {
              const hide = message.loading('Down Syncing Suppliers...', 0);
              dsSupplier()
                .then(() => {
                  message.success('Synced successfully!');
                  actionRef.current?.reload();
                })
                .catch(Util.error)
                .finally(hide);
            }}
          >
            Sync
          </Button>
        </Space>
      } */
    >
      <Card style={{ marginBottom: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          submitter={{
            render(props, dom) {
              return <div style={{ marginLeft: "auto" }}>{dom}</div>;
            },
            searchConfig: { submitText: "Search" },
            submitButtonProps: { loading, htmlType: "submit", style: { marginLeft: 8 } },
            onSubmit: () => actionRef.current?.reload(),
            onReset: () => {
              searchFormRef.current?.resetFields();
              actionRef.current?.reload();
            },
          }}
        >
          <ProFormCheckbox.Group
            name="in_org_a"
            label="Org A"
            options={orgAOptions as any}
            fieldProps={{
              onChange(checkedValues) {
                const oldCheckedValues = [...selectedOrgA];
                // const currentSelectedValues = checkedValues.filter((x) => !oldCheckedValues.includes(x));
                const currentRemovedValues = oldCheckedValues.filter((x: string) => !checkedValues.includes(x));

                // when `all` is unchecked
                if (currentRemovedValues.includes("all")) {
                  const newCheckedValues = ["-"];
                  setSelectedOrgA(newCheckedValues);
                } else {
                  setSelectedOrgA(checkedValues);
                }
                actionRef.current?.reload();
              },
            }}
          />

          <ProFormSelect
            name="user_id"
            label="WHC User"
            options={userOptions}
            fieldProps={{
              onChange(value, option) {
                actionRef.current?.reload();
              },
              popupMatchSelectWidth: false,
            }}
            width={120}
            showSearch
            allowClear
          />

          <ProFormText name={"id"} label="Supplier ID" width={"xs"} placeholder={"Supplier ID"} />

          <ProFormText name={"keyWords"} label="KeyWords" width={200} placeholder={"Search by Company Name / Contacts / Email"} />

          <ProFormSelect
            name="supplier_type"
            label="Supplier Type"
            options={["-", ...SupplierTypeOptions]}
            width="xs"
            fieldProps={{
              onChange(value, option) {
                actionRef.current?.reload();
              },
              popupMatchSelectWidth: false,
            }}
            allowClear
            showSearch
          />

          <ProFormSelect
            name="product_trademark"
            label="Trademark"
            options={["-", ...ProductTrademarkOptions]}
            width="xs"
            fieldProps={{
              onChange(value, option) {
                actionRef.current?.reload();
              },
              popupMatchSelectWidth: false,
            }}
            allowClear
            showSearch
          />

          <ProFormSelect
            name="product_category"
            label="Category"
            width="xs"
            request={async (params) => {
              try {
                const res = await getProductCategoryACList(params);
                return [{ value: "-", label: "- No Category - " }, ...res];
              } catch (err) {
                Util.error(err);
                return [];
              }
            }}
            fieldProps={{
              onChange(value, option) {
                actionRef.current?.reload();
              },
              popupMatchSelectWidth: false,
            }}
            allowClear
            showSearch
          />

          <Space style={{ marginLeft: "unset" }}>
            <ProFormDigit
              name="relevance_from"
              label="Relevance"
              placeholder="from"
              min={0}
              max={9}
              width={60}
              fieldProps={{ precision: 0 }}
              formItemProps={{ style: { marginRight: 0 } }}
            />
            <span>~</span>
            <ProFormDigit name="relevance_to" placeholder="to" min={0} max={9} fieldProps={{ precision: 0 }} width={60} />
          </Space>
        </ProForm>
      </Card>

      <ProTable<API.Supplier, API.PageParams>
        headerTitle={
          <Space size={32}>
            <span>Suppliers List</span>
          </Space>
        }
        toolBarRender={() => [
          <Button
            key="new"
            type="primary"
            onClick={() => {
              setOpenCreateModal(true);
            }}
          >
            New
          </Button>,
        ]}
        actionRef={actionRef}
        rowKey="id"
        size="small"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        sticky
        scroll={{ x: 800 }}
        pagination={{
          defaultPageSize: sn(Util.getSfValues("cu_sf_supplier_p")?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION),
        }}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues("cu_sf_supplier", searchFormValues);
          Util.setSfValues("cu_sf_supplier_p", params);

          setLoading(true);
          return getSupplierListByPage(
            {
              ...params,
              with: "org_supplier,contacts,address,address.country,ext,ext.user,meta,metaLabel,calls_phone_count",
              ...Util.mergeGSearch(searchFormValues),
            },
            sort,
            filter,
          )
            .then((res) => {
              if (currentRow?.id) {
                setCurrentRow(res.data.find((x) => x.id == currentRow.id));
              }
              return res;
            })
            .finally(() => setLoading(false));
        }}
        onRequestError={Util.error}
        columns={columns}
        rowSelection={{
          selectedRowKeys: selectedRowsState.map((x) => x.id as React.Key),
          onChange(selectedRowKeys, selectedRows, info) {
            setSelectedRows(selectedRows);
          },
        }}
        tableAlertRender={false}
        columnEmptyText=""
      />

      <CreateEmailForm
        modalVisible={replyModalVisible}
        handleModalVisible={handleReplyModalVisible}
        initialValues={{
          receiver:
            receiver ?? (currentRow?.contacts ? Util.emailBuildSender(currentRow?.contacts?.[0]?.email, currentRow?.contacts?.[0]?.fullname) : ""),
        }}
        supplier={{
          id: currentRow?.id,
          name: currentRow?.name,
          address: currentRow?.address,
          contacts: currentRow?.contacts,
          created_on: currentRow?.created_on,
        }}
        onSubmit={async (value) => {
          setCurrentRow(undefined);
        }}
        onCancel={() => {
          handleReplyModalVisible(false);
        }}
      />

      <CreateEmailForm
        modalVisible={openBulkSendMailModal}
        handleModalVisible={setOpenBulkSendMailModal}
        initialValues={{ receiver: receiver }}
        htmlEditorId="bulk_email"
      />

      <CreateEmailForm
        modalVisible={openStaffEmailModal}
        handleModalVisible={setOpenStaffEmailModal}
        initialValues={{
          sender: Util.emailBuildSender(initialState?.currentUser?.email, initialState?.currentUser?.name),
          receiver: Util.emailBuildSender(currentRow?.ext?.user?.email, currentRow?.ext?.user?.name),
          text_html: `<br /><a href="${urlFull()}suppliers/detail/${currentRow?.id}" target="_blank">View Supplier detail: ${currentRow?.name} </a>`,
        }}
        htmlEditorId="send_email_whc"
        internal
        supplier={{
          id: currentRow?.id,
          name: currentRow?.name,
          address: currentRow?.address,
          contacts: currentRow?.contacts,
          created_on: currentRow?.created_on,
        }}
      />

      <UpdateContactForm
        modalVisible={openUpdateContactsModal}
        handleModalVisible={setOpenUpdateContactsModal}
        initialValues={currentRow}
        onSubmit={(value) => {
          actionRef.current?.reload();
        }}
      />

      <CreateForm
        modalVisible={openCreateModal}
        handleModalVisible={setOpenCreateModal}
        onSubmit={() => {
          actionRef.current?.reload();
        }}
      />

      <UpdateForm
        modalVisible={openUpdateModal}
        handleModalVisible={setOpenUpdateModal}
        onSubmit={() => {
          actionRef.current?.reload();
        }}
        initialValues={currentRow}
      />

      {selectedRowsState?.length > 0 && (
        <FooterToolbar extra={<SFooterToolbarExtra title={"Supplier"} selectedRowsState={selectedRowsState} actionRef={actionRef} />}>
          <Button
            type="primary"
            icon={<MailOutlined />}
            onClick={() => {
              if (selectedRowsState.length) {
                const str = selectedRowsState
                  .filter((x) => !!x.contacts?.find((x) => x.is_default)?.email)
                  ?.map((x) => Util.emailBuildSender(x.contacts?.find((x) => x.is_default)?.email, x.contacts?.find((x) => x.is_default)?.fullname))
                  ?.join(",");
                setReceiver(str);
                setOpenBulkSendMailModal(true);
              }
            }}
          >
            Send Mails
          </Button>

          <BatchDeleteAction
            title="Email template category"
            onConfirm={async () => {
              const hide = message.loading("Deleting...", 0);
              if (!selectedRowsState) return true;

              try {
                await deleteSupplier(selectedRowsState.map((row) => row.id).join(","));
                hide();
                message.success("Deleted successfully and will refresh soon");
                setSelectedRows([]);
                actionRef.current?.reloadAndRest?.();
              } catch (error) {
                hide();
                Util.error(error);
              }
            }}
          />
        </FooterToolbar>
      )}
    </PageContainer>
  );
};

export default SupplierList;

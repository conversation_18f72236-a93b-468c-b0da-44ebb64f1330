<?php

declare(strict_types=1);

namespace App\Controller\Email\Email;

use Slim\Http\Request;
use Slim\Http\Response;

final class GetOne extends Base
{
    public function __invoke(Request $request, Response $response, array $args): Response
    {
        $id = $args['id'] ?? null;
        if (!$id) {
            \App\Exception\Base::raiseInvalidRequest('ID is required.');
        }

        $params = (array)$request->getQueryParams();
        $params['id'] = $id;
        $data = $this->emailService->getEmailRepository()->getQueryEmailsByPage($params)->firstOrFail();

        return $this->jsonResponse($response, 'success', $data, 200);
    }
}

<?php

declare(strict_types=1);

use App\Service\Customer\CustomerCall\CustomerCallService;
use App\Service\Email\Email\EmailSendByOrgOfferService;
use App\Service\Email\Email\EmailService;
use App\Service\Email\EmailAccount\EmailAccountService;
use App\Service\Email\EmailServer\EmailServerService;
use App\Service\Email\EmailTemplate\EmailTemplateService;
use App\Service\Email\EmailTemplateCategory\EmailTemplateCategoryService;
use App\Service\File\FileService;
use App\Service\FileBrowser\FileBrowserService;
use App\Service\FileBrowser\MiscFileAssignService;
use App\Service\Offer\OfferExt\OfferExtService;
use App\Service\Offer\OrgOffer\OrgOfferService;
use App\Service\Supplier\Supplier\SupplierService;
use App\Service\Supplier\SupplierAddress\SupplierAddressService;
use App\Service\Supplier\SupplierContact\SupplierContactService;
use App\Service\Customer\Customer\CustomerService;
use App\Service\Customer\CustomerAddress\CustomerAddressService;
use App\Service\Customer\CustomerContact\CustomerContactService;
use App\Service\Customer\CustomerExt\CustomerExtService;
use App\Service\Offer\OfferTemplate\OfferTemplateService;
use App\Service\Offer\OfferTemplateLang\OfferTemplateLangService;
use App\Service\Offer\OfferBlog\OfferBlogService;
use App\Service\Offer\OfferNewsletter\OfferNewsletterService;
use App\Service\Sys\Backup\BackupService;
use App\Service\Sys\Country\CountryService;
use App\Service\Sys\CountryRegion\CountryRegionService;
use App\Service\Sys\Dict\DictService;
use App\Service\Sys\SysLog\SysLogService;
use App\Service\Sys\SysTextModule\SysTextModuleService;
use App\Service\User;

/**
 * User services
 * -------------------------------------------------------------------------------
 * @param Slim\Container $container
 * @return User\Find
 */
$container['find_user_service'] = static function (
    Slim\Container $container
): User\Find {
    return new User\Find(
        $container,
        $container->get(\App\Repository\UserRepository::class),
        $container->get('redis_service')
    );
};

$container['create_user_service'] = static function (
    Slim\Container $container
): User\Create {
    return new User\Create(
        $container,
        $container->get(\App\Repository\UserRepository::class),
        $container->get('redis_service')
    );
};

$container['update_user_service'] = static function (
    Slim\Container $container
): User\Update {
    return new User\Update(
        $container,
        $container->get(\App\Repository\UserRepository::class),
        $container->get('redis_service')
    );
};

$container['delete_user_service'] = static function (
    Slim\Container $container
): User\Delete {
    return new User\Delete(
        $container,
        $container->get(\App\Repository\UserRepository::class),
        $container->get('redis_service')
    );
};

$container['login_user_service'] = static function (
    Slim\Container $container
): User\Login {
    return new User\Login(
        $container,
        $container->get(\App\Repository\UserRepository::class),
        $container->get('redis_service')
    );
};



/**
 * File services
 * -------------------------------------------------------------------------------
 *
 * @param Slim\Container $container
 * @return FileService
 */
$container['file_service'] = static function (Slim\Container $container): FileService {
    return new FileService($container);
};


$map = [
    \App\Service\Supplier\SupplierCall\SupplierCallService::class,
    \App\Service\Supplier\SupplierExt\SupplierExtService::class,

    SupplierService::class,
    SupplierAddressService::class,
    SupplierContactService::class,

    CustomerService::class,
    CustomerAddressService::class,
    CustomerContactService::class,
    CustomerExtService::class,
    CustomerCallService::class,

    OfferTemplateService::class,
    OfferTemplateLangService::class,
    OfferExtService::class,
    OfferBlogService::class,
    OfferNewsletterService::class,

    SysTextModuleService::class,
    SysLogService::class,
    \App\Service\Sys\ProductCategory\ProductCategoryService::class,
    // \App\Service\Sys\Product\ProductService::class,
    // \App\Service\Sys\Trademark\TrademarkService::class,

    CountryService::class,
    CountryRegionService::class,

    DictService::class,

    EmailService::class,
    EmailSendByOrgOfferService::class,
    EmailAccountService::class,
    EmailServerService::class,
    EmailTemplateService::class,
    EmailTemplateCategoryService::class,
    BackupService::class,

    // File manager
    FileBrowserService::class,
    MiscFileAssignService::class,

    \App\Service\MediaApi\MediaApiBaseService::class,
    \App\Service\CustApi\CustApiService::class,

    OrgOfferService::class,
];

foreach ($map as $class) {
    $container[$class] = static function (Slim\Container $container) use ($class) {
        return new $class($container);
    };
}
import type { Dispatch, SetStateAction } from "react";
import React, { useEffect, useRef } from "react";
import { message } from "antd";
import type { ProFormInstance } from "@ant-design/pro-form";
import { ModalForm } from "@ant-design/pro-form";
import { updateSupplierCall } from "@/services/app/Supplier/supplier-call";
import Util, { getFormData, sn } from "@/util";
import { ProForm, ProFormDependency, ProFormRadio, ProFormTextArea } from "@ant-design/pro-components";
import { SupplierCallDirectionKv, SupplierCallType } from "@/constants";
import { useModel } from "@umijs/max";
import HtmlEditor from "@/components/HtmlEditor";
import { UploadFile } from "antd/es/upload";
import { DefaultOptionType } from "antd/es/select";
import { SupplierCallTypeOptions } from "@/pages/Supplier/SupplierCall/components/CreateForm";

type FormValueType = Omit<Partial<API.SupplierCall>, "files"> & {
  products?: DefaultOptionType[];
  trademarks?: DefaultOptionType[];
} & { files?: UploadFile[] };

const handleUpdate = async (fields: FormValueType) => {
  const hide = message.loading("Updating...", 0);

  const formData = getFormData(fields);

  if (fields.files?.length) {
    fields.files.forEach((file, ind) => {
      if (file?.originFileObj) {
        formData.append(`files[${ind}]`, file?.originFileObj ?? "");
      }
    });
  }

  try {
    await updateSupplierCall(sn(fields.id), formData as any);
    hide();
    message.success("Update is successful");
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type SupplierCallUpdateFormProps = {
  initialValues?: Partial<API.SupplierCall>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.SupplierCall) => void;
};

const SupplierCallUpdateForm: React.FC<SupplierCallUpdateFormProps> = (props) => {
  const { initialValues, modalVisible, handleModalVisible, onSubmit } = props;
  const { initialState } = useModel("@@initialState");

  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (modalVisible && formRef.current) {
      formRef.current.resetFields();
      const newValues = {
        ...(initialValues || {}),
      };

      formRef.current.setFieldsValue(newValues);
    }
  }, [modalVisible, initialValues, initialState?.currentUser?.initials]);

  return (
    <ModalForm
      title={"Update Supplier Conversion"}
      width="800px"
      open={modalVisible}
      onOpenChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ flex: "90px" }}
      wrapperCol={{ flex: "auto" }}
      initialValues={initialValues || {}}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleUpdate({ ...value, id: initialValues?.id });
        if (success) {
          handleModalVisible(false);
          if (onSubmit) onSubmit(value);
        }
      }}
    >
      <ProFormRadio.Group name="type" label="Type" options={SupplierCallTypeOptions} />

      <ProFormDependency name={["type"]}>
        {(deps) => {
          if (deps.type == SupplierCallType.Offer) return null;
          else return <ProFormRadio.Group name="direction" label="Direction" valueEnum={SupplierCallDirectionKv} />;
        }}
      </ProFormDependency>

      {/* <ProFormDependency name={["type"]}>
        {(deps) => {
          if (deps.type == SupplierCallType.Offer) return <ProFormTextArea name="comment" label="Comment" fieldProps={{ rows: 5 }} />;
          else return null;
        }}
      </ProFormDependency> */}

      <ProForm.Item name={"note"} label={"Notes"} style={{ width: "100%" }} labelCol={undefined} wrapperCol={{ span: 24 }}>
        <HtmlEditor id={`call_note_update`} initialFocus enableTextModule hideMenuBar toolbarMode={2} height={400} />
      </ProForm.Item>
    </ModalForm>
  );
};

export default SupplierCallUpdateForm;

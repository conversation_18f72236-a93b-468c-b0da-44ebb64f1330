import { request } from 'umi';
import { paramsSerializer } from '../api';

const urlPrefix = '/api/offer/org-offer';

export async function getOrgOffer(id: string, params?: any) {
  return request<API.ResultObject<APIOrg.Offer>>(`${urlPrefix}/${id}`, {
    method: 'GET',
    params,
    withToken: true,
    paramsSerializer,
  }).then((res) => {
    return res.message;
  });
}


export async function getOrgOfferListByPage(params: API.PageParams, sort?: any, filter?: any) {
  return request<API.ResultList<APIOrg.Offer>>(`${urlPrefix}-list`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
    pagination: res.message.pagination,
  }));
}
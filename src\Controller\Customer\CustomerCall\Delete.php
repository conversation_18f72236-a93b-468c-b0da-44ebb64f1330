<?php

declare(strict_types=1);

namespace App\Controller\Customer\CustomerCall;

use Slim\Http\Request;
use Slim\Http\Response;

final class Delete extends Base
{
    public function __invoke(Request $request, Response $response, array $args): Response
    {
        $ids = $args['id'] ?? '';
        if (!$ids) {
            \App\Exception\Base::raiseInvalidRequest();
        }
        $this->customerCallService->delete($ids);

        return $this->jsonResponse($response, 'success', null, 200);
    }
}

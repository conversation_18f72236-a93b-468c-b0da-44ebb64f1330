<?php

declare(strict_types=1);

namespace App\Repository\Email;

use App\Lib\Func;
use App\Lib\FuncModel;
use App\Models\Email\EmailTemplate;
use App\Models\Email\EmailTemplateCategory;
use App\Repository\BaseRepositoryORM;
use Illuminate\Database\Eloquent\Builder;
use <PERSON><PERSON>er<PERSON>n\Template\Template;

final class EmailTemplateRepository extends BaseRepositoryORM
{
    public function getQueryEmailTemplatesByPage($params = []): Builder
    {
        $qb = $this->getQueryBuilder();

        if (!empty($params)) {
            if ($params['id'] ?? null) {
                $qb->where('id', $params['id']);
            }

            if ($params['likeTitle'] ?? null) {
                $qb->where('title', 'like', FuncModel::likeValue($params['likeTitle'], '%'));
            }

            if ($params['category_id'] ?? null) {
                $qb->where('category_id', $params['category_id']);
            }

            if ($params['keyWords'] ?? '') {
                $qb->where(function (Builder $qb) use (&$params) {
                    $qb->where('title', 'like', $params['keyWords'] . '%')
                        ->orWhere('subject', 'like', $params['keyWords'] . '%')
                        ->orWhereHas('category', function ($qb) use (&$params) {
                            $qb->where('cat1', 'like', $params['keyWords'] . '%');
                        });
                });
            }

            if (Func::keyExistsInWithParam($params, 'category')) {
                $qb->with('category');
            }


            $qb = $this->applyOrderBy($qb, $params);
        }

        return $qb;
    }

    /**
     * Get lists by pagination.
     *
     * @param int $page
     * @param int $perPage
     * @param array|null $params
     * @return array
     */
    public function getEmailTemplatesByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        $query = $this->getQueryEmailTemplatesByPage($params);
        $total = $this->getCountByQuery($query);

        $result = $this->getResultsWithPagination(
            $query,
            $page,
            $perPage,
            $total
        );

        return $result;
    }

    public function applyOrderBy(Builder $builder, ?array $params): Builder
    {
        $sortDetail = $params['sort'] ?? null;

        if ($sortDetail) {
            if (!is_array($sortDetail))
                $sortDetail = Func::safeJson($sortDetail) ?? [];

            if ($dir = $sortDetail['cat1_mix'] ?? null) {
                $builder->orderBy(EmailTemplateCategory::query()->whereColumn('email_template_category.id', 'email_template.category_id')->select('sort'), ($dir == 'descend') ? 'DESC' : 'ASC');
                $builder->orderBy('email_template.sort', ($dir == 'descend') ? 'DESC' : 'ASC');
            } else {
                foreach ($sortDetail as $filed => $dir) {
                    $fieldName = str_replace(',', '.', $filed);
                    $builder->orderBy($fieldName, ($dir == 'descend') ? 'DESC' : 'ASC');
                }
            }

        }
        return $builder;
    }

    public function getQueryBuilder(): Builder
    {
        return EmailTemplate::query();
    }
}
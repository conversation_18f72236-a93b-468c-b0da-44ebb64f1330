<?php

namespace App\Lib;

/**
 * Class Func: Utility functions here.
 *
 * @package App\Lib
 * <AUTHOR>
 */
final class FuncDate
{
    const DR_KEY_LABEL = array(
        DR_TODAY => 'Today',
        DR_YESTERDAY => 'Yesterday',
        DR_2_DAYS_AGO => '2 Days Ago',
        DR_THIS_WEEK => 'This Week',
        DR_LAST_WEEK => 'Last Week',
        DR_LAST_30_DAYS => 'Last 30 Days',
        DR_THIS_MONTH => 'This Month',
        DR_LAST_MONTH => 'Last Month',
        DR_THIS_YEAR => 'This Year',
        DR_LAST_YEAR => 'Last Year',
        DR_FY_THIS => 'This Economical Year',
        DR_FY_LAST => 'Last Economical Year',
        DR_SINCE_BEGIN => 'Since Beginning',
        DR_CUSTOM => 'Custom',
    );

    static function randomTs()
    {
        $ts = time() - 1668400000;
        return $ts;
        // return intval(sprintf("%d%02d", $ts, rand(1, 99)));
    }

    /**
     * Convert 2020-04-20           -> timestamp (seconds)
     *
     * @param $str
     * @param string $format
     * @return int|null
     */
    public static function strToTime($str, $format = "Y-m-d"): ?int
    {
        if (!$str) return null;
        if ($str == DATE_EMPTY_STR) return null;

        $dt = date_create_from_format($format, $str);
        if ($dt == false) {
            return null;
        } else {
            return $dt->getTimestamp();
        }
    }

    public static function dtStrToValidDbStr($str)
    {
        $dt = date_create_from_format(DATE_FORMAT_YMD_HIS, $str);
        if ($dt == false) {
            return null;
        } else {
            return $str;
        }
    }

    public static function dtToday($format = DATE_FORMAT_YMD)
    {
        return date($format);
    }

    public static function dtNow()
    {
        return date(DATE_FORMAT_YMD_HIS);
    }

    public static function dtDE($str, $srcFormat = DATE_FORMAT_YMD, $targetFormat = DATE_FORMAT_DMY_F)
    {
        $ts = self::strToTime($str, $srcFormat);
        return $ts ? date($targetFormat, $ts) : null;
    }

    /**
     * @param $var  string | number
     * @param $day_offset
     * @param bool $isTimestamp
     * @return false|int|string
     */
    public static function dtDateByOffset($var, $day_offset, $isTimestamp = FALSE)
    {
        if (is_string($var) && strlen($var) == 10) {
            $var = self::strToTime($var);
        }
        if (!$var) return '';
        if (is_numeric($var)) {
            $ts = strtotime("$day_offset day", $var);
            if ($ts) {
                if ($isTimestamp) return $ts;
                return date(DATE_FORMAT_YMD, $ts);
            }
        }
        return '';
    }

    public static function dtDbDatetimeStr($ts = null, $format = 'Y-m-d'): string
    {
        if (!$ts) $ts = time();
        return date($format, $ts);
    }

    /**
     * @param string|int $var string
     * @param $offsetDays
     * @param bool $isTimestamp
     * @return int|string
     */
    public static function dtByOffset(string|int $var, $offsetDays, bool $isTimestamp = false)
    {
        if (is_string($var) && strlen($var) == 10) {
            $var = self::strToTime($var);
        }
        if (!$var) return '';
        if (is_numeric($var)) {
            $ts = strtotime("$offsetDays day", $var);
            if ($ts) {
                if ($isTimestamp) return $ts;
                return date(DATE_FORMAT_YMD, $ts);
            }
        }
        return '';
    }

    /**
     * @param string|int $var string
     * @param $offsetDays
     * @param bool $isTimestamp
     * @return int|string
     */
    public static function dtByOffsetShort(string|int $var, $offsetDays, bool $isTimestamp = false)
    {
        return substr(self::dtDateByOffset($var, $offsetDays, $isTimestamp), 0, 10);
    }

    public static function parseGADate($str, $format = 'Ymd')
    {
        return self::dtDbDatetimeStr(self::strToTime($str, $format));
    }

    public static function diff($dateStr, $since = null, $format = '%a')
    {
        try {
            $date1 = new \DateTime($since ?? 'now');
            $date2 = new \DateTime($dateStr);
            $days = $date2->diff($date1)->format($format);

            return $days;
        } catch (\Exception $exception) {
        }
        return null;
    }

    /**
     * Get current fiscal Year
     * @param int $startMonth
     * @return int
     */
    public static function getFiscalYear(int $startMonth = 7): int
    {
        $ts_today = time();
        $last_year = intval(date('Y', $ts_today));

        if (date('Y-m-d', $ts_today) >= sprintf('%s-%02d-01', $last_year, $startMonth)) {
            $last_year++;
        }
        return $last_year;
    }


    public static function firstDayOfNLastMonth($nMonth, $ts = null)
    {
        return date('Y-m-d', strtotime("first day of {$nMonth} months", $ts));
    }

    public static function getDateRangeSelectionData($ts = null, $format = DATE_FORMAT_YMD_HIS): array
    {
        $today = time();
        $this_year = intval(date('Y', $today));
        $this_monday = strtotime('monday this week', $today);
        $last_year = $this_year - 1;
        $drMap = array(
            DR_TODAY => [date('Y-m-d', $today), date('Y-m-d', $today)],
            DR_YESTERDAY => [self::dtByOffset($today, -1), self::dtByOffset($today, -1)],
            DR_2_DAYS_AGO => [self::dtByOffset($today, -2), self::dtByOffset($today, -2)],
            DR_THIS_WEEK => [date('Y-m-d', $this_monday), self::dtByOffset($this_monday, 6)],
            DR_LAST_WEEK => [self::dtByOffset($this_monday, -7), self::dtByOffset($this_monday, -1)],
            DR_LAST_30_DAYS => [self::dtByOffset($today, -30), date('Y-m-d', $today)],

            DR_THIS_MONTH => [date('Y-m-d', strtotime('first day of this month', $today)), date("Y-m-d", strtotime('last day of this month', $today))],
            DR_LAST_MONTH => [date('Y-m-d', strtotime('first day of last month', $today)), date("Y-m-d", strtotime('last day of last month', $today))],

            DR_THIS_YEAR => [$this_year . "-01-01", $this_year . "-12-31"],
            DR_LAST_YEAR => [$last_year . "-01-01", $last_year . "-12-31"],
            DR_SINCE_BEGIN => ['', ''],
        );

        // fiscal years
        $fy_this_year = self::getFiscalYear(FY_START_MONTH);
        $fy_last_year = $fy_this_year - 1;
        $drMap[DR_FY_THIS] = [
            sprintf('%s-%02d-01', $fy_this_year - 1, FY_START_MONTH),
            date('Y-m-d', self::strToTime(sprintf('%s-%02d-01', $fy_this_year, FY_START_MONTH)) - DAY_SECONDS),
        ];
        $drMap[DR_FY_LAST] = [
            sprintf('%s-%02d-01', $fy_last_year - 1, FY_START_MONTH),
            date('Y-m-d', self::strToTime(sprintf('%s-%02d-01', $fy_last_year, FY_START_MONTH)) - DAY_SECONDS),
        ];

        return $drMap;
    }

    public static function getYmList(string $fromDate, $count)
    {
        $baseTs = strtotime($fromDate);
        $ymList = [
            self::firstDayOfNLastMonth(0, $baseTs),
        ];

        $ind = 1;
        while ($ind <= $count) {
            $next = self::firstDayOfNLastMonth($ind, $baseTs);
            $ymList[] = $next;
            $ind++;
        }

        return $ymList;
    }


    public static function getMonthsDiff($date1, $date2)
    {
        $ts1 = strtotime($date1);
        $ts2 = strtotime($date2);

        $year1 = date('Y', $ts1);
        $year2 = date('Y', $ts2);

        $month1 = date('m', $ts1);
        $month2 = date('m', $ts2);

        $diff = (($year2 - $year1) * 12) + (intval($month2) - intval($month1));

        return $diff;
    }

}

<?php

declare(strict_types=1);

namespace App\Controller\Supplier\Supplier;

use Slim\Http\Request;
use Slim\Http\Response;

final class GetSupplier2EmailMatrix extends Base
{
    public function __invoke(Request $request, Response $response): Response
    {
        $page = $request->getQueryParam('page', null);
        $perPage = $request->getQueryParam('perPage', null);
        $params = $request->getParams();
        $user = $this->getUserFromToken($request);
        $params['user'] = $user;
        $data = $this->supplierService->getSupplier2EmailMatrixByPage((int)$page, (int)$perPage, $params);

        return $this->jsonResponse($response, 'success', $data, 200);
    }
}

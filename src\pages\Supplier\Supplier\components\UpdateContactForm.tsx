import type { Dispatch, SetStateAction } from "react";
import React, { useEffect, useRef, useState } from "react";
import { Col, message, Row, Spin } from "antd";
import type { ProFormInstance } from "@ant-design/pro-form";
import { ProFormText, ModalForm, ProFormList } from "@ant-design/pro-form";
import Util from "@/util";
import { getSupplier, updateSupplier } from "@/services/app/Supplier/supplier";
import { FormListActionType, ProFormCheckbox, ProFormTextArea } from "@ant-design/pro-components";

type FormValueType = Partial<API.Supplier>;

const handleUpdate = async (fields: FormValueType) => {
  const hide = message.loading("Updating...", 0);

  try {
    await updateSupplier(fields.id, fields);
    hide();
    message.success("Update is successful");
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type UpdateContactFormProps = {
  initialValues?: Partial<API.Supplier>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Supplier) => void;
};

const UpdateContactForm: React.FC<UpdateContactFormProps> = (props) => {
  const { initialValues, modalVisible, handleModalVisible, onSubmit } = props;

  const formRef = useRef<ProFormInstance>();
  const actionRef = useRef<FormListActionType<API.SupplierContact>>();

  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (modalVisible && initialValues?.id && formRef.current) {
      formRef.current?.resetFields();
      formRef.current?.setFieldsValue({
        contacts: [
          {
            is_default: 1,
          },
        ],
      });

      setLoading(true);
      // load supplier data.
      getSupplier(initialValues?.id, { with: "contactsAll" })
        .then((res) => {
          const newValues = {
            contacts: res.contacts_all || [],
          };
          if (!newValues.contacts?.length) {
            newValues.contacts = [
              {
                is_default: 1,
              },
            ];
          }
          formRef.current?.setFieldsValue(newValues);
        })
        .catch(Util.error)
        .finally(() => setLoading(false));
    }
  }, [modalVisible, initialValues?.id]);

  return (
    <ModalForm
      title={"Update Supplier Contacts"}
      width="1750px"
      open={modalVisible}
      onOpenChange={handleModalVisible}
      layout="vertical"
      labelAlign="left"
      labelCol={{ span: 6 }}
      wrapperCol={{ span: 18 }}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleUpdate({ ...value, id: initialValues?.id });

        if (success) {
          handleModalVisible(false);
          if (onSubmit) onSubmit(value);
        }
      }}
      loading={loading}
    >
      <Spin spinning={loading}>
        <ProFormList
          actionRef={actionRef}
          name={["contacts"]}
          label={false}
          wrapperCol={{ span: 24 }}
          copyIconProps={false}
          deleteIconProps={{ tooltipText: "Delete this contact info" }}
          actionRender={(field, action, defaultDom) => {
            const data = actionRef.current?.get(field.key);

            if (/* !data?.is_active ||  */ !data?.id) return defaultDom;
            else return [];
          }}
          creatorButtonProps={{ creatorButtonText: "Add a new contact " }}
        >
          <Row gutter={8}>
            <Col flex="70px">
              <ProFormText name="prefix" label="Prefix" placeholder="Prefix" />
            </Col>
            <Col flex="120px">
              <ProFormText name="firstname" label="First Name" placeholder="First Name" required rules={[{ message: "Required", required: true }]} />
            </Col>
            <Col flex="120px">
              <ProFormText name="lastname" label="Last Name" placeholder="Last Name" required rules={[{ message: "Required", required: true }]} />
            </Col>
            <Col flex="70px">
              <ProFormCheckbox name="is_default" label="Default?" />
            </Col>
            <Col flex="180px">
              <ProFormText name="email" label="Email" placeholder="Email" required rules={[{ message: "Required", required: true }]} />
            </Col>
            <Col flex="120px">
              <ProFormText name="telephone" label="Telephone" placeholder="Telephone" />
            </Col>
            <Col flex="250px">
              <ProFormText name="department" label="Department" placeholder="Department" />
            </Col>
            <Col flex="200px">
              <ProFormTextArea name="note" label="Note" placeholder="Note" fieldProps={{ rows: 2 }} />
            </Col>
            <Col flex="65px">
              <ProFormCheckbox name="is_active" label="Active?" />
            </Col>
            <Col flex="110px">
              <ProFormCheckbox name="is_asp" label="ASP (Leftover)" />
            </Col>
            <Col flex="70px">
              <ProFormCheckbox name="is_asp_reg" label="ASP Reg." />
            </Col>
            <Col flex="65px">
              <ProFormCheckbox name="is_blocked" label="Blocked" />
            </Col>
          </Row>
        </ProFormList>
      </Spin>
    </ModalForm>
  );
};

export default UpdateContactForm;

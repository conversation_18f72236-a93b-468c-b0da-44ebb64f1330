import { dsPullEmail, getEmail, getNextEmail, updateEmail } from "@/services/app/Email/email";
import { getAppSettings } from "@/services/app/api";
import Util, { nl2br, sn } from "@/util";
import { ArrowLeftOutlined, ArrowRightOutlined, CloseOutlined, MailOutlined, SendOutlined } from "@ant-design/icons";
import { PageContainer } from "@ant-design/pro-layout";
import { Button, Card, Col, Row, Spin, message } from "antd";
import { useCallback, useEffect, useRef, useState } from "react";
import { useModel, useParams } from "umi";
import ReplyForm from "./components/ReplyForm";
import AttachmentIconIcon from "./components/AttachmentIcon";
import ButtonGroup from "antd/lib/button/button-group";
import { history } from "umi";
import type { ProFormInstance } from "@ant-design/pro-form";
import ProForm, { ProFormSelect, ProFormSwitch } from "@ant-design/pro-form";

const EmailDetail: React.FC = () => {
  const { setAppSettings } = useModel("app-settings");
  const { initialState } = useModel("@@initialState");

  // const [refreshTickMails, setRefreshTickMails] = useState<number>(0);

  useEffect(() => {
    if (initialState?.currentUser) {
      getAppSettings()
        .then((res) => setAppSettings(res))
        .catch(() => Util.error("Failed to fetch app settings. Please try to reload a page!"));
    }
  }, [initialState?.currentUser, setAppSettings]);

  const [loading, setLoading] = useState<boolean>(false);
  const [email, setEmail] = useState<API.Email>();
  // HTML editor ref object.
  // const [editorAcLoading, setEditorAcLoading] = useState<boolean>(false);

  // modals
  const [replyModalVisible, handleReplyModalVisible] = useState<boolean>(false);
  const [createModalVisible, handleCreateModalVisible] = useState<boolean>(false);
  // const [openCaseSelectModal, setOpenCaseSelectModal] = useState<boolean>(false);
  // const [openCaseReasonTextModal, setOpenCaseReasonTextModal] = useState<boolean>(false);
  // const [openOrderSelectModal, setOpenOrderSelectModal] = useState<boolean>(false);

  // hooks
  const { emailId } = useParams<any>();

  const loadEmail = useCallback(
    async (params?: any) => {
      if (!emailId) return;
      setLoading(true);
      return getEmail(sn(emailId), {
        with: "crmCase,orderDetail,parcel",
        includeHidden: 1,
        ...params,
      })
        .then((res) => {
          setEmail(res);
          // setRefreshTickMails((prev) => prev + 1);
        })
        .catch(Util.error)
        .finally(() => setLoading(false));
    },
    [emailId],
  );

  const handlePullEmails = () => {
    const hide = message.loading("Downloading emails...", 0);
    dsPullEmail({})
      .then(() => {
        loadEmail();
        message.success("Downloaded the latest emails successfully.");
      })
      .catch(Util.error)
      .finally(() => hide());
  };

  useEffect(() => {
    loadEmail();
  }, [loadEmail]);

  /**
   * ---------------------------------------------------------------------
   * Toolbar form
   * ---------------------------------------------------------------------
   */
  const toolbarFormRef = useRef<ProFormInstance>();
  useEffect(() => {
    toolbarFormRef.current?.setFieldValue("is_shown", email?.is_hidden != 1);
  }, [email?.is_hidden]);

  /**
   * Prev / Next navigation in Trademark filter
   * @param dir -1 or 1
   */
  const handleEmailNavigation = (dir: number) => {
    setLoading(true);
    const params = {
      boxes: toolbarFormRef.current?.getFieldValue("boxes"),
      visibility: toolbarFormRef.current?.getFieldValue("visibility"),
    };
    Util.setSfValues("sf_email_detail_toolbar", params);

    getNextEmail(dir, emailId, {
      ...params,
      boxes: typeof params.boxes === "string" ? [params.boxes] : params.boxes,
      includeHidden: 1,
    })
      .then((res) => {
        if (res) {
          history.push(`/email/detail/${res}`);
        } else {
          message.info("No next or previous email exists!");
        }
      })
      .catch(Util.error)
      .finally(() => setLoading(false));
  };

  useEffect(() => {
    toolbarFormRef.current?.setFieldsValue(Util.getSfValues("sf_email_detail_toolbar"));
  }, []);

  return (
    <PageContainer title={<>Email Detail</>} style={{ position: "relative" }}>
      <div style={{ position: "absolute", top: 8, left: 175 }}>
        <ProForm formRef={toolbarFormRef} layout="inline" submitter={false}>
          <Row gutter={8}>
            <Col>
              <ProFormSelect
                name="boxes"
                placeholder={"Box"}
                initialValue={"INBOX"}
                options={[
                  { value: "INBOX", label: "Inbox" },
                  { value: "SENT", label: "Sent" },
                ]}
              />
            </Col>
            <Col>
              <ProFormSelect
                name="visibility"
                placeholder={"Visibility"}
                initialValue={"shown"}
                options={[
                  { value: "shown", label: "Shown only" },
                  { value: "hidden", label: "Hidden only" },
                ]}
              />
            </Col>
            <Col flex="auto">
              <ButtonGroup style={{ marginLeft: 12, marginRight: 12 }}>
                <Button onClick={() => handleEmailNavigation(-1)} icon={<ArrowLeftOutlined />} disabled={loading || !email?.id} />
                <Button onClick={() => handleEmailNavigation(1)} icon={<ArrowRightOutlined />} disabled={loading || !email?.id} />
              </ButtonGroup>
            </Col>
            <Col>
              <ProFormSwitch
                name="is_shown"
                label="Visible?"
                tooltip="Update email visibility."
                disabled={!email?.id}
                fieldProps={{
                  checkedChildren: "Visible",
                  unCheckedChildren: "Hidden",
                  onChange(value) {
                    const hide = message.loading("Updating email visibility...", 0);
                    updateEmail({ id: sn(emailId), is_hidden: value ? 0 : 1 })
                      .then((res) => {
                        setEmail((prev) => ({ ...prev, is_hidden: res.is_hidden }));
                      })
                      .catch(Util.error)
                      .finally(() => hide());
                  },
                }}
              />
            </Col>
          </Row>
        </ProForm>
      </div>
      <Spin spinning={loading}>
        <Row gutter={24}>
          <Col xxl={9}>
            <Row gutter={24} style={{ marginBottom: 4 }}>
              <Col flex="150px">
                <Button type="primary" className="btn-green" size="small" onClick={() => window.close()} icon={<CloseOutlined />}>
                  Close
                </Button>
              </Col>
              <Col flex="250px">Email Date: {email?.date ? Util.dtToDMYHHMM(email.date) : null}</Col>
            </Row>
            <Row gutter={24} style={{ marginBottom: 16 }} wrap={false}>
              <Col flex="150px">
                <Button
                  type="primary"
                  size="small"
                  icon={<SendOutlined />}
                  onClick={() => {
                    handleReplyModalVisible(true);
                  }}
                >
                  Reply
                </Button>
                <Button
                  type="primary"
                  size="small"
                  ghost
                  onClick={() => {
                    handleCreateModalVisible(true);
                  }}
                  style={{ marginLeft: 8 }}
                >
                  New
                </Button>
              </Col>
              <Col flex="250px">From: {email?.sender}</Col>
            </Row>
            <Row>
              <Col style={{ margin: "16px 0" }} flex={"1"}>
                <Card
                  size="small"
                  title={
                    <>
                      <MailOutlined />
                      &nbsp; {email?.subject ?? "[No subject]"}
                    </>
                  }
                  extra={
                    <div style={{ fontSize: 16 }}>
                      <AttachmentIconIcon attachments={email?.attachments} />
                    </div>
                  }
                  style={{ width: "100%" }}
                >
                  <div
                    dangerouslySetInnerHTML={{
                      __html: email?.text_html ? email?.text_html : nl2br(email?.text_plain),
                    }}
                  />
                </Card>
              </Col>
            </Row>
          </Col>
          <Col xxl={7}>
            {/* <div style={{ marginBottom: 16 }}>
              <Button
                type="primary"
                size="small"
                ghost
                onClick={handleNoActionNeeded}
                icon={<CloseOutlined />}
                title={`Set the default Status into this case and close`}
              >
                Close - No Action needed
              </Button>
            </div> */}
          </Col>
        </Row>
      </Spin>
      <ReplyForm
        modalVisible={replyModalVisible}
        handleModalVisible={handleReplyModalVisible}
        initialValues={email}
        onSubmit={async () => {
          handlePullEmails();
        }}
        onCancel={() => {
          handleReplyModalVisible(false);
        }}
      />

      <ReplyForm
        mode="create"
        modalVisible={createModalVisible}
        handleModalVisible={handleCreateModalVisible}
        initialValues={email}
        onSubmit={async () => {
          handlePullEmails();
        }}
        onCancel={() => {
          handleCreateModalVisible(false);
        }}
      />
    </PageContainer>
  );
};

export default EmailDetail;

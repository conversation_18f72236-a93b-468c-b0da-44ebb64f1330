<?php

declare(strict_types=1);

namespace App\Controller\Email\EmailAccount;

use Slim\Http\Request;
use Slim\Http\Response;

final class Create extends Base
{
    public function __invoke(Request $request, Response $response): Response
    {
        $input = (array)$request->getParsedBody();

		$this->validate($input);

        /* // Disable: In OAuth mode, we don't need
        if (!($input['password'] ?? ''))
            \App\Exception\Base::raiseInvalidRequest('Password is required!');*/

        $row = $this->emailAccountService->create($input);

        return $this->jsonResponse($response, 'success', $row, 201);
    }
}

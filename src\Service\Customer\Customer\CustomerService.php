<?php

declare(strict_types=1);

namespace App\Service\Customer\Customer;

use App\Lib\FuncDate;
use App\Models\Customer\Customer;
use App\Models\Customer\CustomerAddress;
use App\Models\Customer\CustomerContact;
use App\Service\BaseService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

final class CustomerService extends Base
{
    /**
     * @param Customer  $row
     * @param array $input
     * @return void
     */
    public function saveRelations(&$row, &$input)
    {
        if (key_exists('contacts', $input)) {
            $existedIds = [];
            $dbModels = [];

            foreach ($input['contacts'] as $x) {
                BaseService::trimInputData($x);

                $dbRow = CustomerContact::getBoundData($x);

                $dbRow['id'] = $x['id'] ?? null;
                $dbRow['customer_id'] = $row->id;
                $dbRow['is_active'] = $dbRow['is_active'] ?? 1;

                if ($x['id'] ?? null) {
                    $existedIds[] = $x['id'];
                    $dbRow['updated_on'] = FuncDate::dtNow();
                } else {
                    $dbRow['created_on'] = $dbRow['updated_on'] = FuncDate::dtNow();
                }
                $dbModels[] = $dbRow;
            }
            if ($existedIds) {
                $row->contacts()->whereNotIn('id', $existedIds)->delete();
            }

            $row->contacts()->upsert($dbModels, ['id']);
        }

        if (key_exists('address', $input)) {
            BaseService::trimInputData($input['address']);
            $dbRow = CustomerAddress::getBoundData($input['address'], true);
            $dbRow['customer_id'] = $row->id;

            $row->address()->updateOrInsert(['customer_id' => $row->id], $dbRow);
        }
    }

    public function create(array $input): Customer
    {
        BaseService::trimInputData($input);

        /** @var Customer $row */
        $row =  Customer::create($input);

        $this->saveRelations($row, $input);

        return $row;
    }

    public function update($id, $input): Customer
    {
        BaseService::trimInputData($input);

        /** @var Customer $row */
        $row = Customer::findOrFail($id);

        $row->update($input);

        $this->saveRelations($row, $input);

        return $row;
    }

    public function updateMeta($id, $meta): Customer
    {
        /** @var Customer $row */
        $row = Customer::findOrFail($id);

        /*foreach ($meta as $type => $inputRows) {
            $dbRows = [];
            foreach ($inputRows as $x) {
                $tmp = CustomerMeta::getBoundData($x);
                $tmp['customer_id'] = $id;
                $dbRows[] = $tmp;
            }

            if ($type == CustomerMeta::TYPE_PRODUCT_CATEGORY) {
                $row->productCategories()->delete();
                $row->productCategories()->insert($dbRows);
            } else if ($type == CustomerMeta::TYPE_SUPPLIER) {
                $row->customerType()->delete();
                $row->customerType()->insert($dbRows);
            } else if ($type == CustomerMeta::TYPE_PRODUCT_TRADEMARK) {
                $row->productTrademarks()->delete();
                $row->productTrademarks()->insert($dbRows);
            }
        }

        $row->load('meta');
        */


        return $row;
    }

    /**
     * @param int $id
     * @param array $params
     * @return Customer|Model
     */
    public function getOne(int $id, array $params = []): Customer
    {
        return $this->customerRepository->getQueryBuilder()->where('id', $id)->first();
    }

    public function getCustomersByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($perPage < 1) {
            $perPage = self::DEFAULT_PER_PAGE_PAGINATION;
        }

        $result = $this->customerRepository->getCustomersByPage(
            $page,
            $perPage,
            $params
        );

        return $result;
    }

    /**
     * DownSyncing customers data.
     *
     * @param $params
     * @return void
     */
    public function dsCustomers($params=[])
    {
        // Customer data.
        $maxUpdatedOn = Customer::query()->max('updated_on') ?? '1900-01-01';

        OrgCustomer::query()->where('updated_on', '>=', $maxUpdatedOn)->chunk(500, function (Collection $items){
            Customer::upsert($items->toArray(), ['id']);
        });


        /*$maxUpdatedOn = CustomerInfo::query()->max('updated_on') ?? '1900-01-01';
        OrgCustomerInfo::query()->where('updated_on', '>=', $maxUpdatedOn)->chunk(500, function (Collection $items){
            CustomerInfo::upsert($items->toArray(), ['id']);
        });

        $maxUpdatedOn = CustomerComment::query()->max('created_on') ?? '1900-01-01';
        OrgCustomerComment::query()->where('created_on', '>=', $maxUpdatedOn)->chunk(500, function (Collection $items){
            CustomerComment::upsert($items->toArray(), ['id']);
        });*/
    }

    public function delete($ids): void
    {
        $arr = is_array($ids) ? $ids : explode(',', $ids);
        $this->customerRepository->getQueryBuilder()->whereIn('id', $arr)->delete();
    }
}

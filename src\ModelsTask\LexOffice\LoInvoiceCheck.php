<?php

namespace App\ModelsTask\LexOffice;

use App\Lib\Func;
use App\Models\BaseModel;
use App\Models\Casts\Csv;
use App\Models\Supplier\Supplier;
use App\ModelsOrg\OrgSupplier;

/**
 * @property string $lo_id
 * @property integer $org_customer_id
 * @property string $lo_no
 * @property string $lo_type
 * @property string $lo_status
 * @property string $lo_document_file_id
 * @property float $lo_version
 * @property integer $lo_cust_no
 * @property string $lo_voucher_date
 * @property float $total_gross_amount
 * @property string $detail
 * @property string $lo_created_on
 * @property string $lo_updated_on
 * @property string $lo_contact_id
 * @property string $lo_contact_name
 * @property boolean $lo_archived
 * @property string $lo_currency
 * @property integer $is_checked
 * @property integer $is_stat_checked
 * @property integer $is_exported
 * @property string $note
 * @property string $order_out_nos
 *
 */
class LoInvoiceCheck extends BaseModel
{
    public $timestamps = false;

    protected $connection = TASK_DB;

    public $casts = ['detail' => 'array', 'order_out_nos' => Csv::class];

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'lo_invoice_check';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'lo_id';

    /**
     * The "type" of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * Indicates if the IDs are auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * @var array
     */
    protected $fillable = ['lo_id', 'org_customer_id', 'lo_no', 'lo_type', 'lo_status', 'lo_document_file_id', 'lo_version', 'lo_cust_no', 'lo_voucher_date', 'total_gross_amount', 'detail', 'lo_created_on', 'lo_updated_on', 'lo_contact_id', 'lo_contact_name', 'lo_archived', 'lo_currency'
        , 'is_checked'
        , 'is_stat_checked'
        , 'is_exported'
        , 'note'
        , 'order_out_nos'
    ];

    public static function getBoundData(&$item, $applyReverseCast = true): array
    {
        /*
        {
            "id": "25a01f8c-72fb-4294-8917-710f88b71201",
            "voucherType": "quotation",
            "voucherStatus": "draft",
            "voucherNumber": "AG0033",
            "voucherDate": "2023-01-25T00:00:00.000+01:00",
            "createdDate": "2023-01-25T17:40:57.000+01:00",
            "updatedDate": "2023-01-25T17:41:38.000+01:00",
            "contactId": "49f8a987-f86c-4d0b-b2bd-1e55c747b1ca",
            "contactName": "Max SW1 Web Solution",
            "totalAmount": 5.98,
            "currency": "EUR",
            "archived": false
        },
         */

        $tmpObj = new static($item);
        $tmpObj->lo_id = $item['id'];
        $tmpObj->lo_type = $item['voucherType'];
        $tmpObj->lo_status = $item['voucherStatus'];
        $tmpObj->lo_no = $item['voucherNumber'];
        $tmpObj->lo_voucher_date = isset($item['voucherDate']) ? substr(Func::dtTzStrToDBFormat($item['voucherDate']), 0, 10) : null;
        $tmpObj->lo_created_on = Func::dtTzStrToDBFormat($item['createdDate']);
        $tmpObj->lo_updated_on = Func::dtTzStrToDBFormat($item['updatedDate']);
        $tmpObj->lo_contact_id = $item['contactId'] ?? null;
        $tmpObj->lo_contact_name = $item['contactName'] ?? null;
        $tmpObj->total_gross_amount = $item['totalAmount'] ?? null;
        $tmpObj->lo_version = $item['version'] ?? null;
        $tmpObj->lo_archived = $item['archived'] ?? null;
        $tmpObj->lo_currency = $item['currency'] ?? null;

        $attributes = $tmpObj->getFillable();
        $data = [];
        foreach ($attributes as $key) {
            $data[$key] = $tmpObj->getAttributeValue($key);
            if ($applyReverseCast) {
                if ($tmpObj->hasCast($key, 'array')) {
                    $data[$key] = $data[$key] ? json_encode($data[$key]) : null;
                } else if (($tmpObj->getCasts()[$key] ?? null) == Csv::class) {
                    $data[$key] = $data[$key] ? implode(',', $data[$key]) : null;
                }
            }
        }

        return $data;
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function orgCustomer()
    {
        return $this->belongsTo(\App\Models\Customer::class, 'org_customer_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function loContact()
    {
        return $this->belongsTo(\App\Models\LexOffice\LoContact::class, 'lo_contact_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOneThrough
     */
    public function orderOut()
    {
        return $this->hasOneThrough(
            \App\Models\OrderOut::class,
            \App\Models\LexOffice\LoOrderOutInvoice::class,
            'lo_id',
            'order_no',
            'lo_id',
            'order_no',
        );
    }

    public function scopeValidOnly($builder)
    {
        // to do
        return $builder->whereIn('lo_type', ['invoice', 'creditnote'])
            ->whereNotIn('lo_status', ['draft', 'voided']);
    }

    /**
     * @Attension: We should provide import_source in where clause!!!
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function finDetails()
    {
        return $this->hasMany(FinDetail::class,
            'import_source_id',
            'lo_id'
        );
    }

    /**
     * Dummy relation to be used in Union query
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function suppSupplier()
    {
        return $this->hasOne(Supplier::class, 'id', 'supp_supplier_id');
    }

    /**
     * Dummy relation to be used in Union query
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function orgSupplier()
    {
        return $this->hasOne(OrgSupplier::class, 'supp_supplier_id', 'supp_supplier_id');
    }
}

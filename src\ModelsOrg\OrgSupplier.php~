<?php

namespace App\ModelsOrg;


/**
 * @property string $id
 * @property string $name
 * @property string $org_a
 * @property integer $supp_supplier_id
 * @property string $created_on
 * @property string $updated_on
 *
 *
 * @property OrgSupplierInfo $info
 * @property OrgSupplierComment[] $comments
 *
 */
class OrgSupplier extends BaseModel
{
    use \Staudenmeir\EloquentEagerLimit\HasEagerLimit;

    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'suppliers';

    /**
     * The "type" of the auto-incrementing ID.
     * 
     * @var string
     */
    protected $keyType = 'string';

    /**
     * Indicates if the IDs are auto-incrementing.
     * 
     * @var bool
     */
    public $incrementing = false;

    /**
     * @var array
     */
    protected $fillable = ['name', 'org_a', 'supp_supplier_id', 'created_on', 'updated_on'];

    public function info(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne(OrgSupplierInfo::class, 'supplier_id', 'id');
    }

    public function comments(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(OrgSupplierComment::class, 'supplier_id', 'id');
    }

    public function top3Comments(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(OrgSupplierComment::class, 'supplier_id', 'id')
            ->latest('created_on')
            ->limit(3);
    }

    public function offers(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(OrgOffer::class, 'supplier_id', 'id');
    }

}

<?php

declare(strict_types=1);

namespace App\Controller\User;

use App\Exception\User;
use Slim\Http\Request;
use Slim\Http\Response;

final class UpdatePartial extends Base
{
    public function __invoke(Request $request, Response $response, array $args): Response
    {
        $user_id = $args['id'] ?? null;
        if (!$user_id) {
            throw new User('User not found', 404);
        }
        $user_id = intval($user_id);

        $input = (array)$request->getParsedBody();

        unset($input['password']);
        unset($input['role']);
        unset($input['status']);
        unset($input['username']);
        unset($input['email']);

        $user = $this->getUpdateUserService()->update($input, $user_id);

        return $this->jsonResponse($response, 'success', $user, 200);
    }
}

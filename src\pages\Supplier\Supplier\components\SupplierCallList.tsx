import { SupplierCallDirection, SupplierCallDirectionKv, SupplierCallType, SupplierCallTypeKv } from "@/constants";
import CreateForm from "@/pages/Supplier/SupplierCall/components/CreateForm";
import UpdateForm from "@/pages/Supplier/SupplierCall/components/UpdateForm";
import { deleteSupplierCall, getSupplierCallListByPage } from "@/services/app/Supplier/supplier-call";
import Util, { ni, nl2br, sn, urlFull } from "@/util";
import {
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  FileTextOutlined,
  HeartOutlined,
  MailOutlined,
  PhoneOutlined,
  SendOutlined,
  UserOutlined,
} from "@ant-design/icons";
import { ActionType, ProColumns, ProTable } from "@ant-design/pro-components";
import { Button, Checkbox, Col, message, Popconfirm, Popover, Radio, Row, Space, Typography } from "antd";
import { useEffect, useRef, useState } from "react";
import CreateEmailForm from "@/pages/Email/EmailList/components/CreateEmailForm";
import EditableCell from "@/components/EditableCell";
import { updateSupplierExtBySupplierId } from "@/services/app/Supplier/supplier-ext";
import useUserOptions from "@/hooks/BasicData/useUserOptions";
import SupplierCallFilesIcon from "../../SupplierCall/components/SupplierCallFilesIcon";
import SNotesViewerModal from "@/components/SNotesViewerModal";

export type SearchFormValueType = Partial<API.SupplierCall>;

type RowType = API.SupplierCall;

export const SupplierCallTypeComp = ({ type, internal }: { type?: number; internal?: boolean }) => {
  if (type) {
    if (type == SupplierCallType.Email) {
      return <MailOutlined title="Email" />;
    } else if (type == SupplierCallType.Phone) {
      return <PhoneOutlined title="Phone" />;
    } else if (type == SupplierCallType.Meeting) {
      return <UserOutlined title="Meeting" />;
    } else if (type == SupplierCallType.Notes) {
      return <FileTextOutlined title="Notes" />;
    } else if (type == SupplierCallType.Offer) {
      return <HeartOutlined title="Offer" />;
    } else return SupplierCallTypeKv[type];
  }
  return null;
};

type SupplierCallListProps = {
  supplier?: API.Supplier;
  loadSupplierDetail?: () => void;
};

const SupplierCallList: React.FC<SupplierCallListProps> = (props) => {
  const { supplier, loadSupplierDetail } = props;
  const { id, ext: supplierExt } = supplier || {};

  const actionRef = useRef<ActionType>();

  const [currentRow, setCurrentRow] = useState<RowType>();
  const [loading, setLoading] = useState(false);

  const [createModalVisible, handleCreateModalVisible] = useState<boolean>(false);
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);

  const [replyModalVisible, handleReplyModalVisible] = useState<boolean>(false);
  const [openStaffEmailModal, setOpenStaffEmailModal] = useState<boolean>(false);

  const [inc_internal_email, set_inc_internal_email] = useState<boolean>(false);
  const [offer_mode, set_offer_mode] = useState<string>("inc");

  const [notesModalVisible, setNotesModalVisible] = useState<boolean>(false);

  const columns: ProColumns<RowType>[] = [
    {
      title: "Created on",
      dataIndex: ["date"],
      defaultSortOrder: "descend",
      sorter: true,
      width: 120,
      align: "center",
      ellipsis: true,
      render: (dom, record) => Util.dtToDMYHHMMTz(record.date),
    },
    {
      title: "Type",
      dataIndex: ["type"],
      sorter: true,
      width: 100,
      ellipsis: true,
      align: "center",
      render: (__, record) => {
        return <SupplierCallTypeComp type={record.type} internal={record.uid?.startsWith("call_")} />;
      },
    },
    {
      title: "",
      dataIndex: ["direction"],
      sorter: true,
      width: 80,
      ellipsis: true,
      align: "center",
      render: (dom, record) => {
        return record.direction ? (SupplierCallDirectionKv[record.direction] ?? record.direction) : null;
      },
    },
    {
      title: "Notes",
      dataIndex: ["note"],
      ellipsis: true,
      width: 300,
      render: (__, record) => {
        return record?.note ? (
          <Row wrap={false}>
            <Col flex={"auto"}>
              <Typography.Text ellipsis>{Util.stripTags(record.note)}</Typography.Text>
            </Col>
            <Col flex="20px">
              <Popover
                title={`${SupplierCallTypeKv[record.type || 0]}${record.direction ? ` / ${SupplierCallDirectionKv[record.direction]}` : ""}`}
                content={<div dangerouslySetInnerHTML={{ __html: nl2br(record.note) }} style={{ maxHeight: 600, overflowY: "auto" }}></div>}
                trigger={["hover"]}
                styles={{ root: { maxWidth: 1200 } }}
                placement="right"
              >
                <div>
                  <EyeOutlined
                    className="c-blue cursor-pointer"
                    onClick={() => {
                      setCurrentRow(record);
                      setNotesModalVisible(true);
                    }}
                  />
                </div>
              </Popover>
            </Col>
          </Row>
        ) : null;
      },
    },
    /* {
      title: 'Trademark / Products',
      dataIndex: ['offer_no2'],
      width: 250,
      render: (__, record) => {
        return record.uid?.includes('call_') ? (
          <Space direction="vertical">
            <div>
              {record.trademarks?.map((x) => (
                <Tag key={x.id} color="cyan">
                  {x.name}
                </Tag>
              ))}
            </div>
            <div>
              {record.products?.map((x) => (
                <Tag key={x.id} color="purple">
                  {x.name}
                </Tag>
              ))}
            </div>
          </Space>
        ) : null;
      },
    }, */
    {
      title: "Offer No",
      dataIndex: ["offer_no"],
      ellipsis: true,
      width: 120,
      render: (__, record) => {
        return record.offer_no;
        /* return record.type != SupplierCallType.Offer && record.offer_id ? (
          <Typography.Link
            href={`${FSONE_PIM_URL}/quotes/offer-item?offer_id=${record.offer_id}&offer_no=${record.offer_no}`}
            title="Open GFC PIM Dashboard in new tab"
            target="_blank"
          >
            {record.offer_no}
          </Typography.Link>
        ) : null; */
      },
    },
    {
      title: "Subject / Comment",
      dataIndex: ["email_subject"],
      width: 300,
      ellipsis: true,
      render(__, entity) {
        return entity.uid?.includes("call_") ? (entity.org_offer ? entity.org_offer?.offer : entity.comment) : entity.email_subject;
      },
    },
    {
      title: "",
      dataIndex: "files",
      sorter: false,
      width: 20,
      hideInSearch: true,
      render(__, record) {
        return <SupplierCallFilesIcon files={record.files} />;
      },
    },
    {
      title: "First Opening",
      dataIndex: ["email_open_first_seen_on"],
      width: 120,
      ellipsis: true,
      render: (__, record) => Util.dtToDMYHHMMTz(record.email_open_first_seen_on),
    },
    {
      title: "How Often Opened",
      dataIndex: ["date"],
      width: 80,
      ellipsis: true,
      align: "right",
      render: (__, record) => ni(record.email_open_count),
    },
    {
      valueType: "option",
      render(__, entity) {
        return (
          <Space size={0}>
            {entity.uid?.startsWith("call_") && (
              <Button
                type="link"
                color="primary"
                icon={<EditOutlined color="primary" />}
                onClick={() => {
                  setCurrentRow(entity);
                  handleUpdateModalVisible(true);
                }}
              />
            )}
            {/* {entity.uid?.startsWith("call_") && (
              <Popconfirm
                className="cursor-pointer c-red"
                title={<div>Are you sure you want to delete?</div>}
                okText="Yes"
                cancelText="No"
                styles={{ root: { width: 300 } }}
                onConfirm={async () => {
                  const hide = message.loading("Deleting a selected log...", 0);
                  deleteSupplierCall(entity.id)
                    .then(() => {
                      message.success("Deleted successfully.");
                      actionRef.current?.reload();
                    })
                    .catch(Util.error)
                    .finally(() => hide());
                }}
              >
                <div>
                  <Button type="link" danger icon={<DeleteOutlined color="danger" />} />
                </div>
              </Popconfirm>
            )} */}
          </Space>
        );
      },
    },
  ];

  useEffect(() => {
    if (id) {
      actionRef.current?.reload();
    }
  }, [id]);

  const { userOptions } = useUserOptions();
  const userId = supplierExt?.user?.user_id;
  const initials = supplierExt?.user?.initials ?? supplierExt?.user?.username ?? "Select";

  useEffect(() => {
    actionRef.current?.reload();
  }, [inc_internal_email]);

  useEffect(() => {
    actionRef.current?.reload();
  }, [offer_mode]);

  return (
    <>
      <ProTable<RowType, API.PageParams>
        headerTitle={
          <Space size={32}>
            <span>Conversations</span>
            <div style={{ fontWeight: "normal" }}>
              <label htmlFor="inc_internal_email" style={{ marginRight: 8 }}>
                Include Internal Email?
              </label>
              <Checkbox
                id="inc_internal_email"
                defaultChecked={false}
                checked={inc_internal_email}
                onChange={(e) => set_inc_internal_email(e.target.checked)}
              />
            </div>
            <div style={{ fontWeight: "normal" }}>
              <label htmlFor="offer_mode" style={{ marginRight: 8 }}>
                Offer
              </label>
              <Radio.Group
                id="offer_mode"
                onChange={(e) => set_offer_mode(e.target.value)}
                value={offer_mode}
                options={[
                  { value: "inc", label: "Inc" },
                  { value: "exc", label: "Exc" },
                  { value: "only", label: "Only" },
                ]}
              />
            </div>
          </Space>
        }
        toolBarRender={() => [
          <Space key="action-buttons" size={48} style={{ marginRight: 8 }}>
            <Button
              key="new"
              type="primary"
              onClick={() => {
                handleCreateModalVisible(true);
              }}
            >
              New
            </Button>
            <Space>
              <Button
                key="reply_mail"
                type="primary"
                icon={<MailOutlined />}
                title={`Send email to ${supplier?.name}`}
                onClick={() => {
                  handleReplyModalVisible(true);
                }}
              >
                New Email
              </Button>
              <Button
                key="send_mail_to_staff"
                type="primary"
                icon={<SendOutlined />}
                title={`Send email to ${initials}`}
                onClick={() => {
                  setOpenStaffEmailModal(true);
                }}
              >
                Staff Email
              </Button>
              <div style={{ width: 70 }}>
                <EditableCell
                  dataType="select"
                  defaultValue={userId}
                  style={{ marginRight: 0 }}
                  fieldProps={{ style: { lineHeight: 1 } }}
                  dataOptions={userOptions}
                  isDefaultEditing={false}
                  showSearch
                  allowClear
                  triggerUpdate={async (newValue: any, cancelEdit) => {
                    if (newValue == userId) {
                      cancelEdit?.();
                      return;
                    }

                    const hide = message.loading("Saving...", 0);
                    return updateSupplierExtBySupplierId(supplier?.id, {
                      user_id: newValue ?? null,
                    })
                      .then((res) => {
                        message.success("Updated successfully.");
                        loadSupplierDetail?.();
                        cancelEdit?.();
                      })
                      .catch(Util.error)
                      .finally(() => {
                        setLoading(false);
                        hide();
                      });
                  }}
                >
                  <span className="c-blue cursor-pointer">{initials}</span>
                </EditableCell>
              </div>
            </Space>
          </Space>,
        ]}
        actionRef={actionRef}
        rowKey="uid"
        size="small"
        revalidateOnFocus={false}
        options={{ fullScreen: true, density: false, setting: false }}
        search={false}
        sticky
        scroll={{ x: 800 }}
        pagination={{
          defaultPageSize: sn(Util.getSfValues("cu_sf_supplier_call_list_p")?.pageSize ?? 20),
        }}
        request={async (params, sort, filter) => {
          const searchFormValues = {};
          Util.setSfValues("cu_sf_supplier_call_list", searchFormValues);
          Util.setSfValues("cu_sf_supplier_call_list_p", params);

          setLoading(true);
          return getSupplierCallListByPage(
            {
              ...params,
              with: "mergeEmail",
              supplier_id: id,
              inc_internal_email,
              offer_mode,
            },
            sort,
            filter,
          )
            .then((res) => {
              if (currentRow?.id) {
                setCurrentRow(res.data.find((x) => x.id == currentRow.id));
              }
              return res;
            })
            .finally(() => setLoading(false));
        }}
        onRequestError={Util.error}
        columns={columns}
        columnEmptyText=""
      />

      <CreateForm
        initialValues={{
          supplier_id: id,
          type: SupplierCallType.Phone,
          direction: SupplierCallDirection.Out,
        }}
        modalVisible={createModalVisible}
        handleModalVisible={handleCreateModalVisible}
        onSubmit={(value) => {
          actionRef.current?.reload();
        }}
      />

      <UpdateForm
        initialValues={{ ...currentRow }}
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        onSubmit={(value) => {
          actionRef.current?.reload();
        }}
      />

      <CreateEmailForm
        modalVisible={openStaffEmailModal}
        handleModalVisible={setOpenStaffEmailModal}
        initialValues={{
          receiver: Util.emailBuildSender(supplierExt?.user?.email, supplierExt?.user?.name),
          text_html: `<br /><a href="${urlFull()}suppliers/detail/${supplier?.id}" target="_blank">View Supplier detail: ${supplier?.name} </a>`,
        }}
        htmlEditorId="send_email_whc"
        internal
        supplier={{
          id: supplier?.id,
          name: supplier?.name,
          address: supplier?.address,
          contacts: supplier?.contacts,
          created_on: supplier?.created_on,
        }}
      />

      <CreateEmailForm
        modalVisible={replyModalVisible}
        handleModalVisible={handleReplyModalVisible}
        initialValues={{
          receiver: supplier?.contacts?.[0] ? `${supplier.contacts[0].fullname} <${supplier.contacts[0].email}>` : "",
        }}
        supplier={{
          id: supplier?.id,
          name: supplier?.name,
          address: supplier?.address,
          contacts: supplier?.contacts,
          created_on: supplier?.created_on,
        }}
        onCancel={() => {
          handleReplyModalVisible(false);
        }}
      />

      <SNotesViewerModal
        id="supplier-call-notes-viewer"
        title={`${SupplierCallTypeKv[currentRow?.type || 0]}${currentRow?.direction ? ` / ${SupplierCallDirectionKv[currentRow?.direction]}` : ""} Notes`}
        content={currentRow?.note}
        modalVisible={notesModalVisible}
        handleModalVisible={setNotesModalVisible}
      />
    </>
  );
};

export default SupplierCallList;

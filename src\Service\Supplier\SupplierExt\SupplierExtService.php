<?php

declare(strict_types=1);

namespace App\Service\Supplier\SupplierExt;

use App\Models\Supplier\SupplierExt;

final class SupplierExtService extends Base
{
    public function create(array $input): SupplierExt
    {
        return SupplierExt::create($input);
    }

    public function update($id, $input): SupplierExt
    {
        $row = SupplierExt::findOrFail($id);
        $row->update($input);
        return $row;
    }

    public function updateBySupplierId($id, $input): SupplierExt
    {
        /** @var SupplierExt $row */
        $row = SupplierExt::query()->firstOrNew(['supplier_id' => $id], $input);
        $row->fill($input);
        $row->save();
        return $row;
    }

    public function getOne(int $id, array $params=[]): SupplierExt
    {
        return $this->supplierExtRepository->getQueryBuilder()->where('id', $id)->first();
    }

    public function getSupplierExtsByPage(
        int $page,
        int $perPage,
        ?array $params
    ): array
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($perPage < 1) {
            $perPage = self::DEFAULT_PER_PAGE_PAGINATION;
        }

        return $this->supplierExtRepository->getSupplierExtsByPage(
            $page,
            $perPage,
            $params
        );
    }

    public function delete($ids): void
    {
        $arr = is_array($ids) ? $ids : explode(',', $ids);
        $this->supplierExtRepository->getQueryBuilder()->whereIn('id', $arr)->delete();
    }
}

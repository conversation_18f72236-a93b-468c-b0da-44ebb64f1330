<?php

declare(strict_types=1);

namespace App\Controller\Email\EmailTemplateCategory;

use App\Controller\BaseController;
use App\Service\Email\EmailTemplateCategory\EmailTemplateCategoryService;
use Respect\Validation\Validator as v;
use Slim\Container;

abstract class Base extends BaseController
{
    public EmailTemplateCategoryService $emailTemplateCategoryService;

    public function __construct(Container $container)
    {
        parent::__construct($container);
        $this->emailTemplateCategoryService = $container->get(EmailTemplateCategoryService::class);
    }

	public function validate(array $input): bool {
        $validator = v::key('cat1', v::stringType()->length(1, 255));

        if (!$validator->validate($input)) {
            \App\Exception\Base::raiseInvalidRequest();
        }
        return true;
    }
}

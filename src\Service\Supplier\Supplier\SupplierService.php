<?php

declare(strict_types=1);

namespace App\Service\Supplier\Supplier;

use App\Lib\Func;
use App\Lib\FuncDate;
use App\Models\Email\Email;
use App\Models\Email\EmailTemplate;
use App\Models\Supplier\Supplier;
use App\Models\Supplier\SupplierAddress;
use App\Models\Supplier\SupplierContact;
use App\Models\Supplier\SupplierMeta;
use App\ModelsOrg\OrgSupplier;
use App\Service\BaseService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Query\JoinClause;

final class SupplierService extends Base
{
    /**
     * @param Supplier  $row
     * @param array $input
     * @return void
     */
    public function saveRelations(&$row, &$input)
    {
        if (key_exists('contacts', $input)) {
            $existedIds = [];
            $dbModels = [];

            foreach ($input['contacts'] as $x) {
                BaseService::trimInputData($x);

                $dbRow = SupplierContact::getBoundData($x);

                $dbRow['id'] = $x['id'] ?? null;
                $dbRow['supplier_id'] = $row->id;
                $dbRow['is_active'] = $dbRow['is_active'] ?? 1;

                if ($x['id'] ?? null) {
                    $existedIds[] = $x['id'];
                    $dbRow['updated_on'] = FuncDate::dtNow();
                } else {
                    $dbRow['created_on'] = $dbRow['updated_on'] = FuncDate::dtNow();
                }
                $dbModels[] = $dbRow;
            }
            if ($existedIds) {
                $row->contacts()->whereNotIn('id', $existedIds)->delete();
            }

            $row->contacts()->upsert($dbModels, ['id']);
        }

        if (key_exists('address', $input)) {
            BaseService::trimInputData($input['address']);
            $dbRow = SupplierAddress::getBoundData($input['address'], true);
            $dbRow['supplier_id'] = $row->id;

            $row->address()->updateOrInsert(['supplier_id' => $row->id], $dbRow);
        }
    }

    public function create(array $input): Supplier
    {
        BaseService::trimInputData($input);

        /** @var Supplier $row */
        $row =  Supplier::create($input);

        $this->saveRelations($row, $input);

        return $row;
    }

    public function update($id, $input): Supplier
    {
        BaseService::trimInputData($input);

        /** @var Supplier $row */
        $row = Supplier::findOrFail($id);

        $row->update($input);

        $this->saveRelations($row, $input);

        return $row;
    }

    public function updateMeta($id, $meta): Supplier
    {
        /** @var Supplier $row */
        $row = Supplier::findOrFail($id);

        foreach ($meta as $type => $inputRows) {
            $dbRows = [];
            foreach ($inputRows as $x) {
                $tmp = SupplierMeta::getBoundData($x);
                $tmp['supplier_id'] = $id;
                $dbRows[] = $tmp;
            }

            if ($type == SupplierMeta::TYPE_PRODUCT_CATEGORY) {
                $row->productCategories()->delete();
                $row->productCategories()->insert($dbRows);
            } else if ($type == SupplierMeta::TYPE_SUPPLIER) {
                $row->supplierType()->delete();
                $row->supplierType()->insert($dbRows);
            } else if ($type == SupplierMeta::TYPE_PRODUCT_TRADEMARK) {
                $row->productTrademarks()->delete();
                $row->productTrademarks()->insert($dbRows);
            }
        }

        $row->load('meta');

        return $row;
    }

    /**
     * @param int $id
     * @param array $params
     * @return Supplier|Model
     */
    public function getOne(int $id, array $params = []): Supplier
    {
        return $this->supplierRepository->getQueryBuilder()->where('id', $id)->first();
    }

    public function getSuppliersByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($perPage < 1) {
            $perPage = self::DEFAULT_PER_PAGE_PAGINATION;
        }

        $result = $this->supplierRepository->getSuppliersByPage(
            $page,
            $perPage,
            $params
        );

        return $result;
    }

    /**
     * DownSyncing suppliers data.
     *
     * @param $params
     * @return void
     */
    public function dsSuppliers($params=[])
    {
        // Supplier data.
        $maxUpdatedOn = Supplier::query()->max('updated_on') ?? '1900-01-01';

        OrgSupplier::query()->where('updated_on', '>=', $maxUpdatedOn)->chunk(500, function (Collection $items){
            Supplier::upsert($items->toArray(), ['id']);
        });


        /*$maxUpdatedOn = SupplierInfo::query()->max('updated_on') ?? '1900-01-01';
        OrgSupplierInfo::query()->where('updated_on', '>=', $maxUpdatedOn)->chunk(500, function (Collection $items){
            SupplierInfo::upsert($items->toArray(), ['id']);
        });

        $maxUpdatedOn = SupplierComment::query()->max('created_on') ?? '1900-01-01';
        OrgSupplierComment::query()->where('created_on', '>=', $maxUpdatedOn)->chunk(500, function (Collection $items){
            SupplierComment::upsert($items->toArray(), ['id']);
        });*/
    }

    public function getSupplier2EmailMatrixByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($perPage < 1) {
            $perPage = self::DEFAULT_PER_PAGE_PAGINATION;
        }

        $query = $this->supplierRepository->getQuerySuppliersByPage($params);
        $query->orderBy('supplier.created_on', 'DESC');

        $total = $this->supplierRepository->getCountByQuery($query);

        $result = $this->supplierRepository->getResultsWithPagination(
            $query,
            $page,
            $perPage,
            $total
        );

        if ($total) {
            $ids = [];  // supplier ids.
            foreach ($result['data'] as $x) {
                $ids[] = $x['id'];
            }

            // Getting last_seen_on per email and template id.
            $queryEmailTpl = EmailTemplate::query()
                ->join('email', 'email.email_template_id', '=', 'email_template.id')
                ->join('email_receiver', 'email_receiver.email_id', '=', 'email.id')
                ->join('supplier_contact', 'supplier_contact.email', '=', 'email_receiver.email')
            ;

            $queryEmailTpl
                ->select([])
                ->selectRaw("supplier_contact.supplier_id")
                ->selectRaw("email_template.id")
                // ->selectRaw("MAX(email_receiver.open_seen_updated_on) AS last_seen_on")
                // ->selectRaw("MAX(email.date) AS last_sent_on")
                ->selectRaw("COALESCE(MAX(email_receiver.open_seen_updated_on), MAX(email.date)) AS last_seen_on");

            if ($params['category_id'] ?? null) {
                $queryEmailTpl->where('category_id', $params['category_id']);
            }
            $queryEmailTpl->whereIn('supplier_contact.supplier_id', $ids);

            $queryEmailTpl->groupBy('supplier_contact.supplier_id');
            $queryEmailTpl->groupBy('email_template.id');

            // Getting full entry.
            $queryMain = Email::query()
                ->select([])
                ->selectRaw("email.id")
                ->selectRaw("supplier_contact.supplier_id")
                ->selectRaw("email.email_template_id")
                ->selectRaw("email_receiver.*")
                ->selectRaw("email.subject")
                ->selectRaw("email.date AS sent_on")
                ->selectRaw("t.last_seen_on")
                ->join('email_receiver', 'email_receiver.email_id', '=', 'email.id')
                ->join('supplier_contact', 'supplier_contact.email', '=', 'email_receiver.email')
                ->joinSub($queryEmailTpl, 't', function (JoinClause $joinClause) {
                    $joinClause
                        ->whereColumn('t.id', 'email.email_template_id')
                        ->whereColumn('t.supplier_id', 'supplier_contact.supplier_id')
                        ->whereRaw('t.last_seen_on = COALESCE(email_receiver.open_seen_updated_on, email.date)');
                });
            $queryMain->whereIn('supplier_contact.supplier_id', $ids);

            $queryEmailTpl->groupBy('supplier_contact.supplier_id');
            $queryEmailTpl->groupBy('email.email_template_id');

            $tmp = $queryMain->get()->toArray();
            $map2 = [];
            foreach ($tmp as $x) {
                $map2[$x['supplier_id']][$x['email_template_id']] = $x;
            }

            foreach ($result['data'] as &$x) {
                // $x['matrix'] = $map1[$x['email']] ?? null;
                $x['matrix2'] = $map2[$x['id']] ?? null;
            }
        }


        // Loading Templates
        $queryTpl = EmailTemplate::query();
        if ($params['category_id'] ?? null) {
            $queryTpl->where('category_id', $params['category_id']);
        }

        $queryTpl
            ->select('id')
            ->addSelect('sort')
            ->addSelect('title')
            ->with('category')
            ->sortDefault('ascend');

        $result['templates'] = $queryTpl->get()->toArray();

        return $result;
    }

    public function delete($ids): void
    {
        $arr = is_array($ids) ? $ids : explode(',', $ids);
        $this->supplierRepository->getQueryBuilder()->whereIn('id', $arr)->delete();
    }
}

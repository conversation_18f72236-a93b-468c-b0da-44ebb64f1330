<?php

declare(strict_types=1);

namespace App\Controller;

use App\App\Constants;
use App\Exception\BaseException;
use App\Lib\FileLib;
use App\Lib\Func;
use App\Lib\LoggerMessageFormatter;
use App\Models\Email\Email;
use App\Models\Email\EmailReceiver;
use App\Models\Supplier\Supplier;
use App\Models\Sys\SysLog;
use App\ModelsOrg\OrgOffer;
use App\ModelsTask\LexOffice\LoInvoiceCheck;
use App\Service\Ftp\DownloadAndImportRwSupplierXlsService;
use App\Service\Sys\SysLog\SysLogService;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Middleware;
use GuzzleRetry\GuzzleRetryMiddleware;
use Illuminate\Database\Connection;
use LimitIterator;
use Slim\Http\Request;
use Slim\Http\Response;
use SplFileObject;

final class DefaultController extends BaseController
{
    private const API_VERSION = '1.0.0';

    public function getHelp(Request $request, Response $response): Response
    {
        $app = $this->container->get('settings')['app'];
        $url = $app['domain'];
        $endpoints = [
            'status' => $url . '/status',
            // 'this help' => $url . '',
        ];
        $message = [
            'endpoints' => $endpoints,
            'version' => self::API_VERSION,
            'timestamp' => time(),
        ];

        return $this->jsonResponse($response, 'success', $message, 200);
    }

    public function getStatus(Request $request, Response $response): Response
    {
        $status = [
            'version' => self::API_VERSION,
            'MySQL' => 'OK',
            'Redis' => $this->checkRedisConnection(),
            'timestamp' => time(),
            'stats' => $this->getDbStats(),
            'libraries' => $this->getLibrariesStats(),
            'FTP: RW Supplier' => $this->getFtpRwStatus(),
        ];

        $params = $request->getParams();
        $mode = $params['mode'] ?? null;
        if ($mode == 'org') {
            $status['offer_cnt'] = OrgOffer::query()->count();
        } else if ($mode == 'supplier') {
            $status['supplier_cnt'] = Supplier::query()->count();
        } else if ($mode == 'task') {
            $status['task_cnt'] = LoInvoiceCheck::query()->count();
        }

        return $this->jsonResponse($response, 'success', $status, 200);
    }

    public function getStatusBase(Request $request, Response $response): Response
    {
        $status = [
            'version' => self::API_VERSION,
            'MySQL' => 'OK',
            'Redis' => $this->checkRedisConnection(),
            'timestamp' => time(),
            'stats' => $this->getDbStats(),
            'libraries' => $this->getLibrariesStats(),
        ];

        return $this->jsonResponse($response, 'failed', $status, 200);
    }

    private function getDbStats(): array
    {
        /** @var \App\Service\User\Find $subscriberService */
        $subscriberService = $this->container->get('find_user_service');

        return [
            'users' => $subscriberService->getUserRepository()->getCount(),
            'API mode' => $this->container->get('settings')['mode'],
            'API Error Level' => $this->container->get('settings')['displayErrorDetails'],
            'API Logging Level' => $this->container->get('settings')['app']['logLevel'],
        ];
    }

    private function getLibrariesStats(): array
    {
        return [
            'PHP Version' => phpversion(),
            'openssl' => function_exists('openssl_encrypt'),
            'IMAP' => function_exists('imap_open'),
            'IMAP 3rd party' => class_exists('\PhpImap\Mailbox'),
        ];
    }

    private function getFtpRwStatus(): array
    {
        $ret = ['status' => 'OK', 'filesCount' => null];
        try {
            /** @var DownloadAndImportRwSupplierXlsService $service */
            $service = $this->container->get(DownloadAndImportRwSupplierXlsService::class);

            $ftpClient = $service->initConnection();
            if ($ftpClient) {
                $ret['filesCount'] = $ftpClient->getCount('');
            } else {
                $ret['status'] = 'ERROR: no connection!';
            }
        } catch (\Throwable $exception) {
            $ret['status'] = 'ERROR: ' . $exception->getMessage();
            $ret['detail'] = $exception->getTraceAsString();
        }

        return $ret;
    }

    private function checkRedisConnection(): string
    {
        $redis = 'Disabled';
        if (self::isRedisEnabled() === true) {
            $redisService = $this->container->get('redis_service');
            $key = $redisService->generateKey('test:status');
            $redisService->set($key, new \stdClass());
            $redis = 'OK';
        }

        return $redis;
    }

    public function getCountries(Request $request, Response $response): Response
    {
        $countries = \App\Models\Sys\Country::query()
            ->selectRaw('code AS value')
            ->selectRaw('name AS label')
            ->get();

        return $this->jsonResponse($response, 'success', $countries, 200);
    }

    public function getDBTables(Request $request, Response $response): Response
    {
        $params = $request->getQueryParams();

        /** @var Connection $db */
        $db = $this->container->get('dbORM')->getConnection();
        $dbName = $db->getDatabaseName();

        $tables = [];
        if (Func::keyExistsInWithParam($params, 'fields')) {
            $result = $db->select("SELECT table_name, column_name
                    FROM information_schema.columns
                    WHERE table_schema = '{$dbName}'");

            foreach ($result as $key => $row) {
                if (!isset($tables[$row->table_name])) {
                    $tables[$row->table_name] = [];
                }
                $tables[$row->table_name][] = $row->column_name;
            }
        } else {
            $result = $db->select("SELECT table_name FROM information_schema.tables WHERE table_schema = '{$dbName}'");
            foreach ($result as $key => $table) {
                $tables[] = $table->table_name;
            }
        }

        $data['tables'] = $tables;
        $data['dbName'] = $dbName;

        return $this->jsonResponse($response, 'success', $data, 200);
    }

    /**
     * Getting Tracking Pixel image.
     *
     * @deprecated instead of it, we use WHC Media Service
     *
     * @param Request $request
     * @param Response $response
     * @return Response
     * @throws Exception
     */
    public function getTrackingPixelImage(Request $request, Response $response): Response
    {
        $params = $request->getParams();
        $idSecured = $params['id'] ?? null;
        $srcSecured = $params['e1'] ?? null;
        $destSecured = $params['e2'] ?? null;
        $kv = $params['kv'] ?? 1;

        if (!$destSecured || !$srcSecured) {
            BaseException::raiseInvalidRequest();
        }

        //THIS RETURNS THE IMAGE
        header('Content-Type: image/png');
        readfile(APP_PATH . DS . 'pixel.png');

        $keys = Constants::FILE_SEC_KEYS[$kv];
        $id = Func::mcrypt('decrypt', $idSecured, ...$keys);
        $email = Func::mcrypt('decrypt', $srcSecured, ...$keys);
        $emailDest = Func::mcrypt('decrypt', $destSecured, ...$keys);


        //THIS IS THE SCRIPT FOR THE ACTUAL TRACKING
        $excludedIpList = [
            '**************',
            // '127.0.0.1',
        ];
        $ipAddr = $_SERVER['REMOTE_ADDR'];
        $isWhcSeen = in_array($ipAddr, $excludedIpList);

        $date = date('Y-m-d H:i:s', $_SERVER['REQUEST_TIME']);
        $txt = ". IP: $ipAddr, " . $date;
        if ($isWhcSeen) {
            $txt .= ' (from WHC)';
        }

        SysLogService::saveLog(SysLog::CATEGORY_EMAIL_TRACKING, SysLog::NAME_EMAIL_TRACKING_SEEN . $txt
            , SysLog::STATUS_SUCCESS
            , null
            , [
                'ref1' => 'Email Id: #' . $id . ', ' . $email . ' -> ' . $emailDest,
                'ref2' => $emailDest,
            ]);

        if ($id) {
            if (!$isWhcSeen) {
                /** @var EmailReceiver $receiver */
                $receiver = EmailReceiver::query()->where(['email_id' => $id, 'email' => $emailDest])->first();
                if ($receiver) {
                    $receiver->open_seen_updated_on = Func::dtDbDatetimeStr();
                    if (!$receiver->open_count) {
                        $receiver->open_first_seen_on = Func::dtDbDatetimeStr();
                    }
                    $receiver->open_count = $receiver->open_count + 1;
                    $receiver->save();
                }
            }
        }

        exit;
    }


    /**
     * @param Request $request
     * @param Response $response
     * @return Response
     * @throws \Psr\Container\ContainerExceptionInterface
     * @deprecated
     *
     */
    public function getDBTable(Request $request, Response $response): Response
    {
        $mappingTables = [

        ];

        $params = $request->getQueryParams();

        /** @var Connection $db */
        $db = $this->container->get('dbORM')->getConnection();
        $dbName = $db->getDatabaseName();

        $tables = [];
        if (Func::keyExistsInWithParam($params, 'fields')) {
            $result = $db->select("SELECT table_name, column_name
                    FROM information_schema.columns
                    WHERE table_schema = '{$dbName}'");

            foreach ($result as $key => $row) {
                if (!isset($tables[$row->table_name])) {
                    $tables[$row->table_name] = [];
                }
                $tables[$row->table_name][] = $row->column_name;
            }
        } else {
            $result = $db->select("SELECT table_name FROM information_schema.tables WHERE table_schema = '{$dbName}'");
            foreach ($result as $key => $table) {
                $tables[] = $table->table_name;
            }
        }

        $data['tables'] = $tables;
        $data['dbName'] = $dbName;

        return $this->jsonResponse($response, 'success', $data, 200);
    }

    public function getMappableDBTables(Request $request, Response $response): Response
    {
        $mappingTables = [
        ];

        return $this->jsonResponse($response, 'success', $mappingTables, 200);
    }

    public function getDBTableFields(Request $request, Response $response): Response
    {
        $params = $request->getQueryParams();
        $data = [];
        if ($params['table_name'] ?? null) {
            /** @var Connection $db */
            $db = $this->container->get('dbORM')->getConnection();
            $dbName = $db->getDatabaseName();

            $result = $db->select("SELECT column_name, column_comment, column_type
                    FROM information_schema.columns
                    WHERE table_schema = '{$dbName}' AND column_key!='PRI' AND table_name=?", [$params['table_name']]);

            $data = $result;
        } else {
            \App\Exception\Base::raiseInvalidRequest('table_name is required.');
        }

        return $this->jsonResponse($response, 'success', $data, 200);
    }

    public function downloadFile(Request $request, Response $response)
    {
        $params = (array)$request->getParams();
        $key = $params['key'] ?? null;
        $type = $params['type'] ?? null;
        $b64 = $params['b64'] ?? null;


        if (!$key) {
            \App\Exception\Base::raiseInvalidRequest('Type and Key are required!');
        }
        $force = $params['force'] ?? null;

        $filePath = Func::pathJoin(PRIVATE_DATA_PATH, $key);
        if (file_exists($filePath)) {
            if ($b64) {
                $result = ['b64' => base64_encode(file_get_contents($filePath))];

                return $this->jsonResponse($response, 'success', $result, 200);
            }

            $tmpFileLib = new FileLib('');
            $mime = $tmpFileLib->mime2ext('type', true);

            $info = pathinfo($key);
            $filename = $info['basename'];
            $x = explode('.', $filename);
            $extension = end($x);

            $filesize = @filesize($filePath);

            /*if ($set_mime === TRUE) {
                if (count($x) === 1 or $extension === '') {
                    /* If we're going to detect the MIME type,
                     * we'll need a file extension.
                     * /
                    return;
                }
            }*/

            /* It was reported that browsers on Android 2.1 (and possibly older as well)
             * need to have the filename extension upper-cased in order to be able to
             * download it.
             *
             * Reference: http://digiblog.de/2011/04/19/android-and-the-download-file-headers/
             */
            if (count($x) !== 1 && isset($_SERVER['HTTP_USER_AGENT']) && preg_match('/Android\s(1|2\.[01])/', $_SERVER['HTTP_USER_AGENT'])) {
                $x[count($x) - 1] = strtoupper($extension);
                $filename = implode('.', $x);
            }

            if (($fp = @fopen($filePath, 'rb')) === FALSE) {
                \App\Exception\Base::raiseInvalidRequest('Internal server error while opening file.', 500);
            }

            // Clean output buffer
            if (ob_get_level() !== 0 && @ob_end_clean() === FALSE) {
                @ob_clean();
            }

            // Generate the server headers
            if ($force)
                header('Content-Disposition: attachment; filename="' . $filename . '"');
            else
                header('Content-Disposition: inline; filename="' . $filename . '"');

            // Generate the server headers
            header('Content-Type: ' . $mime);
            header('Expires: 0');
            header('Content-Transfer-Encoding: binary');
            header('Content-Length: ' . $filesize);
            header('Cache-Control: private, no-transform, no-store, must-revalidate');

            // Flush 1MB chunks of data
            while (!feof($fp) && ($data = fread($fp, 1048576)) !== FALSE) {
                echo $data;
            }

            fclose($fp);

            exit;
        }
    }

    public function getLastLog(Request $request, Response $response): Response
    {
        $input = $request->getQueryParams();
        $lineCnt = Func::safeInt($input['lines'] ?? 100);

        $filename = APP_PATH . DS . 'logs' . DS . Func::getLogger()->getName() . '-' . date(DATE_FORMAT_YMD) . '.log';

        $file = new SplFileObject($filename, 'r');

        $file->seek(PHP_INT_MAX);

        $last_line = $file->key();

        $offset = $last_line - $lineCnt;
        if ($offset < 0) $offset = 0;
        $lines = new LimitIterator($file, $offset, $last_line);
        $data = implode('', iterator_to_array($lines));

        return $this->jsonResponse($response, 'success', $data, 200);
    }
}



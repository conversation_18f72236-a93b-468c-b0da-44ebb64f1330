<?php

namespace App\Models\Offer;

use App\Models\BaseModel;
use App\Models\File;

/**
 * @property integer $offer_newsletter_id
 * @property integer $file_id
 * @property File $file
 * @property OfferNewsletter $newsletter
 */
class OfferNewsletterFile extends BaseModel
{
    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'offer_newsletter_file';

    /**
     * @var array
     */
    protected $fillable = [];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function file()
    {
        return $this->belongsTo('App\Models\File');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function newsletter()
    {
        return $this->belongsTo('App\Models\Offer\OfferNewsletter');
    }
}

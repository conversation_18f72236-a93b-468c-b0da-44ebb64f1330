<?php

declare(strict_types=1);

namespace App\Controller\Offer\OfferNewsletter;

use App\Exception\BaseException;
use App\Models\File;
use App\Models\Offer\OfferNewsletter;
use Slim\Http\Request;
use Slim\Http\Response;

final class UpdateOrCreate extends Base
{
    public function __invoke(Request $request, Response $response): Response
    {
        $input = (array)$request->getParsedBody();

        $offer_no = $input['offer_no'] ?? null;
        if (!$offer_no) {
            BaseException::raiseInvalidRequest('Offer No is required!');
        }

        $uploadedFiles = $request->getUploadedFiles();

        $rows = $input['rows'] ?? [];
        $existedIds = [];
        foreach ($rows as $ind => &$row) {
            if ($row['id'] ?? null) $existedIds[] = $row['id'];
        }

        if ($rows) {
            foreach ($rows as $ind => &$row) {
                $row['offer_no'] = $offer_no;
                $newsletter = $this->offerNewsletterService->updateOrCreate($row, $uploadedFiles['rows'][$ind]['files'] ?? []);
                $existedIds[] = $newsletter->id;
            }
        }

        $idsDeleted = OfferNewsletter::query()
            ->where('offer_no', $offer_no)
            ->whereNotIn('id', $existedIds)
            ->pluck('id')->toArray();

        $excludedFileNames = File::query()
            ->select('file_name')
            ->where('category', File::CAT_OFFER_NEWSLETTER_FILE)
            ->whereHas('offerNewsletters', function ($builder) use (&$offer_no, &$existedIds) {
                $builder->where('offer_no', $offer_no)
                    ->whereIn('id', $existedIds);
            })
            ->pluck('file_name')
            ->toArray();
        $this->offerNewsletterService->delete($idsDeleted, $excludedFileNames);

        return $this->jsonResponse($response, 'success', true, 201);
    }
}

<?php

declare(strict_types=1);

namespace App\Service\Offer\OfferTemplate;

use App\Service\BaseService;
use App\Repository\Offer\OfferTemplateRepository;
use Slim\Container;

abstract class Base extends BaseService
{
    private const REDIS_KEY = 'Offer:%s';
    protected OfferTemplateRepository $offerTemplateRepository;

    public function __construct(Container $container)
    {
        $this->offerTemplateRepository = $container->get(OfferTemplateRepository::class);
    }

    public function getOfferTemplateRepository()
    {
        return $this->offerTemplateRepository;
    }
}


<?php

namespace App\Models\Supplier;

use Illuminate\Contracts\Database\Eloquent\CastsAttributes;

class SupplierMetaValueCast implements CastsAttributes
{
    /**
     * @param SupplierMeta $model
     * @param string $key
     * @param $value
     * @param array $attributes
     * @return array|float[]|int[]|mixed|string[]
     */
    public function get($model, string $key, $value, array $attributes)
    {
        $type = $model->getAttributeValue('type');

        if ($type == SupplierMeta::TYPE_PRODUCT_CATEGORY) {
            return $value === null ? null : (int)$value;
        }
        return $value;
    }

    public function set($model, string $key, $value, array $attributes)
    {
        return $value;
    }
}
<?php

declare(strict_types=1);

namespace App\Repository\Email;

use App\Lib\FuncModel;
use App\Models\Email\EmailTemplateCategory;
use App\Repository\BaseRepositoryORM;
use Illuminate\Database\Eloquent\Builder;

final class EmailTemplateCategoryRepository extends BaseRepositoryORM
{
    public function getQueryEmailTemplateCategoriesByPage($params = []): Builder
    {
        $qb = $this->getQueryBuilder();

        if (!empty($params)) {
            if ($params['cat1'] ?? '') {
                $qb->where('cat1', 'like', FuncModel::likeValue($params['cat1']));
            }
            if ($params['keyWords'] ?? '') {
                $qb->where('cat1', 'like', $params['keyWords'] . '%');
            }


            $qb = $this->applyOrderBy($qb, $params);
        }

        return $qb;
    }

    /**
     * Get lists by pagination.
     *
     * @param int $page
     * @param int $perPage
     * @param array|null $params
     * @return array
     */
    public function getEmailTemplateCategoriesByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        $query = $this->getQueryEmailTemplateCategoriesByPage($params);

        $total = $this->getCountByQuery($query);

        $result = $this->getResultsWithPagination(
            $query,
            $page,
            $perPage,
            $total
        );

        return $result;
    }

    public function getQueryBuilder(): Builder
    {
        return EmailTemplateCategory::query();
    }
}
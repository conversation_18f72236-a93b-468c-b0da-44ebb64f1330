<?php

declare(strict_types=1);

namespace App\Repository\Email;

use App\Lib\Func;
use App\Lib\FuncModel;
use App\Models\Email\Email;
use App\Repository\BaseRepositoryORM;
use Illuminate\Database\Eloquent\Builder;

final class EmailRepository extends BaseRepositoryORM
{
    public function getQueryEmailsByPage($params = []): Builder
    {
        $qb = $this->getQueryBuilder();

        if (!empty($params)) {
            if ($params['id'] ?? null) {
                $qb->where('id', $params['id']);
            } else {
                if ($params['sender'] ?? null) {
                    $qb->where('sender', 'LIKE', "{$params['sender']}%");
                }

                if ($params['receiver'] ?? null) {
                    $qb->where('receiver', 'LIKE', "{$params['receiver']}%");
                }

                if ($params['subject'] ?? null) {
                    $qb->where('subject', 'LIKE', "%{$params['subject']}%");
                }

                if ($params['text_plain'] ?? null) {
                    $qb->where('text_plain', 'LIKE', "%{$params['text_plain']}%");
                }

                /*if ($linkedType = $params['linkedType'] ?? null) {
                    // Not linked?
                    if ($linkedType == 1) {
                        $qb->whereDoesntHave('tasks');
                    } else if ($linkedType == 2) {
                        $qb->whereHas('tasks');
                    }
                }*/
            }

            if ($keyword = $params['keyword'] ?? null) {
                $qb->where(function ($builder) use ($keyword) {
                    $builder->where('sender', 'LIKE', "%$keyword%")
                        ->orWhere('subject', 'LIKE', "%$keyword%")
                        ->orWhere('text_plain', 'LIKE', "%$keyword%")
                        ->orWhere('sender_name', 'LIKE', "%$keyword%")
                        ->orWhere('receiver', 'LIKE', "%$keyword%")
                        ->orWhere('receiver', 'LIKE', "%$keyword%");
                });
            }

            if ($nid = ($params['nid'] ?? null)) {
                $qb->where('id', '!=', $nid);
            }

            if ($caseId = ($params['crm_case_id'] ?? null)) {
                $qb->where('crm_case_id', '=', $caseId);
            }

            if ($boxes = ($params['boxes'] ?? null)) {
                $qb->whereIn('box', $boxes);
            }

            if ($params['includeHidden'] ?? 0) {

            } else {
                $qb->where('is_hidden', '!=', 1);
            }

            if ($visibility = $params['visibility'] ?? null) {
                if ($visibility == 'shown') {
                    $qb->where('is_hidden', 0);
                } else if ($visibility == 'hidden') {
                    $qb->where('is_hidden', 1);
                }
            }

            $qb = $this->applyOrderBy($qb, $params);
        }

        return $qb;
    }

    /**
     * Get lists by pagination.
     *
     * @param int $page
     * @param int $perPage
     * @param array|null $params
     * @return array
     */
    public function getEmailsByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        $query = $this->getQueryEmailsByPage($params);
        $total = $this->getCountByQuery($query);

        $result = $this->getResultsWithPagination(
            $query,
            $page,
            $perPage,
            $total
        );

        return $result;
    }

    public function getQueryBuilder(): Builder
    {
        return Email::query();
    }
}
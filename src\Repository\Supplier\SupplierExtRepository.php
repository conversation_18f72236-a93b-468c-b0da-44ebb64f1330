<?php

declare(strict_types=1);

namespace App\Repository\Supplier;

use App\Lib\Func;
use App\Models\Supplier\SupplierExt;
use App\Repository\BaseRepositoryORM;
use Illuminate\Database\Eloquent\Builder;

final class SupplierExtRepository extends BaseRepositoryORM
{
    private function getQuerySupplierExtsByPage($params = []): Builder
    {
        $qb = $this->getQueryBuilder();

        if (!empty($params)) {
            if (Func::keyExistsInWithParam($params, 'supplier')) {
                $qb->with('supplier');
            }

            $qb = $this->applyOrderBy($qb, $params);
        }

        return $qb;
    }

    /**
     * Get lists by pagination.
     *
     * @param int $page
     * @param int $perPage
     * @param array|null $params
     * @return array
     */
    public function getSupplierExtsByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        $query = $this->getQuerySupplierExtsByPage($params);
        $total = $this->getCountByQuery($query);

        $result = $this->getResultsWithPagination(
            $query,
            $page,
            $perPage,
            $total
        );

        return $result;
    }

    public function getQueryBuilder(): Builder
    {
        return SupplierExt::query();
    }
}
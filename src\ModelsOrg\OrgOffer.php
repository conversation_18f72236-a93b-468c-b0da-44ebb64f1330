<?php

namespace App\ModelsOrg;

use App\Models\Offer\OfferExt;

/**
 * @property string $id
 * @property string $offer_sid
 * @property string $offer
 * @property number $status
 * @property boolean $warehouse
 * @property string $supplier_id
 * @property integer $we
 * @property boolean $brand
 * @property boolean $spread_out
 * @property string $created_on
 * @property string $updated_on
 *
 *
 * @property OrgSupplier $supplier
 *
 *
 */
class OrgOffer extends BaseModel
{
    use \Staudenmeir\EloquentEagerLimit\HasEagerLimit;

    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'offers';

    /**
     * The "type" of the auto-incrementing ID.
     * 
     * @var string
     */
    protected $keyType = 'string';

    /**
     * Indicates if the IDs are auto-incrementing.
     * 
     * @var bool
     */
    public $incrementing = false;

    /**
     * @var array
     */
    protected $fillable = ['offer_sid', 'offer', 'status', 'warehouse', 'supplier_id', 'we', 'brand', 'spread_out', 'created_on', 'updated_on'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function comments()
    {
        return $this->hasMany('App\ModelsOrg\OrgOfferComment', 'offer_id', 'id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function top3Comments()
    {
        return $this->hasMany('App\ModelsOrg\OrgOfferComment', 'offer_id', 'id')->latest()->limit(3);
    }


    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function supplier()
    {
        return $this->belongsTo('App\ModelsOrg\OrgSupplier', 'supplier_id', 'id');
    }

    /**
     * Other DB connection
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function ext()
    {
        return $this->hasOne(OfferExt::class, 'offer_no', 'offer_sid');
    }

}


<?php

declare(strict_types=1);

namespace App\Service\Sys\Backup;

use App\Exception\BaseException;
use App\Lib\Func;
use App\Lib\FuncModel;
use App\Models\Sys\Dict;
use App\Service\BaseService;
use Ifsnop\Mysqldump\Mysqldump;
use Slim\Container;

final class BackupService extends BaseService
{
    public function __construct(Container $container)
    {
        $dummyConnection = $container->get('dbORM')->getConnection();
    }

    public function backup()
    {
        $settings = Func::getContainer()->get('settings')['db'] ?? null;
        if (!$settings) BaseException::raiseInternalServerError('No database setting');

        $DB_HOST = $settings['host'];
        $DB_USER = $settings['user'];
        $DB_PASSWORD = $settings['pass'];
        $DB_NAME = $settings['name'];
        $DB_PORT = $settings['port'];

        try {
            $dumpSettings = [
                'compress' => Mysqldump::GZIP,
                'add-drop-table' => true,
                'add-locks' => false,
                'routines' => true,
                'extended-insert' => true,  // batch insert mode.
            ];
            $dump = new Mysqldump("mysql:host={$DB_HOST}:{$DB_PORT};dbname={$DB_NAME}", $DB_USER, $DB_PASSWORD, $dumpSettings);
            $file_name = "WHC_Supplier_".date("Ymd") . "_" . time() . ".gz";

            $basePath = FuncModel::getDictValue(Dict::CODE_BACKUP_PATH);
            if (!$basePath) {
                $relPath = '/backup/';
                $basePath = Func::pathJoin(PRIVATE_DATA_PATH, $relPath);
            }
            if (!file_exists($basePath)) {
                \Safe\mkdir($basePath, 0755, true);
            }
            $filePath = Func::pathJoin($basePath, $file_name);
            $dump->start($filePath);
        } catch (\Exception $e) {
             BaseException::raiseInternalServerError('DB Backup ERROR: ' . $e->getMessage());
        }

    }
}

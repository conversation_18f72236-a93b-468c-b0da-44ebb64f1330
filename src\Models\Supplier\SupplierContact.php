<?php

namespace App\Models\Supplier;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Builder;

/**
 * @property integer $id
 * @property integer $supplier_id
 * @property string $department
 * @property string $email
 * @property integer $is_active
 * @property integer $is_default
 * @property string $firstname
 * @property string $lastname
 * @property string $middlename
 * @property string $prefix
 * @property string $suffix
 * @property string $telephone
 * @property string $note
 * @property string $created_on
 * @property string $updated_on
 * @property boolean $is_asp
 * @property boolean $is_asp_reg
 * @property boolean $is_blocked
 *
 * @property Supplier $supplier
 */
class SupplierContact extends BaseModel
{
    public $timestamps = true;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'supplier_contact';

    /**
     * @var array
     */
    protected $fillable = ['supplier_id', 'email'
        , 'department'
        , 'is_active'
        , 'is_default'
        , 'note'
        , 'firstname', 'lastname', 'middlename', 'prefix', 'suffix', 'telephone', 'created_on', 'updated_on'
        , 'is_asp'
        , 'is_asp_reg'
        , 'is_blocked'
    ];

    protected $appends = [
        'fullname',
    ];

    public function getFullnameAttribute()
    {
        $firstName = $this->getAttribute('firstname') ?? '';
        $lastname = $this->getAttribute('lastname') ?? '';

        return $firstName . ' ' . $lastname;
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function supplier()
    {
        return $this->belongsTo('App\Models\Supplier\Supplier');
    }

    public function scopeSortDefault(Builder $query)
    {
        $query
            ->orderByDesc('is_asp')
            ->orderBy('is_asp_reg')
            ->orderByDesc('is_active');
    }

    public function scopeActive(Builder $query)
    {
        $query->where('is_active', 1);
    }
}

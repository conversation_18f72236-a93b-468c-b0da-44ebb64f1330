<?php

namespace App\Models\Sys;

use App\Lib\Func;
use App\Models\BaseModel;

/**
 * @property string $code
 * @property string $parent_code
 * @property string $type
 * @property string $label
 * @property string $value
 * @property boolean $status
 * @property integer $sort
 * @property string $desc
 * @property string $settings
 *
 * @property array $delivery_note_suffix       // calc value
 *
 * @property Dict $parent
 *
 * @property @method active()
 */
class Dict extends BaseModel
{
    // types
    public const TYPE_SYS_CONFIG = 'sys config';
    public const TYPE_EMAIL_CONFIG = 'Email Config';

    // codes
    public const CODE_BACKUP_PATH = 'BACKUP_PATH';
    public const CODE_FS_ONE_ADDRESS = 'FS_ONE_ADDRESS';
    public const CODE_FS_ONE_ADDRESS_SHIPPING = 'FS_ONE_ADDRESS_SHIPPING';

    public const CODE_EMAIL_FROM = 'EMAIL_FROM';
    public const CODE_EMAIL_BCC = 'EMAIL_BCC';
    public const CODE_EMAIL_IS_OAUTH = 'EMAIL_IS_OAUTH';

    public const CODE_OFFER_DEFAULT_ANREDE_DE = 'OFFER_DEFAULT_ANREDE_DE';
    public const CODE_OFFER_DEFAULT_ANREDE_EN = 'OFFER_DEFAULT_ANREDE_EN';

    // File Manager
    public const CODE_FM_DATA_FILE_BASE_PATH = 'FM_DATA_FILE_BASE_PATH';

    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'sys_dict';

    /**
     * The primary key for the model.
     * 
     * @var string
     */
    protected $primaryKey = 'code';

    /**
     * The "type" of the auto-incrementing ID.
     * 
     * @var string
     */
    protected $keyType = 'string';

    /**
     * Indicates if the IDs are auto-incrementing.
     * 
     * @var bool
     */
    public $incrementing = false;

    /**
     * @var array
     */
    protected $fillable = ['parent_code', 'code', 'type', 'value', 'label', 'status', 'sort', 'desc', 'settings'];

    protected $casts = ['settings' => 'json'];

    protected $appends = ['casted_value'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function parent()
    {
        return $this->belongsTo('App\Models\Sys\Dict', 'parent_code', 'code');
    }

    public function scopeActive($query, $status=1)
    {
        return $query->where('status', $status);
    }

    public function getCastedValueAttribute()
    {
        $value = $this->getAttribute('value');

        return $value;
    }


    public static function parseEmailBgColorConfig($str) {
        $arr = Func::csvToArr($str);
        $map = [];
        foreach ($arr as $x) {
            $x = trim($x);
            $pos = mb_strrpos($x, '[');
            if ($pos === false) continue;
            $pos2 = mb_strrpos($x, ']');
            if ($pos2 === false) continue;
            $crmStatus = trim(mb_substr($x, 0, $pos));
            $color = mb_substr($x, $pos + 1, $pos2 - $pos -1);
            $map[$crmStatus] = ['color' => $color];
        }
        return $map;
    }

    public function getDeliveryNoteSuffixAttribute() {
        $str = $this->getAttribute('value');
        $arr = Func::csvToArr($str, '^');
        return $arr;
    }
}

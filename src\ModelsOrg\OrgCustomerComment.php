<?php

namespace App\ModelsOrg;

use App\ModelsOrg\BaseModel;

/**
 * @property string $id
 * @property string $customer_id
 * @property string $comment
 * @property string $created_on
 * @property string $created_by
 * @property string $sc_id
 * @property string $offer_id
 * @property string $order_id
 */
class OrgCustomerComment extends BaseModel
{
    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'customer_comments';

    /**
     * The "type" of the auto-incrementing ID.
     * 
     * @var string
     */
    protected $keyType = 'string';

    /**
     * Indicates if the IDs are auto-incrementing.
     * 
     * @var bool
     */
    public $incrementing = false;

    /**
     * @var array
     */
    protected $fillable = ['customer_id', 'comment', 'created_on', 'created_by', 'sc_id', 'offer_id', 'order_id'];
}

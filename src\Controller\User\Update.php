<?php

declare(strict_types=1);

namespace App\Controller\User;

use App\Exception\User;
use App\Lib\Func;
use Illuminate\Database\Eloquent\Builder;
use Slim\Http\Request;
use Slim\Http\Response;

final class Update extends Base
{
    public function __invoke(Request $request, Response $response, array $args): Response
    {
        $user_id = $args['id'] ?? null;
        if (!$user_id) {
            throw new User('User not found', 404);
        }
        $user_id = intval($user_id);

        $input = (array)$request->getParsedBody();

        $newData = array_merge($input, [
            'email' => $input['email'] ?? '',
            'username' => $input['username'] ?? '',
            'status' => $input['status'] ?? 0,
            'role' => $input['role'] ?? \App\Models\User::ROLE_USER_NORMAL,
        ]);

        if (key_exists('name', $input)) {
            if (!$input['name'])
                User::raiseInvalidRequest('User name is required!');
            $newData['name'] = $input['name'];
        }
        if (key_exists('status', $input)) {
            $newData['status'] = intval($input['status']);
        }
        if (key_exists('role', $input)) {
            $newData['role'] = intval($input['role']);
        }

        // Check if username or email exists?
        $repo = $this->getCreateUserService()->getUserRepository();
        $builder = $repo->getQueryBuilder();
        $builder->where('user_id', '!=', $user_id);
        $builder->where(function (Builder $builder) use ($newData) {
            $builder->where('username', $newData['username'])
                ->orWhere('email', $newData['email']);
        });

        $exists = $builder->count() > 0;
        if ($exists) {
            User::raiseInvalidRequest('Username or Email already exists.');
        }

        // If a password exists
        if (isset($input['password']) && $input['password']) {
            if ($user_id != $input['user']['user_id'] ?? null) {
                $newData['nonce'] = $repo->getNonce();
            }
            $newData['password'] = Func::passwordHash($input['password']);
        } else {
            unset($newData['password']);
        }

        // If initials exists, then make it to uppercase
        if ($newData['initials'] ?? null) {
            $newData['initials'] = strtoupper($newData['initials']);
        }

        // Permission checking
        $authUser = $input['user'] ?? null;
        if ($authUser['role'] != \App\Models\User::ROLE_ADMIN) {
            unset($newData['role']);
            unset($newData['status']);
        }

        $user = $this->getUpdateUserService()->update($newData, $user_id);

        return $this->jsonResponse($response, 'success', $user, 200);
    }
}

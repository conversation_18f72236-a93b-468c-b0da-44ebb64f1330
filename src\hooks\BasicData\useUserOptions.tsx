import { getUsersList } from "@/services/app/user";
import Util from "@/util";
import type { ProFormInstance } from "@ant-design/pro-form";
import type { DefaultOptionType } from "antd/lib/select";
import React, { useCallback, useEffect, useState } from "react";

/**
 * Auto completion list of Active User
 */
export default (defaultParams?: Record<string, any>, formRef?: React.MutableRefObject<ProFormInstance | undefined>, eleOptions?: any) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [userOptions, setUserOptions] = useState<DefaultOptionType[]>([]);

  const searchUserOptions = useCallback(
    async (params?: Record<string, any>) => {
      setLoading(true);

      getUsersList({ status: 1, pageSize: 1000, ...params }, {}, {})
        .then((res) => {
          let options: any[] = [];
          if (defaultParams?.include_all_option) {
            options.push({
              value: "all",
              label: "All",
            });
          }

          if (defaultParams?.include_blank_option) {
            options.push({
              value: "-",
              label: "N/A",
            });
          }

          options = [
            ...options,
            ...res.data.map((x: API.CurrentUser) => ({
              ...x,
              value: x.user_id,
              label: `${x.initials ?? x.username}`,
            })),
          ];

          setUserOptions(options);
        })
        .catch(Util.error);
    },
    [defaultParams?.include_all_option, defaultParams?.include_blank_option],
  );

  useEffect(() => {
    searchUserOptions();
  }, [searchUserOptions]);

  return {
    userOptions,
    searchUserOptions,
    loading,
  };
};

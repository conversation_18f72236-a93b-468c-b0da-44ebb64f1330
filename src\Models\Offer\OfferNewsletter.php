<?php

namespace App\Models\Offer;

use App\Models\BaseModel;
use App\Models\File;

/**
 * @property integer $id
 * @property string $offer_no
 * @property string $product_title
 * @property integer $case_qty
 * @property integer $ve_pallet
 * @property float $price
 * @property integer $sort_order
 * @property string $details
 * @property string $created_on
 * @property string $updated_on
 * @property integer $created_by
 * @property integer $updated_by
 *
 * @property File[] $files
 */
class OfferNewsletter extends BaseModel
{
    public $timestamps = true;

    protected $casts = ['details' => 'json'];

    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'offer_newsletter';

    /**
     * @var array
     */
    protected $fillable = ['offer_no', 'product_title', 'case_qty', 've_pallet', 'price', 'sort_order', 'details', 'created_on', 'updated_on', 'created_by', 'updated_by'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function files()
    {
        return $this->belongsToMany('App\Models\File', 'offer_newsletter_file');
    }
}

import { DEFAULT_PER_PAGE_PAGINATION, DictCode } from "@/constants";
import Util, { ni, nl2br, sEllipsed, sn } from "@/util";
import { FileOutlined } from "@ant-design/icons";
import { ActionType, PageContainer, ProColumns, ProForm, ProFormDigit, ProFormInstance, ProTable } from "@ant-design/pro-components";
import { Avatar, Card, Col, Popover, Row, Space, Tag, Typography, Image } from "antd";
import { useEffect, useRef, useState } from "react";
import { useModel } from "@umijs/max";
import { getOfferNewsletterListByPage } from "@/services/app/Offer/offer-newsletter";

type RowType = API.OfferNewsletter & {
  uid?: string;
};
type SearchFormValueType = API.OfferNewsletter;

const OfferNewsletter: React.FC = () => {
  const searchFormRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();

  const [loading, setLoading] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<RowType>();

  useEffect(() => {
    const formValues = Util.getSfValues("offer_blog", {});
    searchFormRef.current?.resetFields();
    searchFormRef.current?.setFieldsValue(formValues);
  }, []);

  const columns: ProColumns<RowType>[] = [
    {
      title: "Offer No",
      dataIndex: ["offer_no"],
      ellipsis: true,
      width: 100,
      sorter: true,
      defaultSortOrder: "descend",
      render: (__, entity) => {
        return (
          <Typography.Link href={`${PUBLIC_PATH}offers/detail/${entity.offer_no}`} target="_blank">
            {entity.offer_no}
          </Typography.Link>
        );
      },
    },
    {
      title: "Product Title",
      dataIndex: ["product_title"],
      width: 200,
    },
    {
      title: "Pcs / Case",
      dataIndex: ["case_qty"],
      width: 100,
      align: "right",
      render(dom, entity, index, action, schema) {
        return ni(entity.case_qty);
      },
    },
    {
      title: "VE Pallet",
      dataIndex: ["ve_pallet"],
      width: 100,
      align: "right",
      render(dom, entity, index, action, schema) {
        return ni(entity.ve_pallet);
      },
    },
    {
      title: "Price",
      dataIndex: ["price"],
      width: 100,
      align: "right",
      render(dom, entity, index, action, schema) {
        return ni(entity.price);
      },
    },
    {
      valueType: "option",
      dataIndex: "option2",
      width: 60,
    },
    {
      title: "Files",
      dataIndex: ["files"],
      ellipsis: true,
      width: 800,
      render: (__, record) => {
        return record.files ? (
          <Space size={24} wrap={true}>
            {record.files.map((a) => {
              const fileUrl = `${a.url}`;
              return (
                <Space key={a.id} direction="horizontal" style={{ maxWidth: 250 }}>
                  {a.type?.includes("image") ? (
                    <Avatar shape="square" size={48} src={<Image src={fileUrl} />} />
                  ) : (
                    <FileOutlined style={{ fontSize: 32, color: "grey" }} />
                  )}
                  <Typography.Link href={fileUrl} ellipsis target="_blank" className="text-sm" style={{ textAlign: "center", maxWidth: 200 }}>
                    {a.file_name}
                  </Typography.Link>
                </Space>
              );
            })}
          </Space>
        ) : null;
      },
    },
    {
      valueType: "option",
    },
  ];

  return (
    <PageContainer>
      <Card style={{ marginBottom: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          submitter={{
            render(props, dom) {
              return <div style={{ marginLeft: "auto" }}>{dom}</div>;
            },
            searchConfig: { submitText: "Search" },
            submitButtonProps: { loading, htmlType: "submit", style: { marginLeft: 8 } },
            onSubmit: () => actionRef.current?.reload(),
            onReset: () => {
              searchFormRef.current?.resetFields();
              actionRef.current?.reload();
            },
          }}
        >
          <ProFormDigit name={"offer_no"} label="Offer No" width={"xs"} placeholder={"Offer No"} />
        </ProForm>
      </Card>

      <ProTable<RowType, API.PageParams>
        headerTitle={
          <Space size={32}>
            <span>Offers Newsletters</span>
          </Space>
        }
        actionRef={actionRef}
        rowKey="id"
        size="small"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        sticky
        scroll={{ x: 800 }}
        pagination={{
          defaultPageSize: sn(Util.getSfValues("offer_blog_p")?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION),
        }}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues("offer_blog", searchFormValues);
          Util.setSfValues("offer_blog_p", params);

          setLoading(true);
          return getOfferNewsletterListByPage(
            {
              ...params,
              ...searchFormValues,
              with: "files",
            },
            sort,
            filter,
          )
            .then((res) => {
              if (currentRow?.id) {
                setCurrentRow(res.data.find((x) => x.id == currentRow.id));
              }
              return res;
            })
            .finally(() => setLoading(false));
        }}
        onRequestError={Util.error}
        columns={columns}
        tableAlertRender={false}
        columnEmptyText=""
      />
    </PageContainer>
  );
};

export default OfferNewsletter;

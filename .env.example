MODE='production'

DB_HOST='localhost'
DB_NAME='whc_supplier'
DB_USER='root'
DB_PASS=''
DB_PORT='3306'

# WHC Org database info
DB_HOST_ORG='localhost'
DB_NAME_ORG='whc_org'
DB_USER_ORG='root'
DB_PASS_ORG=''
DB_PORT_ORG='3306'

# WHC Task Database
DB_HOST_TASK='localhost'
DB_NAME_TASK='whc_task'
DB_USER_TASK='root'
DB_PASS_TASK=''
DB_PORT_TASK='3306'

# Important: Please set 0 in the production.
DISPLAY_ERROR_DETAILS=0

# DEBUG = 100,INFO = 200,NOTICE = 250,WARNING = 300,ERROR = 400,CRITICAL = 500,ALERT = 550
LOG_LEVEL=400

# API call log from frontend.
LOG_SKIP_REQUEST=1
LOG_SKIP_RESPONSE=1

APP_DOMAIN='https://whcs138.whc.local'
MEDIA_BASE_URL='https://whcs138.whc.local:444/uploads'

# API Setting for WHC_Cust
CUST_API_URL='https://bogfccust.yuuru.net/rest'
CUST_API_TOKEN='CUST_tNHk2WwkqvCmkdrQfm7W'

# Redis related
REDIS_ENABLED=false
REDIS_URL=''

# CORS
CORS_HOSTS='*'

# JWT Authentication
SECRET_KEY='Please generate a new one!!!'

# -------------------------------------- M365 ----------------
M365_TENANT=""
M365_CLIENT_ID=""
M365_CLIENT_SECRET=""
# gfc-421.de media server REST API.
MEDIA_API_URL='https://gfc-421.de'
MEDIA_API_TOKEN='nNHk2WwkqvCmkdrQfm7W'
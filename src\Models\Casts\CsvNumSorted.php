<?php

namespace App\Models\Casts;


class CsvNumSorted extends Csv
{
    public function set($model, string $key, $value, array $attributes)
    {
        if ($value) {
            if (is_array($value)) {
                $values = array_values($value);
                if ($values) {
                    sort($values, SORT_NUMERIC);
                }
                return implode(',', array_values($values));
            } else
                return NULL;
        } else {
            return NULL;
        }
    }
}
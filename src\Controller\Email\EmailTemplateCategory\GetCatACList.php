<?php

declare(strict_types=1);

namespace App\Controller\Email\EmailTemplateCategory;

use Slim\Http\Request;
use Slim\Http\Response;

final class GetCatACList extends Base
{
    public function __invoke(Request $request, Response $response): Response
    {
        // $page = $request->getQueryParam('page', null);
        // $perPage = $request->getQueryParam('perPage', null);
        $params = $request->getParams();

        $qb = $this->emailTemplateCategoryService->getEmailTemplateCategoryRepository()->getQueryEmailTemplateCategoriesByPage($params);
        $data = $qb
            ->select('*')
            ->get()
            ->toArray();

        return $this->jsonResponse($response, 'success', $data, 200);
    }
}

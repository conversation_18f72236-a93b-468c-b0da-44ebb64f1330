version: '3'

services:
  nginx:
    image: nginx:latest
    container_name: rest-api-slim-php-nginx-container
    ports:
      - "8081:80"
    volumes:
      - ./:/var/www/html
      - ./docker/nginx/conf.d:/etc/nginx/conf.d

  php-fpm:
    build: ./docker/php7
    container_name: rest-api-slim-php-php-container
    links:
      - mysql
      - redis
    environment:
      - PHP_FPM_ENABLE=1
      - PHP_FPM_SERVER_ADDR=php
      - PHP_FPM_SERVER_PORT=9000
      - PHP_FPM_TIMEOUT=${HTTPD_TIMEOUT_TO_PHP_FPM:-180}
      - DB_HOST=mysql
      - DB_NAME=rest_api_slim_php
      - DB_USER=root
      - DB_PASS=
      - DB_PORT=3306

    volumes:
      - ./:/var/www/html

  redis:
    image: redis:4.0.5-alpine
    ports:
      - 63790:6379
    hostname: redis
    volumes:
      - redis-data:/data

  mysql:
    image: mysql:5.7
    ports:
      - 33060:3306
    environment:
      MYSQL_ALLOW_EMPTY_PASSWORD: "yes"
      MYSQL_DATABASE: rest_api_slim_php
      MYSQL_USER: root
      MYSQL_PASSWORD: 
      MYSQL_ROOT_PASSWORD: 
    volumes:
      - my-data:/var/lib/mysql
      - ./database/database.sql:/docker-entrypoint-initdb.d/database.sql

  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    depends_on:
      - mysql
    ports:
        - 8888:80
    environment:
        PMA_HOST: mysql

volumes:
  redis-data:
  my-data:

<?php

declare(strict_types=1);

namespace App\Service\Sys\CountryRegion;

use App\Service\BaseService;
use App\Repository\Sys\CountryRegionRepository;
use Slim\Container;

abstract class Base extends BaseService
{
    private const REDIS_KEY = 'Sys:%s';
    protected CountryRegionRepository $countryRegionRepository;

    public function __construct(Container $container)
    {
        $this->countryRegionRepository = $container->get(CountryRegionRepository::class);
    }

    public function getCountryRegionRepository()
    {
        return $this->countryRegionRepository;
    }
}


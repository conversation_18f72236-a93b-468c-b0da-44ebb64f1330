<?php

declare(strict_types=1);

namespace App\Service\Offer\OfferTemplateLang;

use App\Models\Offer\OfferTemplateLang;

final class OfferTemplateLangService extends Base
{
    public function create(array $input): OfferTemplateLang
    {
        return OfferTemplateLang::create($input);
    }

    public function update($id, $input): OfferTemplateLang
    {
        $row = OfferTemplateLang::findOrFail($id);
        $row->update($input);
        return $row;
    }

    public function getOne(int $id, array $params = []): OfferTemplateLang
    {
        return $this->offerTemplateLangRepository->getQueryBuilder()->where('id', $id)->first();
    }

    public function getOfferTemplateLangsByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($perPage < 1) {
            $perPage = self::DEFAULT_PER_PAGE_PAGINATION;
        }

        return $this->offerTemplateLangRepository->getOfferTemplateLangsByPage(
            $page,
            $perPage,
            $params
        );
    }

    public function delete($ids): void
    {
        $arr = is_array($ids) ? $ids : explode(',', $ids);
        $this->offerTemplateLangRepository->getQueryBuilder()->whereIn('id', $arr)->delete();
    }
}

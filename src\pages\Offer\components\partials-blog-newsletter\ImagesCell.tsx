import { Avatar, Space, Spin, Typography, Image } from "antd";
import Util, { sEllipsed } from "@/util";
import { FileOutlined, FilePdfOutlined, FileTextOutlined, FileWordOutlined } from "@ant-design/icons";

export type ImagesCellProps = {
  files: API.File[];
};

const ImagesCell: React.FC<ImagesCellProps> = ({ files }) => {
  return (
    <Space size={24} wrap={true}>
      {files.map((a) => {
        const fileUrl = a.url?.startsWith("http") ? a.url : `${API_URL}/api/${a.url}`;

        const eleStyles = {
          style: {
            fontSize: 64,
            color: "grey",
          },
        };
        let ele = null;
        if (a.type == "application/pdf") {
          ele = <FilePdfOutlined {...eleStyles} />;
        } else if (a.clean_file_name?.endsWith(".docx") || a.clean_file_name?.endsWith(".doc")) {
          ele = <FileWordOutlined {...eleStyles} />;
        } else if (a.clean_file_name?.endsWith(".txt")) {
          ele = <FileTextOutlined {...eleStyles} />;
        } else {
          ele = <FileOutlined {...eleStyles} />;
        }

        return (
          <Space key={a.id} direction="vertical">
            {a.type?.includes("image") ? (
              <Avatar shape="square" size={128} src={<Image src={fileUrl} />} style={{ border: "1px solid #efefef", borderRadius: 8 }} />
            ) : (
              <div
                style={{
                  border: "1px solid #efefef",
                  borderRadius: 8,
                  textAlign: "center",
                  width: 128,
                  height: 128,
                  paddingTop: 32,
                }}
              >
                {ele}
              </div>
            )}

            <Typography.Link href={fileUrl} ellipsis target="_blank" className="text-sm" style={{ textAlign: "center", maxWidth: 128 }}>
              {sEllipsed(a.clean_file_name, 15, 5)}
            </Typography.Link>
          </Space>
        );
      })}
    </Space>
  );
};

export default ImagesCell;

import { getOfferACList } from "@/services/app/Offer/org-offer";
import Util from "@/util";
import type { ProFormInstance } from "@ant-design/pro-form";
import { ProFormSelect } from "@ant-design/pro-form";
import type { DefaultOptionType } from "antd/lib/select";
import React, { useCallback, useEffect, useMemo, useState } from "react";

/**
 * Auto completion list of Offer
 */
export default (
  defaultParams?: Record<string, any> & { linked_ibo_pre_management_id?: number },
  formRef?: React.MutableRefObject<ProFormInstance | undefined>,
  eleOptions?: any,
) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [offerOptions, setOfferOptions] = useState<DefaultOptionType[]>([]);
  // selected offer
  const [offer, setOffer] = useState<DefaultOptionType>();

  const searchOfferOptions = useCallback(
    async (params?: Record<string, any>) => {
      setLoading(true);

      return getOfferACList({ ...defaultParams, ...params })
        .then((res) => {
          setOfferOptions(res);
          return res;
        })
        .catch(Util.error)
        .finally(() => {
          setLoading(false);
        });
    },
    [defaultParams],
  );

  useEffect(() => {
    searchOfferOptions().then((res) => {
      const offer_id = formRef?.current?.getFieldValue("offer_id");
      if (offer_id) {
        const found = (res || []).find((x: any) => x.id == offer_id);
        setOffer(found);
      }
    });
  }, [formRef, searchOfferOptions]);

  useEffect(() => {
    if (!offerOptions?.length) return;
    setOffer((prev: any) => {
      if (!prev) return prev;
      let newItem = { ...prev };

      if (newItem.id) {
        const found = offerOptions.find((x: any) => x.id == newItem.id);

        if (found) {
          newItem = { ...newItem, ...found };
        }
      }
      return newItem;
    });
  }, [offerOptions]);

  const formElements = useMemo(() => {
    return (
      <ProFormSelect
        name="offer_id"
        label={"Offer"}
        placeholder="Please select Offer"
        mode="single"
        showSearch
        options={offerOptions}
        required={eleOptions?.required}
        // request={searchOfferOptions}
        rules={
          eleOptions?.required
            ? [
                {
                  required: true,
                  message: "Offer is required",
                },
              ]
            : []
        }
        fieldProps={{
          loading: loading,
          dropdownMatchSelectWidth: false,
          maxTagCount: 1,
          /* onSearch(value) {
            searchOfferOptions({ keyWords: value });
          }, */
          onChange: (value, option) => {
            setOffer(option as any);
            eleOptions?.onChange(value, option);
          },
        }}
        labelCol={eleOptions?.labelCol}
        width={200}
      />
    );
  }, [eleOptions?.required, offerOptions, loading, searchOfferOptions]);

  return {
    offerOptions,
    searchOfferOptions,
    loading,
    offer,
    setOffer,
    formElements,
    setOfferOptions,
  };
};

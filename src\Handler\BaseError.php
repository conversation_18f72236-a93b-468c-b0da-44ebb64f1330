<?php

namespace App\Handler;

use App\Lib\FuncModel;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Monolog\Logger;
use Psr\Container\ContainerInterface;
use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;

class BaseError
{
    protected ContainerInterface $container;
    protected $message = 'Unknown';

    public function __construct(ContainerInterface $container)
    {
        $this->container = $container;
    }

    public function __invoke(
        Request    $request,
        Response   $response,
        \Exception $exception
    ): Response
    {
        $isDetail = $_SERVER['DISPLAY_ERROR_DETAILS'] == '1';

        // Clean up function
        FuncModel::cleanUp();

        $statusCode = $this->getStatusCode($exception);
        $className = $exception ? new \ReflectionClass(get_class($exception)) : null;


        /** @var Logger $logger */
        $logger = $this->container->get('logger');
        $data = [
            'code' => $statusCode,
            'status' => 'error',
            'message' => $this->message,
            'class' => $className ? $className->getName() : ' NotFound',
            'trace' => $isDetail ? $exception->getTraceAsString() : $exception->getMessage(),
        ];
        $body = json_encode($data, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT);

        // Skip detailed logging on general errors.
        if (!($exception instanceof ModelNotFoundException || $exception->getCode() == 400 || $exception->getCode() == 404)) {
            $logger->error("[Base] " . $exception->getMessage());

            // Disable detail error
            // $logger->error($exception->getTraceAsString());
            $traces = $exception->getTrace();
            if ($traces) {
                $errorDetails =  [] ;
                foreach ($traces as $ind => &$trace) {
                    $errorDetails[] = sprintf("Func: %s, #%s in %s"
                        , $trace['function']
                        , $trace['line'] ?? ''
                        , $trace['file'] ?? ''
                    );
                    if ($ind > 3) {
                        break;
                    }
                }

                $logger->error(implode(PHP_EOL, $errorDetails));
            }
        }

        $response->getBody()->write((string)$body);

        $newResp = $response
            //->withHeader('Access-Control-Allow-Origin', '*')
            ->withStatus($statusCode)
            ->withHeader('Access-Control-Allow-Headers', 'X-Requested-With, Content-Type, Accept, Origin, Authorization, AccessToken')
            ->withHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS');
        $newResp = setCorsOrigin($request, $newResp);
        return $newResp;
    }

    private function getStatusCode(\Exception $exception): int
    {
        $statusCode = 500;
        if (is_int($exception->getCode()) &&
            $exception->getCode() >= 400 &&
            $exception->getCode() <= 500
        ) {
            $statusCode = $exception->getCode();
        }

        return $statusCode;
    }
}

<?xml version="1.0"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" colors="true" bootstrap="vendor/autoload.php" xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/9.3/phpunit.xsd">
  <coverage includeUncoveredFiles="true">
    <include>
      <directory suffix=".php">src</directory>
    </include>
    <exclude>
      <directory suffix="App.php">src/App</directory>
      <directory suffix=".php">tests</directory>
      <directory suffix=".php">vendor</directory>
    </exclude>
  </coverage>
  <testsuites>
    <testsuite name="rest-api-slim-php">
      <directory>tests</directory>
    </testsuite>
  </testsuites>
</phpunit>

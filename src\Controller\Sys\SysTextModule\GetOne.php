<?php

declare(strict_types=1);

namespace App\Controller\Sys\SysTextModule;

use Slim\Http\Request;
use Slim\Http\Response;

final class GetOne extends Base
{
    public function __invoke(Request $request, Response $response, array $args): Response
    {
        $id = $args['id'] ?? null;
        if (!$id) {
            \App\Exception\Base::raiseNotFound('Sys text module not found by number ' . $id);
        }

		$row = $this->sysTextModuleService->getOne($id);

        return $this->jsonResponse($response, 'success', $row, 200);
    }
}

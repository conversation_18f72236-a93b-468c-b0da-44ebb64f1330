<?php

declare(strict_types=1);

namespace App\Controller\Offer\OfferTemplate;

use Slim\Http\Request;
use Slim\Http\Response;

final class Create extends Base
{
    public function __invoke(Request $request, Response $response): Response
    {
        $input = (array)$request->getParsedBody();

		// $this->validate($input);

        $row = $this->offerTemplateService->create($input);

        return $this->jsonResponse($response, 'success', $row, 201);
    }
}

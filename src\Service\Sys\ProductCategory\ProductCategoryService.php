<?php

declare(strict_types=1);

namespace App\Service\Sys\ProductCategory;

use App\Models\Sys\ProductCategory;

final class ProductCategoryService extends Base
{
    public function create(array $input): ProductCategory
    {
        return ProductCategory::create($input);
    }

    public function update($id, $input): ProductCategory
    {
        $row = ProductCategory::findOrFail($id);
        $row->update($input);
        return $row;
    }

    public function getOne(int $id, array $params=[]): ProductCategory
    {
        return $this->productCategoryRepository->getQueryBuilder()->where('id', $id)->first();
    }

    public function getProductCategorysByPage(
        int $page,
        int $perPage,
        ?array $params
    ): array
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($perPage < 1) {
            $perPage = self::DEFAULT_PER_PAGE_PAGINATION;
        }

        return $this->productCategoryRepository->getProductCategorysByPage(
            $page,
            $perPage,
            $params
        );
    }

    public function delete($ids): void
    {
        $arr = is_array($ids) ? $ids : explode(',', $ids);
        $this->productCategoryRepository->getQueryBuilder()->whereIn('id', $arr)->delete();
    }
}

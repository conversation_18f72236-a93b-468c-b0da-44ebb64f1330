<?php

declare(strict_types=1);

use App\Repository\Customer\CustomerAddressRepository;
use App\Repository\Customer\CustomerCallRepository;
use App\Repository\Customer\CustomerContactRepository;
use App\Repository\Customer\CustomerExtRepository;
use App\Repository\Customer\CustomerRepository;
use App\Repository\DummyRepository;
use App\Repository\Email\EmailAccountRepository;
use App\Repository\Email\EmailRepository;
use App\Repository\Email\EmailServerRepository;

use App\Repository\Email\EmailTemplateRepository;
use App\Repository\Offer\OfferBlogRepository;
use App\Repository\Offer\OfferExtRepository;
use App\Repository\Offer\OfferNewsletterRepository;
use App\Repository\Offer\OfferTemplateLangRepository;
use App\Repository\Offer\OfferTemplateRepository;
use App\Repository\Supplier\SupplierAddressRepository;
use App\Repository\Supplier\SupplierContactRepository;
use App\Repository\Supplier\SupplierRepository;
use App\Repository\Sys\SysLogRepository;
use App\Repository\Sys\SysTextModuleRepository;
use App\Repository\Sys\CountryRegionRepository;
use Psr\Container\ContainerInterface;
use App\Repository\UserRepository;
use App\Repository\FileRepository;
use App\Repository\Sys\DictRepository;
use App\Repository\Sys\CountryRepository;

$repoMap2 = [
    UserRepository::class,
    FileRepository::class,
    \App\Repository\Supplier\SupplierCallRepository::class,
    \App\Repository\Supplier\SupplierExtRepository::class,

    // ImportSupplierData related
    SupplierRepository::class,
    SupplierAddressRepository::class,
    SupplierContactRepository::class,

    CustomerRepository::class,
    CustomerAddressRepository::class,
    CustomerContactRepository::class,
    CustomerExtRepository::class,
    CustomerCallRepository::class,

    OfferTemplateRepository::class,
    OfferTemplateLangRepository::class,
    OfferExtRepository::class,
    OfferBlogRepository::class,
    OfferNewsletterRepository::class,

    DictRepository::class,

    EmailServerRepository::class,
    EmailRepository::class,
    EmailAccountRepository::class,
    EmailTemplateRepository::class,
    \App\Repository\Email\EmailTemplateCategoryRepository::class,

    CountryRepository::class,
    CountryRegionRepository::class,
    SysTextModuleRepository::class,
    SysLogRepository::class,
    \App\Repository\Sys\ProductCategoryRepository::class,

    DummyRepository::class,
];

foreach ($repoMap2 as $class) {
    $container[$class] = static function (ContainerInterface $container) use ($class) {
        return new $class($container->get('dbORM'), $container->get('logger'));
    };
}

// Note: We open DB connection first.
$container->get(UserRepository::class);
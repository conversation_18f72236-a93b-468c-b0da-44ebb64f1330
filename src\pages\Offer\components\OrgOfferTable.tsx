import { DEFAULT_PER_PAGE_PAGINATION } from "@/constants";
import Util, { sn } from "@/util";
import { ActionType, ProColumns, ProTable } from "@ant-design/pro-components";
import { Space, Typography } from "antd";
import { useEffect, useRef, useState } from "react";
import { getOrgOfferListByPage } from "@/services/app/Offer/org-offer";
import { ReloadOutlined } from "@ant-design/icons";

type RowType = APIOrg.Offer;

type OrgOfferTableProps = {
  supp_supplier_id: number;
  reloadTick?: number;
};

const OrgOfferTable: React.FC<OrgOfferTableProps> = (props) => {
  const { supp_supplier_id, reloadTick } = props;

  const actionRef = useRef<ActionType>();
  const [loading, setLoading] = useState<boolean>(false);

  const columns: ProColumns<RowType>[] = [
    {
      title: "Offer No",
      dataIndex: "offer_sid",
      width: 80,
      sorter: true,
      render: (__, entity) => (
        <Typography.Link href={`${PUBLIC_PATH}offers/detail/${entity.offer_sid}`} target="_blank">
          {entity.offer_sid}
        </Typography.Link>
      ),
    },
    {
      title: "Offer",
      dataIndex: "offer",
      width: 150,
      ellipsis: true,
    },
    {
      title: "WE",
      dataIndex: "we",
      width: 80,
      align: "center",
    },
    {
      title: "Brand",
      dataIndex: "brand",
      width: 80,
      align: "center",
      render: (__, record) => (record.brand ? "Y" : ""),
    },
  ];

  useEffect(() => {
    if (supp_supplier_id || reloadTick) {
      actionRef.current?.reload();
    }
  }, [supp_supplier_id, reloadTick]);

  return (
    <ProTable<RowType, API.PageParams>
      headerTitle={false}
      actionRef={actionRef}
      rowKey="id"
      size="small"
      revalidateOnFocus={false}
      options={{ density: false, setting: false, reload: false }}
      search={false}
      sticky
      scroll={{ x: 400 }}
      cardProps={{ bodyStyle: { padding: 0 }, headStyle: { display: "none" } }}
      pagination={{
        defaultPageSize: 5,
        hideOnSinglePage: true,
      }}
      request={async (params, sort, filter) => {
        setLoading(true);
        return getOrgOfferListByPage({ ...params, supp_supplier_id: supp_supplier_id, with: "supplier" }, sort, filter).finally(() =>
          setLoading(false),
        );
      }}
      onRequestError={Util.error}
      columns={columns}
      columnEmptyText=""
    />
  );
};

export default OrgOfferTable;

<?php

declare(strict_types=1);

namespace App\Service\Supplier\SupplierAddress;

use App\Models\Supplier\SupplierAddress;


final class SupplierAddressService extends Base
{
    public function create(array $input): SupplierAddress
    {
        return SupplierAddress::create($input);
    }

    public function update($id, $input): SupplierAddress
    {
        $row = SupplierAddress::findOrFail($id);
        $row->update($input);
        return $row;
    }

    public function getOne(int $id, array $params=[]): SupplierAddress
    {
        return $this->supplierAddressRepository->getQueryBuilder()->where('id', $id)->first();
    }

    public function getSupplierAddressesByPage(
        int $page,
        int $perPage,
        ?array $params
    ): array
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($perPage < 1) {
            $perPage = self::DEFAULT_PER_PAGE_PAGINATION;
        }

        return $this->supplierAddressRepository->getSupplierAddressesByPage(
            $page,
            $perPage,
            $params
        );
    }

    public function delete($ids): void
    {
        $arr = is_array($ids) ? $ids : explode(',', $ids);
        $this->supplierAddressRepository->getQueryBuilder()->whereIn('id', $arr)->delete();
    }
}

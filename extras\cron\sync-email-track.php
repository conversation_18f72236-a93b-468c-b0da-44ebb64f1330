<?php
/**
 *
 * Latest down sync of Email Tracking History.
 *
 * The cron will fetch tracking history data from WHC Media Server.
 *
 * @package     Cron job script.
 * @since       2025-05-12
 *
 * @recommendedPeriod: 15 mins.
 */
require __DIR__ . '/../../src/App/App.php';

/** @var \Slim\Container $container */



error_reporting(E_ALL);

use App\Service\MediaApi\MediaApiBaseService;

/** @var MediaApiBaseService $mediaApiService */
$mediaApiService = $container->get(MediaApiBaseService::class);

$mediaApiService->dsEmailTrackingHistory();

<?php

declare(strict_types=1);

namespace App\Handler;

use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;

final class ApiError extends BaseError
{
    public function __invoke(
        Request $request,
        Response $response,
        \Exception $exception
    ): Response {
        $this->message = $exception->getMessage();
        return parent::__invoke($request, $response, $exception);
    }
}

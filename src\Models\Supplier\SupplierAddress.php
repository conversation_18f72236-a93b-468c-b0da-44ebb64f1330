<?php

namespace App\Models\Supplier;

use App\Models\BaseModel;
use App\Models\Sys\Country;
use App\Models\Sys\CountryRegion;

/**
 * @property integer $id
 * @property integer $supplier_id
 * @property string $company
 * @property string $country_id
 * @property string $city
 * @property string $fax
 * @property string $postcode
 * @property string $region
 * @property integer $region_id
 * @property string $street1
 * @property string $street2
 * @property string $telephone
 * @property string $created_on
 * @property string $updated_on
 *
 * @property Supplier $supplier
 * @property Country $country
 * @property CountryRegion $countryRegion
 *
 * @property string $street
 */
class SupplierAddress extends BaseModel
{
    protected $casts = [

    ];


    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'supplier_address';

    /**
     * @var array
     */
    protected $fillable = [
        'supplier_id'
        , 'company'
        , 'country_id', 'city', 'fax'
        , 'postcode', 'region', 'region_id', 'street1','street2'
        , 'telephone', 'created_on', 'updated_on'
    ];

    protected $appends = [
        'fullname',
        'street',
    ];

    public static function getBoundData(&$item, $applyReverseCast = false): array
    {
        $data = parent::getBoundData($item, $applyReverseCast);

        return $data;
    }

    public function getFullnameAttribute()
    {
        $firstname = $this->getAttribute('firstname');
        $lastname = $this->getAttribute('lastname');
        return ($firstname || $lastname) ? $firstname . ' ' . $lastname : null;

    }

    public function getStreetAttribute()
    {
        $street1 = $this->getAttribute('street1');
        $street2 = $this->getAttribute('street2');
        return ($street1 || $street2) ? $street1 . ' ' . $street2 : null;

    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function supplier()
    {
        return $this->belongsTo('App\Models\Supplier\Supplier', 'supplier_id', 'id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function country()
    {
        return $this->belongsTo('App\Models\Sys\Country', 'country_id', 'code');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function countryRegion()
    {
        return $this->belongsTo('App\Models\Sys\CountryRegion', 'region_id', 'id');
    }

}

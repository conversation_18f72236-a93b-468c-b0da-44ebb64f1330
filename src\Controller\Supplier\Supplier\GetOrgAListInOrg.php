<?php

declare(strict_types=1);

namespace App\Controller\Supplier\Supplier;

use App\ModelsOrg\OrgSupplier;
use Slim\Http\Request;
use Slim\Http\Response;

final class GetOrgAListInOrg extends Base
{
    public function __invoke(Request $request, Response $response): Response
    {
        $data = OrgSupplier::query()->distinct()->select('org_a')->pluck('org_a', 'org_a')->toArray();

        return $this->jsonResponse($response, 'success', $data, 200);
    }
}

<?php

namespace App\Models\Supplier;

use App\Models\BaseModel;

/**
 * @property string $id
 * @property string $supplier_id
 * @property string $comment
 * @property string $created_by
 * @property string $updated_by
 * @property string $created_on
 * @property string $updated_on
 * @property string $code
 * @property string $offer_id
 * @property string $customer_order
 * @property string $order_id
 */
class SupplierComment extends BaseModel
{
    public $timestamps = true;

    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'supplier_comment';

    /**
     * The "type" of the auto-incrementing ID.
     * 
     * @var string
     */
    protected $keyType = 'string';

    /**
     * Indicates if the IDs are auto-incrementing.
     * 
     * @var bool
     */
    public $incrementing = false;

    /**
     * @var array
     */
    protected $fillable = ['supplier_id', 'comment', 'created_by', 'updated_by', 'created_on', 'updated_on', 'code', 'offer_id', 'customer_order', 'order_id'];
}

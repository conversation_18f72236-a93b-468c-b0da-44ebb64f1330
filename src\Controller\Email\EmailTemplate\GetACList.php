<?php

declare(strict_types=1);

namespace App\Controller\Email\EmailTemplate;

use Slim\Http\Request;
use Slim\Http\Response;

final class GetACList extends Base
{
    public function __invoke(Request $request, Response $response): Response
    {
        // $page = $request->getQueryParam('page', null);
        // $perPage = $request->getQueryParam('perPage', null);
        $params = $request->getParams();

        $qb = $this->emailTemplateService->getEmailTemplateRepository()->getQueryEmailTemplatesByPage($params);
        $qb->select([])
            ->selectRaw('id')
            ->selectRaw('title')
            ->selectRaw('subject')
            ->selectRaw('category_id')
            ->selectRaw('sort')
            ->with('category');

        $data = $qb->get()->toArray();

        return $this->jsonResponse($response, 'success', $data, 200);
    }
}

<?php

declare(strict_types=1);

namespace App\Controller\Offer\OfferBlog;

use App\Exception\BaseException;
use Slim\Http\Request;
use Slim\Http\Response;

final class UpdateOrCreate extends Base
{
    public function __invoke(Request $request, Response $response): Response
    {
        $input = (array)$request->getParsedBody();

        $offer_no = $input['offer_no'] ?? null;
        if (!$offer_no) {
            BaseException::raiseInvalidRequest('Offer No is required!');
        }

        $uploadedFiles = $request->getUploadedFiles();

		$row = $this->offerBlogService->updateOrCreate($input, $uploadedFiles['files'] ?? []);

        return $this->jsonResponse($response, 'success', $row, 201);
    }
}

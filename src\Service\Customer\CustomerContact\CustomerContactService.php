<?php

declare(strict_types=1);

namespace App\Service\Customer\CustomerContact;

use App\Models\Customer\CustomerContact;

final class CustomerContactService extends Base
{
    public function create(array $input): CustomerContact
    {
        return CustomerContact::create($input);
    }

    public function update($id, $input): CustomerContact
    {
        $row = CustomerContact::findOrFail($id);
        $row->update($input);
        return $row;
    }

    public function getOne(int $id, array $params=[]): CustomerContact
    {
        return $this->customerContactRepository->getQueryBuilder()->where('id', $id)->first();
    }

    public function getCustomerContactsByPage(
        int $page,
        int $perPage,
        ?array $params
    ): array
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($perPage < 1) {
            $perPage = self::DEFAULT_PER_PAGE_PAGINATION;
        }

        return $this->customerContactRepository->getCustomerContactsByPage(
            $page,
            $perPage,
            $params
        );
    }

    public function delete($ids): void
    {
        $arr = is_array($ids) ? $ids : explode(',', $ids);
        $this->customerContactRepository->getQueryBuilder()->whereIn('id', $arr)->delete();
    }
}

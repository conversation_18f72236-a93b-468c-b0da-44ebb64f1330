<?php

namespace App\Models\Email;

use App\Models\BaseModel;

/**
 * @property integer $id
 * @property integer $server_id
 * @property string $email
 * @property string $pop_type
 * @property string $password
 * @property boolean $status
 * @property string $sender_name
 * @property string $settings
 * @property string $created_on
 * @property integer $created_by
 * @property string $updated_on
 * @property integer $updated_by
 * @property Email[] $emails
 * @property EmailServer $emailServer
 */
class EmailAccount extends BaseModel
{
    protected $casts = [
        'settings' => 'array'
    ];

    public $timestamps = true;

    protected $hidden = ['password'];

    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'email_account';

    /**
     * @var array
     */
    protected $fillable = ['server_id', 'email', 'pop_type', 'password', 'status', 'sender_name', 'settings', 'created_on', 'created_by', 'updated_on', 'updated_by'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function emails()
    {
        return $this->hasMany('App\Models\Email\Email');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function emailServer()
    {
        return $this->belongsTo('App\Models\Email\EmailServer', 'server_id');
    }
}

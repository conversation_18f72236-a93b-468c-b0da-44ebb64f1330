<?php

declare(strict_types=1);

namespace App\Service\Sys\Trademark;

use App\Service\BaseService;
use App\Repository\Sys\TrademarkRepository;
use Slim\Container;

abstract class Base extends BaseService
{
    protected TrademarkRepository $trademarkRepository;

    public function __construct(Container $container)
    {
        $this->trademarkRepository = $container->get(TrademarkRepository::class);
    }

    public function getTrademarkRepository()
    {
        return $this->trademarkRepository;
    }
}


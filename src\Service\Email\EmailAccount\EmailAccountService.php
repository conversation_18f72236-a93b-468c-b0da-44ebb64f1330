<?php

declare(strict_types=1);

namespace App\Service\Email\EmailAccount;

use App\Lib\Func;
use App\Models\Email\EmailAccount;

final class EmailAccountService extends Base
{
    public const SEC_KEY = '+#b%%qwnYl3Za%-twvKn-CsN8NrMJw8nKWxVq0ABDEjgJNc55YIw5OTbDxhhJ+=d0pLb++Vf@xrijolepgz8Nm9vaySVUXdSK2I3usIv=EA%F9lf8J8lfgc8s=btb6oP';
    public const SEC_IV = 'KWxVq0ABDEjgJNc55YIw5OTbDxhhJ+=d0pLb++Vf@xrijolepg';

    public function create(array $input): EmailAccount
    {
        $password = $input['password'] ?? '';
        $input['password'] = Func::mcrypt('encrypt', $password, self::SEC_KEY, self::SEC_IV);

        return EmailAccount::query()->create($input);
    }

    public function update($id, $input): EmailAccount
    {
        $password = $input['password'] ?? '';
        if ($password) {
            $input['password'] = Func::mcrypt('encrypt', $password, self::SEC_KEY, self::SEC_IV);
        }

        $row = EmailAccount::findOrFail($id);
        if (key_exists('settings', $input)) {
            $input['settings'] = array_replace_recursive($row->settings ?? [], $input['settings']);
        }

        $row->update($input);
        return $row;
    }

    public function getOne(int $id, array $params=[]): EmailAccount
    {
        return $this->emailAccountRepository->getQueryBuilder()->where('id', $id)->first();
    }

    public function getEmailAccountsByPage(
        int $page,
        int $perPage,
        ?array $params
    ): array
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($perPage < 1) {
            $perPage = self::DEFAULT_PER_PAGE_PAGINATION;
        }

        return $this->emailAccountRepository->getEmailAccountsByPage(
            $page,
            $perPage,
            $params
        );
    }

    public function delete($ids): void
    {
        $arr = is_array($ids) ? $ids : explode(',', $ids);
        $this->emailAccountRepository->getQueryBuilder()->whereIn('id', $arr)->delete();
    }
}

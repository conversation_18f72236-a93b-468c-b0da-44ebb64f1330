<?php

declare(strict_types=1);

namespace App\Controller\Customer\Customer;

use App\Controller\BaseController;
use App\Service\Customer\Customer\CustomerService;
use Respect\Validation\Validator as v;
use Slim\Container;

abstract class Base extends BaseController
{
    public CustomerService $customerService;

    public function __construct(Container $container)
    {
        parent::__construct($container);
        $this->customerService = $container->get(CustomerService::class);
    }

	public function validate(array $input): bool {
        $validator = v::key('name', v::stringType()->length(1, 255));

        if (!$validator->validate($input)) {
            \App\Exception\Base::raiseInvalidRequest();
        }
        return true;
    }
}

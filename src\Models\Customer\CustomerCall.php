<?php

namespace App\Models\Customer;

use App\Lib\SysMsg;
use App\Models\BaseModel;
use App\Models\File;
use App\Models\Customer\Customer;

/**
 * @property integer $id
 * @property integer $customer_id
 * @property integer $type
 * @property string $direction

 * @property integer $offer_no
 * @property string $ref_type
 * @property string $ref_id

 * @property string $note
 * @property string $created_on
 * @property integer $created_by
 * @property string $updated_on
 * @property integer $updated_by
 *
 * @property Customer $customer
 * @property File[] $files
 */
class CustomerCall extends BaseModel
{
    public const TYPE_PHONE = 1;
    public const TYPE_NOTE = 2;
    public const TYPE_EMAIL = 3;
    public const TYPE_OFFER = 4;
    public const TYPE_MEETING = 5;

    public const DIRECTION_IN = 'I'; // incoming to WHC
    public const DIRECTION_OUT = 'O';  // outgoing from WHC
    public const DIRECTION_WHC = 'W'; // between WHC staffs

    public const TYPE_LABELS = [
        self::TYPE_PHONE  => 'Phone',
        self::TYPE_NOTE  => 'Note',
    ];

    public $timestamps = true;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'customer_call';

    /**
     * @var array
     */
    protected $fillable = ['customer_id', 'type', 'direction'
        , 'offer_no'
        , 'ref_type'
        , 'ref_id'
        , 'note', 'created_on', 'created_by', 'updated_on', 'updated_by'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function customer()
    {
        return $this->belongsTo('App\Models\Customer\Customer', 'customer_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    /*public function files()
    {
        return $this->belongsToMany('App\Models\File', 'customer_call_file', 'call_id');
    }*/
}

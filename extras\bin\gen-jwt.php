<?php

declare(strict_types=1);

use App\Lib\Func;
use \Firebase\JWT\JWT;

require __DIR__ . '/../../src/App/App.php';


echo PHP_EOL. "--------------------------------------" . PHP_EOL;
echo "IPFS JWT:" . PHP_EOL;
/** @var \Slim\Container $container */
$email = $container->get('settings')['app']['ipfsUser'] ?? '<EMAIL>';
$token = [
    'sub' => 1000,
    'email' => $email,
    'name' => $email,
    'iat' => time(),
    'exp' => time() + (10 * 24 * 60 * 60),
];

echo JWT::encode($token, $_SERVER['SECRET_KEY'], 'HS512');
// echo JWT::encode($token, $_SERVER['SECRET_KEY'], 'HS512');

echo PHP_EOL;
// var_dump(bin2hex(openssl_random_pseudo_bytes(16)));


// Generate random secrety key for JWT HS512 (64byte)
var_dump(Func::randomPassword(129));

echo PHP_EOL;

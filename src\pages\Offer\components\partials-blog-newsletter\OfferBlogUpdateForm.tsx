import HtmlEditor from "@/components/HtmlEditor";
import { deleteFile } from "@/services/app/File/file";
import { copyFileAndResize, getOfferBlogByOfferNo, updateOrCreateOfferBlogByOfferNo } from "@/services/app/Offer/offer-blog";
import Util, { sn } from "@/util";
import { ProForm, ProFormInstance, ProFormRadio, ProFormText, ProFormUploadDragger } from "@ant-design/pro-components";
import { Button, message, Modal, Space, Spin, Image, Popconfirm } from "antd";
import { RcFile } from "antd/es/upload";
import { useCallback, useEffect, useRef, useState } from "react";
import styles from "./OfferBlogUpdateForm.less";
import { UploadFile } from "antd/lib";
import { DeleteOutlined, FullscreenExitOutlined } from "@ant-design/icons";

type FormValueType = API.OfferBlog;

export type OfferBlogUpdateFormProps = {
  offer_no: string;
  orgOffer: Partial<APIOrg.Offer>;
  reloadTick: number;
  onSubmit?: (formData: FormValueType) => void;
  loadOrgOfferDetail?: () => void;
};

const OfferBlogUpdateForm: React.FC<OfferBlogUpdateFormProps> = ({ offer_no, orgOffer, onSubmit, reloadTick, loadOrgOfferDetail }) => {
  const formRef = useRef<ProFormInstance<FormValueType>>();

  const [loading, setLoading] = useState<boolean>(false);

  const [blog, setBlog] = useState<API.OfferBlog>();

  const loadOfferBlog = useCallback(() => {
    setLoading(true);
    getOfferBlogByOfferNo(offer_no, { with: "files" })
      .then((res) => {
        setBlog(res);
        formRef.current?.setFieldsValue(res);
      })
      .catch(Util.error)
      .finally(() => setLoading(false));
  }, [offer_no]);

  useEffect(() => {
    loadOfferBlog();
  }, [loadOfferBlog]);

  useEffect(() => {
    if (reloadTick) {
      loadOfferBlog();
    }
  }, [reloadTick, loadOfferBlog]);

  return (
    <div className={styles.offerBlogUpdateForm} style={{ opacity: blog?.status ? 1 : 0.8 }}>
      <Spin spinning={loading} style={{ width: "100%" }}>
        <ProForm<FormValueType> layout="vertical" formRef={formRef} isKeyPressSubmit className="search-form" grid submitter={false}>
          <ProFormText name={"title"} label={"Title"} colProps={{ span: 18 }} />
          <ProFormRadio.Group
            name={"status"}
            label={"Active?"}
            options={[
              { label: "Active", value: 1 },
              { label: "Inactive", value: 0 },
            ]}
            colProps={{ span: 6 }}
          />
          <ProForm.Item
            name={"description"}
            label={<>Description</>}
            style={{ width: "100%" }}
            labelCol={undefined}
            wrapperCol={{ span: 24 }}
            rules={[
              {
                required: true,
                message: "Body content is required",
              },
            ]}
          >
            <HtmlEditor id={`offer_blog_description`} enableTextModule hideMenuBar toolbarMode={2} height={300} />
          </ProForm.Item>

          <ProFormUploadDragger
            name={["files"]}
            label={"Blog Files"}
            title={false}
            description="Please select blog files or drag & drop"
            wrapperCol={{ span: 24 }}
            fieldProps={{
              multiple: false,
              listType: "picture-card",
              name: "files",
              accept: "image/*",
              style: { marginBottom: 24 },
              height: 120,
              beforeUpload: (file: RcFile, fileList: RcFile[]) => {
                return false;
              },
              onRemove: async (file: API.File) => {
                if (file.id) {
                  const { confirm } = Modal;
                  return new Promise((resolve, reject) => {
                    confirm({
                      title: "Are you sure you want to delete?",
                      onOk: async () => {
                        resolve(true);
                        const hide = message.loading(`Deleting a file '${file.file_name}'.`, 0);
                        const res = await deleteFile(sn(file.id));
                        hide();
                        if (res) {
                          message.success(`Deleted successfully!`);
                        } else {
                          Util.error(`Delete failed, please try again!`);
                        }

                        return res;
                      },
                      onCancel: () => {
                        reject(true);
                      },
                    });
                  });
                } else {
                  return true;
                }
              },
              itemRender: (originNode: React.ReactElement, file, fileList, actions) => {
                return (
                  <Space direction="vertical" size={6}>
                    <Image
                      src={(file as API.File).id ? (file as API.File).thumb_url : file.thumbUrl}
                      preview={{
                        src: file.url,
                      }}
                      width={80}
                      height={80}
                    />
                    <Space size={12} style={{ justifyContent: "center", width: "100%" }}>
                      {!!(file as API.File).id && (
                        <Popconfirm
                          title={<>Are you sure you want to duplicate this image by 400 x 400 px?</>}
                          okText="Yes"
                          cancelText="No"
                          styles={{ root: { maxWidth: 300 } }}
                          onConfirm={() => {
                            const hide = message.loading("Creating a new file with 400x400...", 0);
                            copyFileAndResize(offer_no, sn((file as API.File).id))
                              .then((res) => {
                                hide();
                                message.success("Created successfully.");
                                loadOfferBlog();
                                loadOrgOfferDetail?.();
                              })
                              .catch(Util.error)
                              .finally(() => {
                                hide();
                              });
                          }}
                        >
                          <Button size="small" variant="outlined" color="blue" icon={<FullscreenExitOutlined />} title="Resize" />
                        </Popconfirm>
                      )}
                      <Button size="small" variant="outlined" color="danger" icon={<DeleteOutlined />} title="Delete" onClick={actions.remove} />
                    </Space>
                  </Space>
                );
              },
            }}
          />

          <Space size={16} style={{ marginLeft: "auto" }}>
            <Button
              type="default"
              onClick={() => {
                formRef.current?.resetFields();
              }}
            >
              Reset
            </Button>
            <Button
              type="primary"
              onClick={() => {
                const formValues = formRef.current?.getFieldsValue();
                const formData = new FormData();
                formData.set("offer_no", offer_no);
                formData.set("title", formValues?.title || "");
                formData.set("description", formValues?.description || "");
                formData.set("status", `${formValues?.status ?? 1}`);
                if (formValues?.files?.length) {
                  formValues?.files.forEach((file, ind) => {
                    console.log(file);
                    if (!sn(file.uid)) {
                      formData.set(`files[${ind}]`, (file as UploadFile).originFileObj || "");
                    }
                  });
                }

                const hide = message.loading("Saving blog data...", 0);
                updateOrCreateOfferBlogByOfferNo(formData)
                  .then((res) => {
                    hide();
                    message.success("Saved successfully.");
                    if (onSubmit) onSubmit(res);
                    loadOfferBlog();
                    loadOrgOfferDetail?.();
                  })
                  .catch(Util.error)
                  .finally(() => {
                    hide();
                  });
              }}
            >
              Save Blog
            </Button>
          </Space>
        </ProForm>
      </Spin>
    </div>
  );
};
export default OfferBlogUpdateForm;

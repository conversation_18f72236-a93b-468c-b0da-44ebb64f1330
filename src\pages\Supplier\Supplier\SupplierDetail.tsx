import { updateSupplierExtBySupplierId } from "@/services/app/Supplier/supplier-ext";
import { getSupplier, updateSupplierMetaSupplierId } from "@/services/app/Supplier/supplier";
import Util, { nf2, sn } from "@/util";
import { InfoCircleOutlined } from "@ant-design/icons";
import {
  PageContainer,
  ProColumns,
  ProDescriptions,
  ProForm,
  ProFormCheckbox,
  ProFormInstance,
  ProFormRadio,
  ProFormSelect,
  ProFormTextArea,
  ProTable,
} from "@ant-design/pro-components";
import { useModel, useParams } from "@umijs/max";
import { Card, Col, message, Popover, Row, Space, Spin, Typography } from "antd";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import SupplierCallList from "./components/SupplierCallList";
import CreateEmailForm from "@/pages/Email/EmailList/components/CreateEmailForm";
import { DefaultContactIconComp } from ".";
import { getProductCategoryACList } from "@/services/app/Sys/product-category";
import { useTheme } from "antd-style";
import { UserRole } from "@/constants";
import OrgOfferTable from "@/pages/Offer/components/OrgOfferTable";
import { getUserSettingOrgStatsItemOptionsByLevel, UserSettingOrgStatsItemEnum } from "@/pages/UsersList/components/UserSettingForm";

export const ProductTrademarkOptions = ["Marke", "Private Label"];
export const SupplierTypeOptions = ["Production", "Trader", "Broker", "Other"];
// const YEARS = [4, 3, 2, 1, 0].map((x) => new Date().getFullYear() - x);

type RecordType = APITask.Supplier & APITask.FinDetail;

const SupplierDetail: React.FC = (props) => {
  const params = useParams();
  const { supplierId } = params || {};

  const theme = useTheme();
  const { initialState } = useModel("@@initialState");
  const userRoleId = sn(initialState?.currentUser?.role);

  const formRefTop = useRef<ProFormInstance>();

  const [loading, setLoading] = useState(false);
  const [supplier, setSupplier] = useState<API.Supplier>();

  const [receiver, setReceiver] = useState<string>("");
  const [replyModalVisible, handleReplyModalVisible] = useState<boolean>(false); // to Supplier

  // Stats in WHC_Task
  const [statsMode, setStatsMode] = useState("EK_Summe"); // Stats mode to show Summe or EK_Summe or Ertrag from WHC_Task
  const [org_stats_user_level, set_org_stats_user_level] = useState<UserSettingOrgStatsItemEnum | string>(UserSettingOrgStatsItemEnum.Option3);

  const loadSupplierDetail = useCallback(() => {
    if (supplierId) {
      setLoading(true);
      getSupplier(sn(supplierId), {
        org_stats_user_level,
        with: "ext,ext.user,calls,address,address.country,address.countryRegion,meta,contacts,taskInvoiceStats",
      })
        .then((res) => {
          setSupplier(res);
        })
        .catch(Util.error)
        .finally(() => {
          setLoading(false);
        });
    } else {
      setSupplier(undefined);
    }
  }, [org_stats_user_level, supplierId]);

  useEffect(() => {
    set_org_stats_user_level(initialState?.currentUser?.settings?.org_stats_item ?? UserSettingOrgStatsItemEnum.Option3);
  }, [initialState?.currentUser?.settings?.org_stats_item]);

  useEffect(() => {
    loadSupplierDetail();
  }, [loadSupplierDetail]);

  useEffect(() => {
    formRefTop.current?.setFieldValue(["ext", "note"], supplier?.ext?.note || "");
  }, [supplier?.ext?.note]);

  useEffect(() => {
    const metaFormData: any = { product_trademark: [], product_category: [], supplier_type: null };
    if (supplier?.meta?.length) {
      for (const x of supplier?.meta) {
        if (x.type == "product_trademark" || x.type == "product_category") {
          metaFormData[`${x.type}`].push(x.type == "product_category" ? sn(x.value) : x.value);
        } else if (x.type == "supplier_type") {
          metaFormData[`${x.type}`] = x.value;
        }
      }
    }
    formRefTop.current?.setFieldValue("metaData", metaFormData);
  }, [supplier?.meta]);

  const contacts = supplier?.contacts || [];

  const YEARS = useMemo(() => {
    return Util.dtBuildFyList(Util.dtCurrentFy(), 4, true);
  }, []);

  const columnsYearly: ProColumns<RecordType>[] = useMemo(() => {
    return [
      ...(YEARS.map((x, mInd) => ({
        title: x.label,
        dataIndex: `${statsMode}_${mInd}`,
        search: false,
        width: 85,
        align: "right",
        className: mInd == 0 ? "bl2 b-gray cursor-pointer" : "cursor-pointer",
        render: (__: any, record: any) => {
          // const value = sn(record[`${statsMode}_${mInd}`]);
          // return nf2(value);
          const value = sn(record[`${statsMode}_${mInd}`]);

          if (userRoleId == UserRole.ADMIN || userRoleId == UserRole.USER_FULL) {
            return nf2(value);
          } else if (userRoleId == UserRole.USER_NORMAL) {
            if (value > 50_000) {
              return "> 50K";
            } else if (value > 10_000) {
              return "> 10K";
            } else if (value <= 10_000) {
              // return "< 10K";
              return nf2(value);
            }
          }
          return null;
        },
      })) as any),
      /* {
        title: "Total",
        dataIndex: `${statsMode}_total`,
        search: false,
        width: 85,
        align: "right",
        className: "bl2 b-gray",
        render: (__: any, record: any) => {
          const value = sn(record[`${statsMode}_total`]);
          return nf2(value);
        },
      }, */
    ];
  }, [YEARS, statsMode, userRoleId]);

  return (
    <PageContainer title={false}>
      <ProForm layout="inline" formRef={formRefTop} isKeyPressSubmit className="search-form" submitter={false}>
        <Spin spinning={loading} wrapperClassName="w-full">
          <Card variant="borderless" style={{ marginBottom: 8, width: "100%" }}>
            <Row gutter={24}>
              <Col span={12}>
                <Row gutter={24}>
                  <Col span={15}>
                    <ProDescriptions<API.Supplier>
                      column={4}
                      size="middle"
                      colon={false}
                      styles={{
                        label: { alignItems: "center", color: theme.colorText },
                        content: { lineHeight: "18px" },
                      }}
                      columns={[
                        {
                          dataIndex: "name",
                          render(__, entity) {
                            return supplier && <span style={{ fontSize: 18, fontWeight: "bold" }}>{`${supplier?.name}`}</span>;
                          },
                        },
                        {
                          dataIndex: "address",
                          span: 3,
                          contentStyle: { flexWrap: "wrap" },
                          render(__, entity) {
                            return supplier?.address ? (
                              <Row
                                key={`${supplier?.address?.postcode}`}
                                style={{
                                  width: "100%",
                                }}
                                gutter={8}
                                wrap={false}
                              >
                                <Col style={{ fontWeight: "bold" }}>{supplier?.address?.company}</Col>
                                <Col>
                                  {supplier?.address?.street} &nbsp;
                                  {supplier?.address?.city} &nbsp;
                                  {supplier?.address?.postcode} &nbsp;
                                  {supplier?.address?.region} &nbsp;
                                  {supplier?.address?.country?.name} &nbsp;
                                </Col>
                              </Row>
                            ) : null;
                          },
                        },
                        {
                          span: 4,
                          dataIndex: "contacts",
                          render(dom, entity) {
                            return (
                              <Row wrap={false} style={{ width: "500px", paddingTop: 12 }}>
                                <Col flex="auto">
                                  {contacts.map((x) => {
                                    return (
                                      <Row key={x.id}>
                                        <Col flex={"20px"}>
                                          <DefaultContactIconComp is_default={x.is_default} is_asp={x.is_asp} is_asp_reg={x.is_asp_reg} />
                                        </Col>
                                        <Col flex="auto" style={{ fontWeight: x.is_default ? "bold" : "normal" }}>
                                          {x.fullname}
                                        </Col>
                                        <Col flex="200px">
                                          <Typography.Link
                                            onClick={() => {
                                              setReceiver(Util.emailBuildSender(x.email, x.fullname));
                                              handleReplyModalVisible(true);
                                            }}
                                            style={{ fontWeight: x.is_default ? "bold" : "normal" }}
                                          >
                                            {x.email}
                                          </Typography.Link>
                                        </Col>
                                        <Col flex="130px">{x.telephone}</Col>
                                        <Col flex={"24px"}>
                                          {!!x.note && (
                                            <Popover content={x.note} styles={{ body: { maxWidth: 300 } }}>
                                              <InfoCircleOutlined />
                                            </Popover>
                                          )}
                                        </Col>
                                      </Row>
                                    );
                                  })}
                                </Col>
                              </Row>
                            );
                          },
                        },
                      ]}
                    />
                  </Col>
                  <Col span={9}>
                    <Space direction="vertical" size={8} wrap>
                      <ProFormCheckbox.Group
                        name={["metaData", "product_trademark"]}
                        options={ProductTrademarkOptions}
                        fieldProps={{
                          onChange(checkedValue) {
                            const dataArr = formRefTop.current?.getFieldValue(["metaData", "product_trademark"]);
                            const data = dataArr.map((x: string) => ({
                              type: "product_trademark",
                              value: x,
                            }));

                            const hide = message.loading("Saving...", 0);
                            setLoading(true);
                            updateSupplierMetaSupplierId(supplier?.id, {
                              meta: { product_trademark: data },
                            })
                              .then((res) => {
                                message.success("Saved successfully.");
                                setSupplier((prev) => ({
                                  ...prev,
                                  meta: res.meta || [],
                                }));
                              })
                              .catch(Util.error)
                              .finally(() => {
                                setLoading(false);
                                hide();
                              });
                          },
                        }}
                      />

                      <ProFormRadio.Group
                        name={["metaData", "supplier_type"]}
                        options={SupplierTypeOptions}
                        fieldProps={{
                          onChange(e) {
                            const hide = message.loading("Saving...", 0);
                            setLoading(true);
                            updateSupplierMetaSupplierId(supplier?.id, {
                              meta: {
                                supplier_type: [{ type: "supplier_type", value: e.target.value }],
                              },
                            })
                              .then((res) => {
                                message.success("Saved successfully.");
                                setSupplier((prev) => ({
                                  ...prev,
                                  meta: res.meta || [],
                                }));
                              })
                              .catch(Util.error)
                              .finally(() => {
                                setLoading(false);
                                hide();
                              });
                          },
                        }}
                      />

                      <ProFormSelect
                        mode="multiple"
                        name={["metaData", "product_category"]}
                        request={getProductCategoryACList}
                        fieldProps={{
                          onChange(checkedValue) {
                            const dataArr = formRefTop.current?.getFieldValue(["metaData", "product_category"]);
                            const data = dataArr.map((x: string) => ({
                              type: "product_category",
                              value: x,
                            }));

                            const hide = message.loading("Saving...", 0);
                            setLoading(true);
                            updateSupplierMetaSupplierId(supplier?.id, {
                              meta: { product_category: data },
                            })
                              .then((res) => {
                                message.success("Saved successfully.");
                                setSupplier((prev) => ({
                                  ...prev,
                                  meta: res.meta || [],
                                }));
                              })
                              .catch(Util.error)
                              .finally(() => {
                                setLoading(false);
                                hide();
                              });
                          },
                        }}
                      />
                    </Space>
                  </Col>
                </Row>
                <Row style={{ marginTop: 16 }} gutter={24}>
                  <Col span={24}>
                    <ProTable<any, API.PageParams>
                      rowKey="supp_supplier_id"
                      headerTitle={
                        <>
                          <ProFormRadio.Group
                            options={[
                              { value: "Summe", label: "Turnover" },
                              { value: "EK_Summe", label: "BP" },
                              { value: "Ertrag", label: "Ertrag" },
                            ]}
                            radioType="button"
                            fieldProps={{
                              defaultValue: statsMode,
                              onChange(e) {
                                setStatsMode(e.target.value);
                              },
                            }}
                          />

                          <ProFormSelect
                            name={["org_stats_user_level"]}
                            options={getUserSettingOrgStatsItemOptionsByLevel(
                              initialState?.currentUser?.settings?.org_stats_item ?? UserSettingOrgStatsItemEnum.Option1,
                            )}
                            width="xs"
                            fieldProps={{
                              value: org_stats_user_level,
                              popupMatchSelectWidth: false,
                              onChange(value, option) {
                                set_org_stats_user_level(value as UserSettingOrgStatsItemEnum);
                              },
                            }}
                            allowClear={false}
                          />
                        </>
                      }
                      search={false}
                      options={false}
                      pagination={{ hideOnSinglePage: true }}
                      tableStyle={{ padding: 0, width: 320 }}
                      cardProps={{ style: { padding: 0 }, bodyStyle: { padding: 0 } }}
                      columns={columnsYearly}
                      bordered
                      /* columns={YEARS.map((x) => {
                        return {
                          key: x,
                          title: `${x}`,
                          dataIndex: `${statsMode}_${x}`,
                          align: "right",
                          width: 80,
                          render: (__: any, record: any) => {
                            const value = sn(record[`${statsMode}_${x}`]);

                            if (userRoleId == UserRole.ADMIN || userRoleId == UserRole.USER_FULL) {
                              return nf2(value);
                            } else if (userRoleId == UserRole.USER_NORMAL) {
                              if (value > 50_000) {
                                return "> 50K";
                              } else if (value > 10_000) {
                                return "> 10K";
                              }
                            }
                            return null;
                          },
                        } as any;
                      })} */
                      dataSource={supplier?.task_invoice_stats ? [supplier?.task_invoice_stats] : []}
                      locale={{ emptyText: <></> }}
                    />
                  </Col>
                </Row>
              </Col>
              <Col span={5}>
                <ProFormTextArea
                  name={["ext", "note"]}
                  placeholder="Offer notes..."
                  fieldProps={{
                    rows: 8,
                    onBlur: () => {
                      const newValue = formRefTop.current?.getFieldValue(["ext", "note"]);
                      const isTouched = formRefTop.current?.isFieldTouched(["ext", "note"]);
                      if (isTouched && newValue != supplier?.ext?.note) {
                        const hide = message.loading("Saving...", 0);
                        setLoading(true);
                        updateSupplierExtBySupplierId(supplier?.id, {
                          note: formRefTop.current?.getFieldValue(["ext", "note"]),
                        })
                          .then((res) => {
                            message.success("Saved successfully.");
                            setSupplier((prev) => ({
                              ...prev,
                              ext: { ...prev?.ext, note: res.note },
                            }));
                          })
                          .catch(Util.error)
                          .finally(() => {
                            setLoading(false);
                            hide();
                          });
                      }
                    },
                  }}
                />
              </Col>
              <Col span={7}>{supplier?.id ? <OrgOfferTable supp_supplier_id={supplier.id} /> : null}</Col>
            </Row>
          </Card>
        </Spin>

        <Row>
          <Col span={24}>
            <SupplierCallList supplier={supplier} loadSupplierDetail={loadSupplierDetail} />
          </Col>
        </Row>

        <CreateEmailForm
          modalVisible={replyModalVisible}
          handleModalVisible={handleReplyModalVisible}
          initialValues={{
            receiver: receiver,
          }}
          supplier={{
            id: supplier?.id,
            name: supplier?.name,
            address: supplier?.address,
            contacts: supplier?.contacts,
            created_on: supplier?.created_on,
          }}
          onSubmit={async (value) => {}}
          onCancel={() => {
            handleReplyModalVisible(false);
          }}
        />
      </ProForm>
    </PageContainer>
  );
};

export default SupplierDetail;

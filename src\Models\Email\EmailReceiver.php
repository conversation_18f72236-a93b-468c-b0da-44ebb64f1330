<?php

namespace App\Models\Email;

use App\Models\BaseModel;
use App\Models\Customer\CustomerContact;
use App\Models\Supplier\SupplierContact;

/**
 * @property integer $email_id
 * @property string $email
 * @property string $name
 * @property integer $open_count
 * @property string $open_first_seen_on
 * @property string $open_seen_updated_on
 *
 * @property Email $emailObj
 *
 */
class EmailReceiver extends BaseModel
{
    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'email_receiver';

    /**
     * @var array
     */
    protected $fillable = ['email_id', 'email', 'name', 'open_count', 'open_first_seen_on', 'open_seen_updated_on'];

    protected function setKeysForSaveQuery($query): \Illuminate\Database\Eloquent\Builder
    {
        $query
            ->where('email_id', '=', $this->getAttribute('email_id'))
            ->where('email', '=', $this->getAttribute('email'));

        return $query;
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function emailObj()
    {
        return $this->belongsTo('App\Models\Email\Email');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function customerContacts()
    {
        return $this->hasMany(CustomerContact::class, 'email', 'email');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function supplierContacts()
    {
        return $this->hasMany(SupplierContact::class, 'email', 'email');
    }

}

<?php

declare(strict_types=1);

namespace App\Controller\Sys\CountryRegion;

use Slim\Http\Request;
use Slim\Http\Response;

final class Update extends Base
{
    public function __invoke(Request $request, Response $response, array $args): Response
    {
        $id = $args['id'] ?? null;
        if (!$id) {
            \App\Exception\Base::raiseNotFound('Country region');
        }

		$input = (array) $request->getParsedBody();
		$this->validate($input);

        $id = intval($id);
        $row = $this->countryRegionService->getOne($id);
		$this->validate($input);
        if (!$row) {
            \App\Exception\Base::raiseNotFound('Country region');
        }

        $isAdmin = $this->isAdminUser($request);

        $row->name = $input['name'];
        $row->value = $input['value'];
        $row->save();

        return $this->jsonResponse($response, 'success', $row, 200);
    }
}

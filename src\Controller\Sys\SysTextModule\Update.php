<?php

declare(strict_types=1);

namespace App\Controller\Sys\SysTextModule;

use Slim\Http\Request;
use Slim\Http\Response;

final class Update extends Base
{
    public function __invoke(Request $request, Response $response, array $args): Response
    {
        $id = $args['id'] ?? null;
        if ($id === null) {
            \App\Exception\Base::raiseNotFound('Sys text module');
        }

		$input = (array) $request->getParsedBody();

        $row = $this->sysTextModuleService->update($id, $input);

        return $this->jsonResponse($response, 'success', $row, 200);
    }
}

<?php

declare(strict_types=1);

namespace App\Controller\Offer\OfferTemplate;

use Slim\Http\Request;
use Slim\Http\Response;

final class Update extends Base
{
    public function __invoke(Request $request, Response $response, array $args): Response
    {
        $id = $args['id'] ?? null;
        if (!$id) {
            \App\Exception\Base::raiseInvalidRequest('ID is required.');
        }

		$input = (array) $request->getParsedBody();
        $row = $this->offerTemplateService->update($id, $input);

        return $this->jsonResponse($response, 'success', $row, 200);
    }
}

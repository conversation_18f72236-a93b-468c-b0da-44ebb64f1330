<?php

declare(strict_types=1);

namespace App\Service\Email\EmailServer;

use App\Lib\Func;
use App\Models\Email\EmailServer;
use App\Service\Email\EmailAccount\EmailAccountService;

final class EmailServerService extends Base
{
    public function create(array $input): EmailServer
    {
        return EmailServer::create($input);
    }

    public function update($id, $input): EmailServer
    {
        $row = EmailServer::findOrFail($id);
        if (!($input['smtp_password'] ?? '')) {
            unset($input['smtp_password']);
        } else {
            $input['smtp_password'] = Func::mcrypt('encrypt', $input['smtp_password'], EmailAccountService::SEC_KEY, EmailAccountService::SEC_IV);
        }
        $row->update($input);
        return $row;
    }

    public function getOne(int $id, array $params=[]): EmailServer
    {
        return $this->emailServerRepository->getQueryBuilder()->where('id', $id)->first();
    }

    public function getEmailServersByPage(
        int $page,
        int $perPage,
        ?array $params
    ): array
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($perPage < 1) {
            $perPage = self::DEFAULT_PER_PAGE_PAGINATION;
        }

        return $this->emailServerRepository->getEmailServersByPage(
            $page,
            $perPage,
            $params
        );
    }

    public function delete($ids): void
    {
        $arr = is_array($ids) ? $ids : explode(',', $ids);
        $this->emailServerRepository->getQueryBuilder()->whereIn('id', $arr)->delete();
    }
}

﻿/**
* @name umi's route configuration
* @description only supports path,component,routes,redirect,wrappers,name,icon configuration
* @param path path only supports two placeholder configurations, the first is the form of dynamic parameters:id, the second is the * wildcard, the wildcard can only appear at the end of the route string.
* @param component configures the React component path for rendering after location and path match. It can be an absolute path or a relative path. If it is a relative path, it will start from src/pages.
* @param routes configures sub-routes, usually used when you need to add layout components to multiple paths.
* @param redirect configures route jumps
* @param wrappers configures the wrapper component of the route component, through which more functions can be combined for the current route component. For example, it can be used for route-level permission verification
* @param name configures the route title. By default, the value of menu.xxxx in the internationalization file menu.ts is read. If the name is configured as login, the value of menu.login in menu.ts is read as the title
* @param icon configures the route icon. The value can be found in https://ant.design/components/icon-cn. Note that the style suffix and uppercase letters should be removed. If you want to configure the icon as <StepBackwardOutlined />, the value should be stepBackward or StepBackward. If you want to configure the icon as <UserOutlined />, the value should be user or User
* @doc https://umijs.org/docs/guides/routes
*/
export default [
  {
    path: '/user',
    layout: false,
    routes: [
      {
        name: 'login',
        path: '/user/login',
        component: './User/Login',
      },
    ],
  },
  {
    path: '/welcome',
    name: 'Welcome',
    icon: 'smile',
    component: './Welcome',
  },
  {
    path: '/suppliers',
    name: 'Suppliers',
    icon: 'table',
    access: 'user',
    routes: [
      {
        path: '/suppliers/list',
        name: 'Suppliers',
        icon: 'table',
        component: './Supplier/Supplier',
      },
      {
        path: '/suppliers/statistics',
        name: 'Supplier Statistics',
        icon: 'table',
        component: './Supplier/Supplier/SupplierStatistics',
        access: 'userSettingsRouteFilter',
      },

      {
        path: '/suppliers/email-matrix',
        name: 'Supplier X Email Matrix',
        icon: 'table',
        component: './Supplier/Supplier/SupplierEmailMatrix',
      },

      {
        name: 'Supplier Detail',
        path: '/suppliers/detail/:supplierId',
        icon: 'MailOutlined',
        hideInMenu: true,
        component: './Supplier/Supplier/SupplierDetail',
      },
    ],
  },
  {
    path: '/customers',
    name: 'Customers',
    icon: 'table',
    access: 'user',
    routes: [
      {
        path: '/customers/list',
        name: 'Customers',
        icon: 'table',
        component: './Customer/Customer',
      },
      /* {
        path: '/customers/email-matrix',
        name: 'Customer X Email Matrix',
        icon: 'table',
        component: './Customer/Customer/CustomerEmailMatrix',
      },
      {
        name: 'Customer Detail',
        path: '/customers/detail/:customerId',
        icon: 'MailOutlined',
        hideInMenu: true,
        component: './Customer/Customer/CustomerDetail',
      }, */
    ],
  },
  {
    path: '/conversations',
    name: 'Conversations',
    icon: 'table',
    access: 'user',
    routes: [
      {
        path: '/conversations/all',
        name: 'All Conversations',
        icon: 'table',
        component: './Supplier/SupplierCall',
        access: 'canAdmin',
      },
    ]
  },
  {
    path: '/offers',
    name: 'Offers',
    icon: 'table',
    access: 'user',
    routes: [
      {
        path: '/offers/list',
        name: 'Offers',
        icon: 'table',
        component: './Offer',
      },

      {
        path: '/offers/blogs',
        name: 'Offer Blogs',
        icon: 'table',
        component: './Offer/OfferBlog',
      },
      {
        path: '/offers/newsletters',
        name: 'Offer Newsletters',
        icon: 'table',
        component: './Offer/OfferNewsletter',
      },


      {
        name: 'Offer Detail',
        path: '/offers/detail/:offer_no',
        icon: 'MailOutlined',
        hideInMenu: true,
        component: './Offer/OrgOfferDetail',
      },
    ],
  },
  {
    path: '/email',
    name: 'Email',
    icon: 'MailOutlined',
    access: 'user',
    routes: [
      {
        path: '/email/list',
        name: 'Emails',
        icon: 'MailOutlined',
        component: './Email/EmailList',
      },
      {
        path: '/email/accounts-setting',
        name: 'IMAP Accounts',
        icon: 'SettingOutlined',
        access: 'canAdmin',
        component: './Email/EmailAccountList',
      },
      {
        path: '/email/server',
        name: 'Email Servers',
        icon: 'CloudServerOutlined',
        access: 'canAdmin',
        component: './Email/EmailServerList',
      },
      {
        path: '/email/template',
        name: 'Email Templates',
        icon: 'table',
        access: 'canAdmin',
        component: './Email/EmailTemplateList',
      },
      {
        path: '/email/template-category',
        name: 'Email Template Category',
        icon: 'table',
        access: 'canAdmin',
        hideInMenu: true,
        component: './Email/EmailTemplateCategoryList',
      },
      {
        name: 'Email Detail',
        path: '/email/detail/:emailId',
        icon: 'MailOutlined',
        hideInMenu: true,
        layout: true,
        component: './Email/EmailList/EmailDetail',
      },
    ],
  },
  {
    path: '/basic-data',
    name: 'Basic data',
    icon: 'form',
    access: 'canAdmin',
    routes: [
      {
        path: '/basic-data/users',
        name: 'Users',
        icon: 'user',
        component: './UsersList',
      },
      {
        path: '/basic-data/text-module',
        name: 'Text Module',
        icon: 'FileTextOutlined',
        component: './Sys/TextModule',
      },
      {
        path: '/basic-data/product-category',
        name: 'Product Categories',
        icon: 'table',
        component: './Sys/ProductCategory',
      },
      {
        path: '/basic-data/system-config',
        name: 'System Config',
        icon: 'SettingOutlined',
        component: './Sys/Dict',
      },
    ],
  },
  {
    path: '/monitor',
    name: 'Monitor',
    icon: 'MonitorOutlined',
    access: 'canAdmin',
    routes: [
      {
        path: '/monitor/sys-log',
        name: 'System Log',
        icon: 'table',
        component: './Audit/SysLog',
      },
    ],
  },

  {
    path: '/',
    redirect: '/welcome',
  },
  {
    path: '*',
    layout: false,
    component: './404',
  },
];

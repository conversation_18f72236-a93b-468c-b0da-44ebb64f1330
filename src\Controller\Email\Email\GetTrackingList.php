<?php

declare(strict_types=1);

namespace App\Controller\Email\Email;

use App\Lib\Func;
use App\Models\Email\Email;
use App\Models\Email\EmailReceiver;
use App\Models\Sys\SysLog;
use Slim\Http\Request;
use Slim\Http\Response;

final class GetTrackingList extends Base
{
    public function __invoke(Request $request, Response $response, array $args): Response
    {
        $page = intval($request->getQueryParam('page', 1));
        $perPage = intval($request->getQueryParam('perPage', 20));

        $id = $args['id'] ?? null;
        if (!$id) {
            \App\Exception\Base::raiseInvalidRequest('Email ID is required!');
        }

        /** @var Email $email */
        $email = Email::findOrFail($id);
        $params = $request->getParams();
        $emailAddr = $params['email'] ?? [];
        if (!$emailAddr) {
            \App\Exception\Base::raiseInvalidRequest('Email Address is required!');
        }

        $email->receivers()->where('email', $emailAddr)->get();
        /*$qb1 = Email::query()
            ->select("email.id")
            ->select("email.subject")
            ->select("email.email_template_id")
            ->join('email_receiver', )
            ->where('email.id', $id)
            ->with('receivers', function($builder) use (&$emailAddr) {
                $builder->where('email', $emailAddr);
            });*/

        $qbDummy = SysLog::query()->fromRaw('dual')
            ->selectRaw("CONCAT('sent_', $id) AS uid")
            ->selectRaw("$id AS id")
            ->selectRaw("'Sent' AS log")
            ->selectRaw("? AS date", [$email->date]);

        $qbSysLog = SysLog::query()
            ->select([])
            ->selectRaw("CONCAT('seen_', id) AS uid")
            ->selectRaw("id")
            ->selectRaw("name AS log")
            ->selectRaw("created_on AS date")
            ->whereIn('category', [SysLog::CATEGORY_EMAIL_TRACKING, SysLog::CATEGORY_EMAIL_TRACKING_CALL, SysLog::CATEGORY_EMAIL_TRACKING_OFFER_TPL])
            ->where('ref1', 'LIKE', "Email Id: #" . $id . '%')
            ->where('ref2', '=', $emailAddr);


        $qbDummy->unionAll($qbSysLog)->orderBy('date');


        $query = Func::getDb()->query()->fromSub($qbDummy, 'a');
        $query->orderBy('date');

        $total = $this->emailService->getEmailRepository()->getCountByQuery($query);
        $data = $this->emailService->getEmailRepository()->getResultsWithPagination($query, $page, $perPage, $total);

        return $this->jsonResponse($response, 'success', $data, 200);
    }
}

<?php

namespace App\Models\Casts;

use App\Lib\Func;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;

class CsvSorted extends Csv
{
    public function set($model, string $key, $value, array $attributes)
    {
        if ($value) {
            if (is_array($value)) {
                $values = array_values($value);
                if ($values) {
                    sort($values);
                }
                return implode(',', array_values($values));
            } else
                return NULL;
        } else {
            return NULL;
        }
    }
}
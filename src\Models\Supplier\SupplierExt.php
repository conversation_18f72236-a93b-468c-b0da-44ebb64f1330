<?php

namespace App\Models\Supplier;

use App\Models\BaseModel;
use App\Models\User;

/**
 * @property integer $id
 * @property integer $supplier_id
 * @property string $note
 * @property integer $user_id
 * @property integer $relevance
 * @property string $created_on
 * @property integer $created_by
 * @property string $updated_on
 * @property integer $updated_by
 *
 * @property Supplier $supplier
 * @property User $user
 */
class SupplierExt extends BaseModel
{
    public $timestamps = true;

    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'supplier_ext';

    /**
     * @var array
     */
    protected $fillable = ['supplier_id', 'note', 'user_id', 'relevance', 'created_on', 'created_by', 'updated_on', 'updated_by'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function supplier()
    {
        return $this->belongsTo('App\Models\Supplier\Supplier', 'supplier_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo('App\Models\User', 'user_id');
    }
}

<?php

namespace App\Service;

use App\Exception\BaseException;
use App\Lib\Func;
use App\Lib\LoggerMessageFormatter;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Middleware;
use GuzzleRetry\GuzzleRetryMiddleware;
use Monolog\Logger;
use Psr\Http\Message\ResponseInterface;
use Slim\Container;

class BaseApiService
{
    protected Client $apiClient;
    protected array $apiSetting;
    protected $apiToken = null;

    // latest API result
    public $errorDetail = null;
    public bool $isSuccess = false;

    protected Logger $logger;
    protected Container $container;

    public function __construct(Container $container)
    {
        $this->container = $container;
        $this->logger = $container->get('logger');
    }

    public function initialize($baseEndpoint = null): Client {
        return $this->getApiClient($baseEndpoint);
    }

    public function getApiClient($baseEndpoint = null): Client
    {
        $stack = HandlerStack::create();
        $stack->push(GuzzleRetryMiddleware::factory());

        $stack->push(
            Middleware::log(
                $this->logger,
                new LoggerMessageFormatter($this->container->get('settings')['app']['logLevel'])
            )
        );
        $settings = [
            'handler' => $stack,
            'max_retry_attempts' => 3,
            'default_retry_multiplier' => 1.5,
            'base_uri' => $baseEndpoint ?? $this->apiSetting['endpoint'],
            'headers' => []
        ];

        $options = $this->getClientHeaderOption();
        if (isset($options['headers']))
            $settings['headers'] = array_merge_recursive($settings['headers'] ?? [], $options['headers']);
        if (isset($options['auth']))
            $settings['auth'] = $options['auth'];

        $this->apiClient = new Client($settings);

        return $this->apiClient;
    }

    public function getClientInstanceXml($baseEndpoint = null): Client
    {
        $stack = HandlerStack::create();
        $stack->push(GuzzleRetryMiddleware::factory());

        $stack->push(
            Middleware::log(
                $this->logger,
                new LoggerMessageFormatter($this->container->get('settings')['app']['logLevel'])
            )
        );
        $settings = [
            'handler' => $stack,
            'max_retry_attempts' => 3,
            'default_retry_multiplier' => 1.5,
            'base_uri' => $baseEndpoint ?? $this->apiSetting['endpoint'],
            'headers' => []
        ];

        $options = $this->getClientHeaderOptionXml();
        if (isset($options['headers']))
            $settings['headers'] = array_merge_recursive($settings['headers'] ?? [], $options['headers']);

        if (isset($options['auth']))
            $settings['auth'] = $options['auth'];

        $this->apiClient = new Client($settings);

        return $this->apiClient;
    }

    public function getBasicAuth($user, $pass) {

    }

    public function getClientHeaderOption()
    {
        return [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->apiToken,
            ]
        ];
    }

    public function getClientHeaderOptionXml()
    {
        return [
            'headers' => [
                'Content-Type' => 'text/xml',
            ]
        ];
    }

    /**
     * @throws GuzzleException
     * @throws \Exception
     */
    public function post($url, $data, $isSilent = false)
    {
        $this->beforeSend();

        $jsonData = json_encode($data);
        try {
            $res = $this->apiClient->post($url, ['body' => $jsonData]);
            return $this->parseBody($res);
        } catch (\Exception $exception) {
            $this->logger->error($url);
            $this->logger->error($jsonData);
            if (!$isSilent) {
                throw BaseException::raiseInternalServerError($exception->getMessage());
            }
        }
    }

    /**
     * @throws GuzzleException
     * @throws \Exception
     */
    public function put($url, $data, $isSilent = false)
    {
        $this->beforeSend();

        try {
            $res = $this->apiClient->put($url, ['json' => $data]);
            return $this->parseBody($res);
        } catch (\Exception $exception) {
            if (!$isSilent) throw $exception;
        }
    }

    /**
     * @throws GuzzleException
     * @throws \Exception
     */
    public function get($url, $params = [], $isSilent = false)
    {
        $this->beforeSend();
        try {
            $res = $this->apiClient->get($url, ['query' => $params ?? []]);
            return $this->parseBody($res);
        } catch (\Exception $exception) {
            if (!$isSilent) throw $exception;
        }
    }

    /**
     * @throws GuzzleException
     * @throws \Exception
     */
    public function delete($url, $data = [], $isSilent = false)
    {
        $this->beforeSend();
        try {
            $res = $this->apiClient->delete($url, ['json' => $data]);
            return $this->parseBody($res);
        } catch (\Exception $exception) {
            if (!$isSilent) throw $exception;
        }
    }

    /**
     * Before callback to be executed before calling an API.
     *
     * @return void
     */
    public function beforeSend()
    {
        $this->isSuccess = false;
        $this->errorDetail = null;
    }

    /**
     * Parse the response.
     *
     * @param ResponseInterface $res
     * @return array|bool|mixed|null
     */
    public function parseBody(ResponseInterface $res): mixed
    {
        $this->isSuccess = $res->getStatusCode() >= 200 && $res->getStatusCode() <= 300;

        $content = $res->getBody()->__toString();
        if ($content === 'false') {
            return false;
        } else if ($content === 'true') {
            return true;
        } else if (($content[0] ?? '') === '"') {
            return Func::safeJson($content);
        } else {
            $bodyArr = Func::safeJson($content);
            if (isset($bodyArr['message']) || isset($bodyArr['trace'])) {
                $this->isSuccess = false;
                $this->errorDetail = $bodyArr;
                return null;
            }
            return $bodyArr;
        }
    }
}
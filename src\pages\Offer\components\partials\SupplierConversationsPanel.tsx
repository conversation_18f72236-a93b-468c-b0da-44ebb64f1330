import SupplierCallListByOffer from "./SupplierCallListByOffer";

type SupplierConversationsPanelProps = {
  supplier?: API.Supplier;
  offer_no?: string | number;
};

const SupplierConversationsPanel: React.FC<SupplierConversationsPanelProps> = ({ supplier, offer_no }) => {
  return (
    <div>
      {/* <h3 style={{ marginBottom: 0 }}>Supplier: {supplier?.name}</h3> */}
      <SupplierCallListByOffer offer_no={offer_no} supplier={supplier} />
    </div>
  );
};

export default SupplierConversationsPanel;

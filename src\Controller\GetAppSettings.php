<?php

declare(strict_types=1);

namespace App\Controller;

use App\Lib\FuncDate;
use App\Models\Sys\Dict;
use Slim\Http\Request;
use Slim\Http\Response;

final class GetAppSettings extends BaseController
{
    public function __invoke(Request $request, Response $response): Response
    {
        $result = [];

        $result['dict'] = Dict::query()->active()
            ->select('code', 'type', 'label', 'status', 'sort', 'value', 'parent_code')
            ->orderBy('type')
            ->orderBy('sort')
            ->get()->keyBy('code');

        $result['drSelection'] = FuncDate::getDateRangeSelectionData();

        $result['serverTz'] = date('Z')/3600;

        $result['FY_START_MONTH'] = FY_START_MONTH;

        return $this->jsonResponse($response, 'success', $result, 200);
    }
}

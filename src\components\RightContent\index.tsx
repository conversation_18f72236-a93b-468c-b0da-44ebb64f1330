import { Space } from "antd";
import React, { useEffect } from "react";
// @ts-ignore
import { useModel } from "umi";
// import Avatar from './AvatarDropdown';
// import HeaderSearch from '../HeaderSearch';
import styles from "./index.less";
import DownSyncDropdown from "./DownSyncDropdown";
import { getAppSettings } from "@/services/app/api";
import Util from "@/util";
import SessionTimeout from "../SessionTimout";
import { UserRole } from "@/constants";
export type SiderTheme = "light" | "dark";
import SAvatar from "../SAvatar";

const GlobalHeaderRight: React.FC = () => {
  const { initialState } = useModel("@@initialState");
  const { setAppSettings } = useModel("app-settings");

  useEffect(() => {
    if (initialState?.currentUser) {
      getAppSettings()
        .then((res) => setAppSettings((prev) => ({ ...prev, ...res })))
        .catch(() => Util.error("Failed to fetch app settings. Please try to reload a page!"));
    }
  }, [initialState?.currentUser, setAppSettings]);

  if (!initialState || !initialState.settings) {
    return null;
  }

  const { navTheme, layout } = initialState.settings;
  let className = styles.right;

  if ((navTheme === "realDark" && layout === "top") || layout === "mix") {
    className = `${styles.right}  ${styles.dark}`;
  }

  const isAuthenticated = !!initialState?.currentUser?.user_id;
  const isEditor = initialState?.currentUser?.role === UserRole.EDITOR;

  return !initialState || !initialState.settings || !isAuthenticated ? null : (
    <>
      <Space className={className}>
        {!isEditor && <DownSyncDropdown />}
        <SAvatar
          size="small"
          className={styles.avatar}
          src={initialState?.currentUser?.avatar}
          text={initialState?.currentUser?.initials ?? ""}
          alt="avatar"
        />
      </Space>
      <SessionTimeout />
    </>
  );
};

export default GlobalHeaderRight;

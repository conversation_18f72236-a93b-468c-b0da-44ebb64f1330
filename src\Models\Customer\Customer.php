<?php

namespace App\Models\Customer;

use App\Models\BaseModel;

/**
 * @property integer $id
 * @property string $name
 * @property string $org_a
 * @property integer $is_active
 * @property string $created_on
 * @property integer $created_by
 * @property string $updated_on
 * @property integer $updated_by
 *
 *
 * @property CustomerAddress $address
 * @property CustomerExt $ext
 * @property CustomerContact[] $contacts
 * @property CustomerContact[] $contactsAll
 */
class Customer extends BaseModel
{
    public $timestamps = true;

    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'customer';

    /**
     * @var array
     */
    protected $fillable = ['name', 'org_a', 'is_active', 'created_on', 'created_by', 'updated_on', 'updated_by'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function address()
    {
        return $this->hasOne('App\Models\Customer\CustomerAddress', 'customer_id', 'id');
    }

    /**
     * Customer Extension table.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function ext()
    {
        return $this->belongsTo('App\Models\Customer\CustomerExt', 'id', 'customer_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function contacts()
    {
        return $this->hasMany('App\Models\Customer\CustomerContact')->active()->sortDefault();
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function contactsAll()
    {
        return $this->hasMany('App\Models\Customer\CustomerContact');
    }
}

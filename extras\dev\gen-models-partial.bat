F:

cd F:\htdocs\laravel-app

rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel Supplier --table-name=supplier --output-path=ModelsWhcSupplier/Supplier --namespace=App\Models\Supplier
rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel SupplierInfo --table-name=supplier_info --output-path=ModelsWhcSupplier/Supplier --namespace=App\Models\Supplier
rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel SupplierContact --table-name=supplier_contact --output-path=ModelsWhcSupplier/Supplier --namespace=App\Models\Supplier
rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel SupplierComment --table-name=supplier_comment --output-path=ModelsWhcSupplier/Supplier --namespace=App\Models\Supplier
rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel SupplierAddress --table-name=supplier_address --output-path=ModelsWhcSupplier/Supplier --namespace=App\Models\Supplier
rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel SupplierExt --table-name=supplier_ext --output-path=ModelsWhcSupplier/Supplier --namespace=App\Models\Supplier
rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel SupplierCall --table-name=supplier_call --output-path=ModelsWhcSupplier/Supplier --namespace=App\Models\Supplier
rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel SupplierMeta --table-name=supplier_meta --output-path=ModelsWhcSupplier/Supplier --namespace=App\Models\Supplier

rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel EmailTemplate --table-name=email_template --output-path=ModelsWhcSupplier/Email --namespace=App\Models\Email
rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel EmailReceiver --table-name=email_receiver --output-path=ModelsWhcSupplier/Email --namespace=App\Models\Email
rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel EmailTemplateCategory --table-name=email_template_category --output-path=ModelsWhcSupplier/Email --namespace=App\Models\Email

rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel ProductCategory --table-name=sys_product_category --output-path=ModelsWhcSupplier/Sys --namespace=App\Models\Sys
rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel Trademark --table-name=sys_trademark --output-path=ModelsWhcSupplier/Sys --namespace=App\Models\Sys
rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel Product --table-name=sys_product --output-path=ModelsWhcSupplier/Sys --namespace=App\Models\Sys

rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel SupplierCallTrademark --table-name=supplier_call_trademark --output-path=ModelsWhcSupplier/Supplier --namespace=App\Models\Supplier
rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel SupplierCallProduct --table-name=supplier_call_product --output-path=ModelsWhcSupplier/Supplier --namespace=App\Models\Supplier
rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel SupplierCallFile --table-name=supplier_call_file --output-path=ModelsWhcSupplier/Supplier --namespace=App\Models\Supplier


rem =========================================== WHC ORG Modeling =========================================
rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel OrgSupplier --table-name=suppliers --output-path=ModelsWhcOrg --namespace=App\ModelsOrg
rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel OrgSupplierView --table-name=v_suppliers --output-path=ModelsWhcOrg --namespace=App\ModelsOrg
rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel OrgSupplierInfo --table-name=supplier_info --output-path=ModelsWhcOrg --namespace=App\ModelsOrg
rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel OrgSupplierComment --table-name=supplier_comments --output-path=ModelsWhcOrg --namespace=App\ModelsOrg
rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel OrgOffer --table-name=offers --output-path=ModelsWhcOrg --namespace=App\ModelsOrg
rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel OrgOfferComment --table-name=offer_comments --output-path=ModelsWhcOrg --namespace=App\ModelsOrg
rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel OrgSysConfig --table-name=sys_config --output-path=ModelsWhcOrg --namespace=App\ModelsOrg
rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel OrgUser --table-name=users --output-path=ModelsWhcOrg --namespace=App\ModelsOrg
rem =========================================== END OF WHC ORG Modeling =========================================

rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel Customer --table-name=customer --output-path=ModelsWhcSupplier/Customer --namespace=App\Models\Customer
rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel CustomerContact --table-name=customer_contact --output-path=ModelsWhcSupplier/Customer --namespace=App\Models\Customer
rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel CustomerAddress --table-name=customer_address --output-path=ModelsWhcSupplier/Customer --namespace=App\Models\Customer
rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel CustomerExt --table-name=customer_ext --output-path=ModelsWhcSupplier/Customer --namespace=App\Models\Customer


rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel OfferTemplate --table-name=offer_template --output-path=ModelsWhcSupplier/Offer --namespace=App\Models\Offer
rem php artisan krlove:generate:model --base-class-name=App\Models\BaseModel OfferTemplateLang --table-name=offer_template_lang --output-path=ModelsWhcSupplier/Offer --namespace=App\Models\Offer
rem REMOVED php artisan krlove:generate:model --base-class-name=App\Models\BaseModel OfferCustomer --table-name=offer_customer --output-path=ModelsWhcSupplier/Offer --namespace=App\Models\Offer


php artisan krlove:generate:model --base-class-name=App\Models\BaseModel OfferExt --table-name=offer_ext --output-path=ModelsWhcSupplier/Offer --namespace=App\Models\Offer

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel OfferBlog --table-name=offer_blog --output-path=ModelsWhcSupplier/Offer --namespace=App\Models\Offer
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel OfferBlogFile --table-name=offer_blog_file --output-path=ModelsWhcSupplier/Offer --namespace=App\Models\Offer

php artisan krlove:generate:model --base-class-name=App\Models\BaseModel OfferNewsletter --table-name=offer_newsletter --output-path=ModelsWhcSupplier/Offer --namespace=App\Models\Offer
php artisan krlove:generate:model --base-class-name=App\Models\BaseModel OfferNewsletterFile --table-name=offer_newsletter_file --output-path=ModelsWhcSupplier/Offer --namespace=App\Models\Offer


rem "Done"

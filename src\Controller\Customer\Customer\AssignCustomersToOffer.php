<?php

declare(strict_types=1);

namespace App\Controller\Customer\Customer;

use App\Exception\BaseException;
use Slim\Http\Request;
use Slim\Http\Response;

/**
 * @deprecated No need anymore
 */
final class AssignCustomersToOffer extends Base
{
    public function __invoke(Request $request, Response $response): Response
    {
        BaseException::raiseInvalidRequest('No usage!');

        $input = (array) $request->getParsedBody();

        $offerNo = $input['offerNo'] ?? null;
        if (!$offerNo) {
            \App\Exception\Base::raiseInvalidRequest('Offer No is required!');
        }

        $customerIds = $input['customerIds'] ?? null;
        if (!$customerIds) {
            \App\Exception\Base::raiseInvalidRequest('Customer Ids are required!');
        }

        $mode = $input['mode'] ?? 'add';

        if ($mode == 'add') {
            /*$dbRows = [];
            foreach ($customerIds as $customerId) {
                $dbRows[] = [
                    'offer_no' => $offerNo,
                    'customer_id' => $customerId
                ];
            }

            OfferCustomer::upsert($dbRows, ['offer_no', 'customer_id']);*/

            // NOTE: We only allow single customer to select.
            /*$firstId = $customerIds[0];
            OfferCustomer::query()->where('offer_no', $offerNo)->where('customer_id', '!=', $firstId)->delete();
            OfferCustomer::upsert([[
                'offer_no' => $offerNo,
                'customer_id' => $firstId
            ]], ['offer_no', 'customer_id']);*/
        } else if ($mode == 'remove'){
            // OfferCustomer::query()->where('offer_no', $offerNo)->whereIn('customer_id', $customerIds)->delete();
        }

        return $this->jsonResponse($response, 'success', true, 200);
    }
}

<?php

declare(strict_types=1);

namespace App\Service\Supplier\SupplierCall;

use App\Lib\Func;
use App\Lib\FuncModel;
use App\Lib\SysMsg;
use App\Models\Customer\Customer;
use App\Models\Customer\CustomerCall;
use App\Models\Email\Email;
use App\Models\File;
use App\Models\Offer\OfferExt;
use App\Models\Supplier\SupplierCall;
use App\Models\Supplier\SupplierContact;
use App\Models\User;
use App\ModelsOrg\OrgOffer;
use App\ModelsOrg\OrgOfferComment;
use App\ModelsOrg\OrgSupplier;
use App\Service\BaseService;
use App\Service\Email\Email\EmailService;
use App\Service\File\FileService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Psr\Http\Message\UploadedFileInterface;
use Respect\Validation\Exceptions\FileException;
use Slim\Container;


final class SupplierCallService extends Base
{
    private EmailService $emailService;

    public function __construct(Container $container)
    {
        parent::__construct($container);

        $this->emailService = $container->get(EmailService::class);
    }

    private function processInputData(&$input)
    {
        if (key_exists('direction', $input)) {
            if (!($input['direction'])) {
                $input['direction'] = null;
            }
        }

        if (($input['type'] ?? null) == SupplierCall::TYPE_OFFER) {
            $input['direction'] = null;
        }

        if (key_exists('note', $input)) {
            $input['note'] = $input['note'] ? BaseService::extractInlineImage($input['note'], 'supplier_call') : '';
        }
    }

    private function sendEmailToSk(SupplierCall &$row, &$input)
    {
        if ($copyToSk = ($input['copy_to_sk'] ?? '')) {
            $supplier = $row->supplier;
            // Email SK.
            $skEmail = User::query()->where('initials', 'SK')->value('email');
            if ($skEmail) {
                $sendData = [
                    'sender' => Func::buildEmailStr($input['user']['email'], $input['user']['name']),
                    'receiver' => Func::buildEmailStr($skEmail, 'Sincap'),
                    'subject' => 'GFC_Supplier: New Conversation',
                    'text_html' => '<div>
<h5>Supplier: ' . ($supplier?->fullname) . '</h5>
<h5>Type: ' . (SupplierCall::TYPE_LABELS[$row->type] ?? $row->type) . '</h5>
<h5>Offer: ' . ($row->offer_no) . '</h5>
<h5>Direction: ' . ($row->direction) . '</h5>
<div>Comments: ' . nl2br($row->comment ?: '') . '</div>
<div>Notes: ' . nl2br($row->note ?: '') . '</div>
</div>',
                ];
                try {
                    $this->emailService->send($sendData, $copyToSk == 'yes2' ? $row->files : [], ['skip_success_msg' => true]);
                } catch (\Exception $exception) {
                    SysMsg::get_instance()->info($exception->getMessage());
                }
            }
        }
    }

    /**
     * No relation so far.
     *
     * @param SupplierCall $row
     * @param $input
     * @return void
     */
    private function saveRelations(SupplierCall &$row, &$input)
    {
        /*if (key_exists('trademarks', $input)) {
            $ids = [];
            if ($input['trademarks']) {
                foreach ($input['trademarks'] as $x) {
                    $ids[] = $x;
                }
            }
            $row->trademarks()->sync($ids);
        }

        if (key_exists('products', $input)) {
            $ids = [];
            if ($input['products']) {
                foreach ($input['products'] as $x) {
                    $ids[] = $x;
                }
            }

            $row->products()->sync($ids);
        }*/
    }

    public function create(array $input): SupplierCall
    {
        // SupplierCall::bindOfferId($input);

        $this->processInputData($input);

        $db = $this->getSupplierCallRepository()->getDb();
        try {
            $db->beginTransaction();

            /** @var SupplierCall $row */
            $row = SupplierCall::create($input);
            $this->saveRelations($row, $input);
            $this->uploadAndSaveFiles($row, $input);

            $db->commit();
        } catch (\Exception $exception) {
            $db->rollBack();
            throw $exception;
        }
        $this->sendEmailToSk($row, $input);
        return $row;
    }

    public static function getFileBasePath(): string
    {
        return PRIVATE_DATA_PATH;
    }

    public static function getFileRelativePath(): string
    {
        return DS . File::CAT_CALL . DS . date('Y') . DS . date('m');
    }

    public function uploadAndSaveFiles(SupplierCall &$row, &$input)
    {
        if (isset($input['uploaded_files'])) {
            $pathRel = self::getFileRelativePath();
            $pathAbs = self::getFileBasePath() . $pathRel;
            if (!file_exists($pathAbs)) {
                mkdir($pathAbs, 0755, true);
            }

            /** @var UploadedFileInterface[] $files */
            $files = $input['uploaded_files'];

            $fileCategory = File::CAT_CALL;

            try {
                foreach ($files as &$file) {
                    // Uploading file
                    $fileType = $file->getClientMediaType();
                    $fileRow = [
                        'category' => $fileCategory,
                        'type' => $fileType,
                        'file_name' => Func::getSafeFilePath($file->getClientFilename()),
                        'clean_file_name' => $file->getClientFilename(),
                        'size' => $file->getSize()
                    ];
                    $fileRow['org_path'] = $fileRow['path'] = Func::pathToUrl($pathRel) . '/' . $fileRow['file_name'];
                    $file->moveTo($pathAbs . DS . $fileRow['file_name']);

                    // Save file data
                    /** @var File $fileObj */
                    $fileObj = File::create($fileRow);
                    $row->files()->save($fileObj);
                }
            } catch (\Exception $e) {
                throw new FileException($e->getMessage(), 500);
            }

        }
    }

    public function update($id, $input): SupplierCall
    {
        $row = SupplierCall::findOrFail($id);
        // SupplierCall::bindOfferId($input);

        $this->processInputData($input);

        $db = $this->getSupplierCallRepository()->getDb();
        try {
            $db->beginTransaction();

            $row->update($input);
            $this->saveRelations($row, $input);
            $this->uploadAndSaveFiles($row, $input);

            $db->commit();
        } catch (\Exception $exception) {
            $db->rollBack();
            throw $exception;
        }

        $this->sendEmailToSk($row, $input);

        return $row;
    }

    /**
     * @param int $id
     * @param array $params
     * @return Builder|Model|object|SupplierCall
     */
    public function getOne(int $id, array $params = [])
    {
        return $this->supplierCallRepository->getQueryBuilder()->where('id', $id)->first();
    }

    public function getSupplierCallsByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($perPage < 1) {
            $perPage = self::DEFAULT_PER_PAGE_PAGINATION;
        }

        // to do TEST
        // ---------------------------------------------
        /*$testData = OrgOffer::query()->whereIn('id', ['AOFFR0000000026', 'AOFFR0000000027'])
            ->with(['comments' => function (HasMany $builder) {
                $builder->latest()->limit(5);
            }])->get()->toArray();
        $tmp = 1;*/
        // ---------------------------------------------

        return $this->supplierCallRepository->getSupplierCallsByPage(
            $page,
            $perPage,
            $params
        );
    }

    /**
     *
     * ServiceCall + Offers of WHC_Org
     *
     * @param int $page
     * @param int $perPage
     * @param array|null $params
     * @return array
     */
    public function getMergedSupplierOffersByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($perPage < 1) {
            $perPage = self::DEFAULT_PER_PAGE_PAGINATION;
        }

        if ($params['fromOrg'] ?? null) {
            $builder = OrgOffer::query()->fromRaw("offers")
                ->select([])
                ->selectRaw("CONCAT('org_', id) AS uid")
                ->selectRaw("'org_offer' AS uid_type")
                ->selectRaw("id AS id")     // This will be zero so next line important.
                ->selectRaw("id AS org_offer_id")
                ->selectRaw("supplier_id")
                ->selectRaw("id AS offer_id")
                ->selectRaw("offer_sid AS offer_no")
                ->selectRaw("offer_sid")
                ->selectRaw("offer AS note")
                ->selectRaw("NULL AS comment")
                ->selectRaw("status")
                ->selectRaw("we")
                ->selectRaw("created_on");

            // Where
            // --------------------------------------------------
            if ($params['offer_no'] ?? null) {
                $builder->where("offer_sid", $params['offer_no']);
            }
            if ($params['org_a'] ?? null) {
                $builder->whereHas('supplier', function (Builder $belongsTo) use (&$params) {
                    $belongsTo->where("org_a", $params['org_a']);
                });
            }

            // Done
            if ($params['in_org_a'] ?? null) {
                if (!in_array('all', $params['in_org_a'])) {
                    $builder->whereHas('supplier', function (Builder $belongsTo) use (&$params) {
                        $belongsTo->where(function (\Illuminate\Database\Eloquent\Builder $builder) use (&$params) {
                            $builder->whereIn('suppliers.org_a', $params['in_org_a']);
                            if (in_array('-', $params['in_org_a'])) {
                                $builder->orWhereRaw("IFNULL(suppliers.org_a, '')=''");
                            }
                        });
                    });
                }
            }

            if (($in_prio = ($params['in_prio'] ?? null)) && !in_array('all', $in_prio)) {
                if (in_array('-', $in_prio)) {
                    $builder->where(function (Builder $builder) use (&$in_prio) {
                        $builder->whereExists(function (\Illuminate\Database\Query\Builder $builder) use (&$in_prio) {
                            $builder
                                ->select('id')
                                ->fromSub(OfferExt::query()
                                    ->whereIn('prio', $in_prio)
                                    ->orWhereRaw("IFNULL(prio, '')=''")
                                    , 't')
                                ->whereColumn('offers.offer_sid', 't.offer_no');
                        })->orWhereNotExists(function (\Illuminate\Database\Query\Builder $builder) {
                            $builder
                                ->select('id')
                                ->fromSub(OfferExt::query(), 't')
                                ->whereColumn('offers.offer_sid', 't.offer_no');
                        });
                    });
                } else {
                    $builder->whereExists(function (\Illuminate\Database\Query\Builder $builder) use (&$in_prio) {
                        $builder
                            ->select('id')
                            ->fromSub(OfferExt::query()->whereIn('prio', $in_prio), 't')
                            ->whereColumn('offers.offer_sid', 't.offer_no');
                    });
                }
            }



            if (Func::isZeroOrOne($params['is_brand'] ?? null)) {
                $builder->whereExists(function (\Illuminate\Database\Query\Builder $builder) use (&$params) {
                    $builder
                        ->select('id')
                        ->fromSub(OfferExt::query()->where('is_brand', intval($params['is_brand'])), 't')
                        ->whereColumn('offers.offer_sid', 't.offer_no');
                });
            }
            if (Func::isZeroOrOne($params['is_b_group_appr'] ?? null)) {
                $builder->whereExists(function (\Illuminate\Database\Query\Builder $builder) use (&$params) {
                    $builder
                        ->select('id')
                        ->fromSub(OfferExt::query()->where('is_b_group_appr', intval($params['is_b_group_appr'])), 't')
                        ->whereColumn('offers.offer_sid', 't.offer_no');
                });
            }


            if ($keyWords = $params['keyWords'] ?? null) {
                $builder->where("offer", 'LIKE', FuncModel::likeValue($keyWords, '%'));
            }
            if ($comment = $params['comment'] ?? null) {
                $builder->whereRaw("exists (SELECT 1 FROM offer_comments WHERE offers.id=offer_comments.offer_id AND comment LIKE ?)"
                    , [FuncModel::likeValue($comment, '%')]);
            }
            if (key_exists('in_status', $params)) {
                if ($params['in_status']) {
                    $builder->whereIn("status", $params['in_status']);
                } else {
                    $builder->whereRaw("0");
                }
            }


            $builder->with('supplier');
            $builder->with('ext');
            $builder->with(['top3Comments' => function (HasMany $builder) {
                $builder->latest()->limit(3);
                $builder->with('sysConfig');
            }]);

            $this->supplierCallRepository->applyOrderBy($builder, $params);
            $builder->orderByDesc('offer_no');
            $builder->orderByDesc('created_on');
        } else {
            $builder = $this->getSupplierCallRepository()->getQuerySupplierCallsByPage($params);
            $builder
                ->select([])
                ->selectRaw("CONCAT('call_', id) AS uid")
                ->selectRaw("'call' AS uid_type")
                ->selectRaw("id AS id")
                ->selectRaw("NULL AS org_offer_id")
                ->selectRaw("supplier_id")
                ->selectRaw("offer_id")
                ->selectRaw("offer_no")
                ->selectRaw("note")
                ->selectRaw("comment")
                ->selectRaw("NULL AS status")
                ->selectRaw("NULL AS we")
                ->selectRaw("created_on");

            if ($params['in_org_a'] ?? null) {
                if (!in_array('all', $params['in_org_a'])) {
                    $suppIds = OrgSupplier::query()
                        ->where(function (\Illuminate\Database\Eloquent\Builder $builder) use (&$params) {
                            $builder->whereIn('org_a', $params['in_org_a']);
                            if (in_array('-', $params['in_org_a'])) {
                                $builder->orWhereRaw("IFNULL(org_a, '')=''");
                            }
                        })
                        ->whereNotNull('supp_supplier_id')->distinct()
                        ->select('supp_supplier_id')
                        ->pluck('supp_supplier_id')
                        ->toArray();
                    if ($suppIds) {
                        $builder->whereIn('id', $suppIds);
                    } else {
                        $builder->whereRaw('0');
                    }
                }
            }

            // Load relations
            // -----------------------------------------------------------
            $builder->with('supplier', function (BelongsTo $builder) {
                $builder->with('contacts');
            });
            $builder->with('files');

            $this->supplierCallRepository->applyOrderBy($builder, $params);
            $builder->orderByDesc('created_on');
        }


        $total = $this->supplierCallRepository->getCountByQuery($builder);

        $result = $this->supplierCallRepository->getResultsWithPagination(
            $builder,
            $page,
            $perPage,
            $total
        );

        if ($total) {
            if ($params['fromOrg'] ?? null) {
                /*$offerNoList = array_map(function ($x) {
                    return $x['offer_no'];
                }, $result['data']);
                $kv = OfferExt::query()->whereIn('offer_no', $offerNoList)->get()->keyBy('offer_no');

                foreach ($result['data'] as &$x) {
                    $x['ext'] = $kv[$x['offer_no']] ?? null;
                }*/
            } else {
                if (Func::keyExistsInWithParam($params, 'org_supplier')) {
                    $ids = array_map(function ($x) {
                        return $x['supplier_id'];
                    }, $result['data']);

                    $kv = OrgSupplier::query()->whereIn('supp_supplier_id', $ids)->get()->keyBy('supp_supplier_id')->toArray();
                    foreach ($result['data'] as &$x) {
                        $x['org_supplier'] = $kv[$x['supplier_id']] ?? null;
                    }
                }
            }
        }

        return $result;
    }

    /**
     *
     * Get Offer comments from WHC_Org
     *
     * @param int $page
     * @param int $perPage
     * @param array|null $params
     * @return array
     */
    public function getOrgOfferCommentsByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($perPage < 1) {
            $perPage = self::DEFAULT_PER_PAGE_PAGINATION;
        }

        $builder = OrgOfferComment::query();
        $builder->with('sysConfig');
        if ($params['offer_id'] ?? null) {
            $builder->where('offer_id', $params['offer_id']);
        }

        $total = $builder->count();

        $this->supplierCallRepository->applyOrderBy($builder, $params);

        $result = $this->supplierCallRepository->getResultsWithPagination(
            $builder,
            $page,
            $perPage,
            $total
        );

        return $result;
    }

    /**
     * Get Merged list with Emails, etc
     *
     * @param int $page
     * @param int $perPage
     * @param array|null $params
     * @return array
     */
    public function getSupplierCallsByPageMerged(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($perPage < 1) {
            $perPage = self::DEFAULT_PER_PAGE_PAGINATION;
        }

        $supplier_id = $params['supplier_id'] ?? null;
        if (!$supplier_id) {
            // BaseException::raiseInvalidRequest('Shop Supplier ID is required!');
            return [
                'pagination' => [
                    'totalRows' => 0,
                    'totalPages' => 0,
                    'currentPage' => 1,
                    'hasMore' => false,
                ],
                'data' => [],
            ];
        }
        // 1. supplier call log
        $qbCall = SupplierCall::query();
        $qbCall->select([])
            ->selectRaw("type")
            ->selectRaw("CONCAT('call_', id) AS uid")
            ->selectRaw("id AS id")
            ->selectRaw("supplier_id")
            ->selectRaw("direction")
            ->selectRaw("offer_id")
            ->selectRaw("offer_no")
            ->selectRaw("note")
            ->selectRaw("comment")
            ->selectRaw("created_on AS date")
            ->selectRaw("NULL AS email_subject")
            ->selectRaw("NULL AS email_open_count")
            ->selectRaw("NULL AS email_open_first_seen_on")
            ->selectRaw("NULL AS email_open_seen_updated_on");

        $qbCall->where('supplier_id', $supplier_id);
        if ($params['offer_no'] ?? null) {
            $qbCall->where('offer_no', $params['offer_no']);
        }
        if ($params['ft_note'] ?? null) {
            $qbCall->where('note', 'like', FuncModel::likeValue($params['ft_note'], '%'));
        }


        if ($offerMode = $params['offer_mode'] ?? 'inc') {
            switch ($offerMode) {
                case 'inc':
                    break;
                case 'exc':
                    $qbCall->whereNotIn('type', [SupplierCall::TYPE_OFFER]);
                    break;
                case 'only':
                    $qbCall->whereIn('type', [SupplierCall::TYPE_OFFER]);
                    break;
            }
        }

        $qbCall->with('files');
        $qbCall->with('orgOffer');

        $this->supplierCallRepository->applyOrderBy($qbCall, $params);

        $total = $this->supplierCallRepository->getCountByQuery($qbCall);

        $result = $this->supplierCallRepository->getResultsWithPagination(
            $qbCall,
            $page,
            $perPage,
            $total
        );

        return $result;
    }


    /**
     * Get Offers / Supplier Conversations: Combine with
     *
     *
     * @param int $page
     * @param int $perPage
     * @param array|null $params
     * @return array
     */
    public function getSupplierAndCustomerCallsByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($perPage < 1) {
            $perPage = self::DEFAULT_PER_PAGE_PAGINATION;
        }

        // 1. supplier call log
        $qbCall = SupplierCall::query();
        $qbCall->select([])
            ->selectRaw("type")
            ->selectRaw("CONCAT('call_supp_', id) AS uid")
            ->selectRaw("id AS id")
            ->selectRaw("supplier_id")
            ->selectRaw("NULL AS customer_id")
            ->selectRaw("direction")
            ->selectRaw("offer_no")
            ->selectRaw("note")
            ->selectRaw("comment")
            ->selectRaw("created_by")
            ->selectRaw("created_on AS date")
            ->selectRaw("NULL AS email_subject")
            ->selectRaw("NULL AS email_open_count")
            ->selectRaw("NULL AS email_open_first_seen_on")
            ->selectRaw("NULL AS email_open_seen_updated_on");

        // 2. customer call log
        $qbCustCall = CustomerCall::query();
        $qbCustCall->select([])
            ->selectRaw("type")
            ->selectRaw("CONCAT('call_cust_', id) AS uid")
            ->selectRaw("id AS id")
            ->selectRaw("NULL AS supplier_id")
            ->selectRaw("customer_id")
            ->selectRaw("direction")
            ->selectRaw("offer_no")
            ->selectRaw("note")
            ->selectRaw("NULL AS comment")
            ->selectRaw("created_by")
            ->selectRaw("created_on AS date")
            ->selectRaw("NULL AS email_subject")
            ->selectRaw("NULL AS email_open_count")
            ->selectRaw("NULL AS email_open_first_seen_on")
            ->selectRaw("NULL AS email_open_seen_updated_on");

        if ($params['supp_supplier_id'] ?? null) {
            $qbCall->where('supplier_id', $params['supp_supplier_id']);
        }
        if ($params['customer_id'] ?? null) {
            $qbCustCall->where('customer_id', $params['customer_id']);
        }

        if ($params['offer_no'] ?? null) {
            $qbCall->where('offer_no', $params['offer_no']);
            $qbCustCall->where('offer_no', $params['offer_no']);
        }
        if ($params['ft_note'] ?? null) {
            $qbCall->where('note', 'like', FuncModel::likeValue($params['ft_note'], '%'));
            $qbCustCall->where('note', 'like', FuncModel::likeValue($params['ft_note'], '%'));
        }

        if (($in_org_a = ($params['in_org_a'] ?? null)) && !in_array('all', $in_org_a)) {
            // If includes empty orgA?
            if (in_array('-', $in_org_a)) {
                $qbCall->where(function (Builder $builder) use (&$in_org_a) {
                    $builder->whereExists(function (\Illuminate\Database\Query\Builder $builder) use (&$in_org_a) {
                        $builder
                            ->select('id')
                            ->fromSub(OrgSupplier::query()
                                ->whereIn('org_a', $in_org_a)
                                ->orWhereRaw("IFNULL(org_a, '')=''")
                                , 't')
                            ->whereColumn('supplier_call.supplier_id', 't.supp_supplier_id');
                    })->orWhereNotExists(function (\Illuminate\Database\Query\Builder $builder) {
                        $builder
                            ->select('id')
                            ->fromSub(OrgSupplier::query(), 't')
                            ->whereColumn('supplier_call.supplier_id', 't.supp_supplier_id');
                    });
                });
            } else {
                $qbCall->whereExists(function (\Illuminate\Database\Query\Builder $builder) use (&$in_org_a) {
                    $builder
                        ->select('id')
                        ->fromSub(OrgSupplier::query()->whereIn('org_a', $in_org_a), 't')
                        ->whereColumn('supplier_call.supplier_id', 't.supp_supplier_id');
                });
            }
        }

        if ($params['in_created_by'] ?? null) {
            if (!in_array('all', $params['in_created_by'])) {
                $qbCall->where(function (\Illuminate\Database\Eloquent\Builder $builder) use (&$params) {
                    $builder->whereIn('created_by', $params['in_created_by']);
                    if (in_array('-', $params['in_created_by'])) {
                        $builder->orWhereRaw("IFNULL(created_by, '')=''");
                    }
                });

                $qbCustCall->where(function (\Illuminate\Database\Eloquent\Builder $builder) use (&$params) {
                    $builder->whereIn('created_by', $params['in_created_by']);
                    if (in_array('-', $params['in_created_by'])) {
                        $builder->orWhereRaw("IFNULL(created_by, '')=''");
                    }
                });
            }
        }

        if ($offerMode = $params['offer_mode'] ?? 'inc') {
            switch ($offerMode) {
                case 'inc':
                    break;
                case 'exc':
                    $qbCall->whereNotIn('type', [SupplierCall::TYPE_OFFER]);
                    $qbCustCall->whereNotIn('type', [CustomerCall::TYPE_OFFER]);
                    break;
                case 'only':
                    $qbCall->whereIn('type', [SupplierCall::TYPE_OFFER]);
                    $qbCustCall->whereIn('type', [CustomerCall::TYPE_OFFER]);
                    break;
            }
        }

        // Union
        // Important: We assume that final result could be SupplierCall models.
        $qbMain = SupplierCall::query()->fromSub($qbCall->unionAll($qbCustCall), 'a');

        if (Func::keyExistsInWithParam($params, 'user')) {
            $qbMain->with('user:user_id,initials');
        }
        if (Func::keyExistsInWithParam($params, 'offerExt')) {
            $qbMain->with('offerExt');
        }

        // load relations
        $qbMain->with('supplier');
        $qbMain->with('files');
        // Load dummy relations
        $qbMain->with('orgSupplier');
        $qbMain->with('orgOffer');
        $qbMain->with('customer');

        $this->supplierCallRepository->applyOrderBy($qbMain, $params);

        $total = $this->supplierCallRepository->getCountByQuery($qbMain);

        $result = $this->supplierCallRepository->getResultsWithPagination(
            $qbMain,
            $page,
            $perPage,
            $total
        );

        return $result;
    }

    /**
     * Get Merged list with Emails, etc
     *
     * @param int $page
     * @param int $perPage
     * @param array|null $params
     * @return array
     */
    public function getSupplierCallsByPageMerged_Old(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        // self::addOfferToCall();

        if ($page < 1) {
            $page = 1;
        }
        if ($perPage < 1) {
            $perPage = self::DEFAULT_PER_PAGE_PAGINATION;
        }

        $supplier_id = $params['supplier_id'] ?? null;
        if (!$supplier_id) {
            // BaseException::raiseInvalidRequest('Shop Supplier ID is required!');
            return [
                'pagination' => [
                    'totalRows' => 0,
                    'totalPages' => 0,
                    'currentPage' => 1,
                    'hasMore' => false,
                ],
                'data' => [],
            ];
        }
        $emails = SupplierContact::query()->where('id', $supplier_id)->pluck('email')?->toArray();

        // 1. supplier call log
        $qbCall = SupplierCall::query();
        $qbCall->select([])
            ->selectRaw("type")
            ->selectRaw("CONCAT('call_', id) AS uid")
            ->selectRaw("id AS id")
            ->selectRaw("supplier_id")
            ->selectRaw("direction")
            ->selectRaw("offer_id")
            ->selectRaw("offer_no")
            ->selectRaw("note")
            ->selectRaw("comment")
            ->selectRaw("created_on AS date")
            ->selectRaw("NULL AS email_subject")
            ->selectRaw("NULL AS email_open_count")
            ->selectRaw("NULL AS email_open_first_seen_on")
            ->selectRaw("NULL AS email_open_seen_updated_on");

        // 2. Email logs
        $qbEmail = Email::query();
        $qbEmail->select([])
            ->selectRaw(SupplierCall::TYPE_EMAIL . " AS type")
            ->selectRaw("CONCAT('email_', id, '_', email_receiver.email) AS uid")
            ->selectRaw("id AS id")
            ->selectRaw("? AS supplier_id", [$supplier_id])
            ->selectRaw("IF(email.box='SENT', '" . SupplierCall::DIRECTION_OUT . "', '" . SupplierCall::DIRECTION_IN . "') AS direction")
            ->selectRaw("NULL AS offer_id")
            ->selectRaw("offer_no AS offer_no")
            ->selectRaw("NULL AS note")
            ->selectRaw("NULL AS comment")
            ->selectRaw("email.date AS date")
            ->selectRaw("email.subject AS email_subject")
            ->selectRaw("email_receiver.open_count AS email_open_count")
            ->selectRaw("email_receiver.open_first_seen_on AS email_open_first_seen_on")
            ->selectRaw("email_receiver.open_seen_updated_on AS email_open_seen_updated_on");
        $qbEmail->join('email_receiver', 'email_receiver.email_id', '=', 'email.id');


        $qbCall->where('supplier_id', $supplier_id);
        if ($params['offer_no'] ?? null) {
            $qbCall->where('offer_no', $params['offer_no']);
            $qbEmail->where('offer_no', $params['offer_no']);
        }


        $qbEmail->where(function ($qb) use (&$emails, &$supplier_id, &$params) {
            if ($emails) {
                $qb->whereIn('email.sender', $emails)
                    ->orWhereIn('email_receiver.email', $emails);
            } else {
                $qb->whereRaw("0");
            }
            if ($supplier_id && ($params['inc_internal_email'] ?? false)) {
                $qb->orWhere('supplier_id', $supplier_id);
            }
        });

        if ($offerMode = $params['offer_mode'] ?? 'inc') {
            switch ($offerMode) {
                case 'inc':
                    break;
                case 'exc':
                    $qbCall->whereNotIn('type', [SupplierCall::TYPE_OFFER]);
                    break;
                case 'only':
                    $qbCall->whereIn('type', [SupplierCall::TYPE_OFFER]);
                    $qbEmail->whereRaw("0");
                    break;
            }
        }


        $union = $qbCall->unionAll($qbEmail);

        $builder = SupplierCall::query()->from($union, 'main');
        $builder->with('supplier');
        $builder->with('files');

        $this->supplierCallRepository->applyOrderBy($builder, $params);

        $total = $this->supplierCallRepository->getCountByQuery($builder);

        $result = $this->supplierCallRepository->getResultsWithPagination(
            $builder,
            $page,
            $perPage,
            $total
        );

        return $result;
    }

    /*public static function addOfferToCall()
    {
        $maxDate = SupplierCall::query()
            ->where('type', SupplierCall::TYPE_OFFER_REQUEST)
            ->max('created_on') ?? '1990-01-01';

        $db = Func::getDb();
        $qb = Offer::query()
            ->select("id")
            ->addSelect('created_on')
            ->addSelect('updated_on')
            ->addSelect('offer_no')
            ->addSelect('customer_id');
        $qbBk = $qb->clone();

        $qb->where('created_on', '=', $maxDate);
        $qb->chunk(1000, function ($items) use (&$db) {
            $db->beginTransaction();
            /** @var Offer $item * /
            foreach ($items as &$item) {
                /** @var SupplierCall $call * /
                $call = SupplierCall::query()->firstOrNew(['offer_id' => $item->id]);
                $call->type = SupplierCall::TYPE_OFFER_REQUEST;
                $call->direction = SupplierCall::DIRECTION_IN;
                $call->supplier_id = $item->customer_id;
                $call->offer_id = $item->id;
                $call->offer_no = $item->offer_no;
                if (!$call->id) {
                    $call->created_on = $item->created_on;
                    $call->updated_on = $item->updated_on;
                }
                $call->save();
            }
            $db->commit();
        });

        $qbBk->where('created_on', '>', $maxDate);
        $qbBk->chunk(1000, function ($items) use (&$db) {
            $rows = [];
            /** @var Offer $item * /
            foreach ($items as &$item) {
                $rows[] = [
                    'type' => SupplierCall::TYPE_OFFER_REQUEST,
                    'direction' => SupplierCall::DIRECTION_IN,
                    'supplier_id' => $item->customer_id,
                    'offer_id' => $item->id,
                    'offer_no' => $item->offer_no,
                    'updated_on' => $item->updated_on,
                    'created_on' => $item->created_on,
                ];
            }
            SupplierCall::insert($rows);
        });

    }*/

    public function delete($ids): void
    {
        $arr = is_array($ids) ? $ids : explode(',', $ids);

        // delete files first
        /** @var SupplierCall[] $calls */
        $calls = SupplierCall::query()
            ->select('id')
            ->whereIn('id', $arr)
            ->with('files')->get();

        /** @var FileService $fileService */
        $fileService = Func::getContainer()->get('file_service');

        foreach ($calls as &$call) {
            if ($call->files) {
                foreach ($call->files as &$file) {
                    $fileService->removeFile($file);
                }
            }
        }

        $this->supplierCallRepository->getQueryBuilder()->whereIn('id', $arr)->delete();
    }
}

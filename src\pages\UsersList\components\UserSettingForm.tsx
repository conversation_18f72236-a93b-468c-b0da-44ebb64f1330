import React from "react";
import { ProFormSelect } from "@ant-design/pro-components";
export type FormValueType = Partial<API.UserListItem>;

export type UserSettingFormProps = {
  values: Partial<API.UserListItem>;
};

export enum UserSettingOrgStatsItemEnum {
  Option1 = "option1",
  Option2 = "option2",
  Option3 = "option3",
}

export const UserSettingOrgStatsItemOptions = [
  { value: UserSettingOrgStatsItemEnum.Option1, label: "Artikelnummer < 50 & Artikelnummer <> 21" },
  { value: UserSettingOrgStatsItemEnum.Option2, label: "Artikelnummer < 50" },
  { value: UserSettingOrgStatsItemEnum.Option3, label: "All" },
];

export const getUserSettingOrgStatsItemOptionsByLevel = (level: UserSettingOrgStatsItemEnum | string) => {
  if (level == UserSettingOrgStatsItemEnum.Option3) {
    return UserSettingOrgStatsItemOptions;
  } else if (level == UserSettingOrgStatsItemEnum.Option2) {
    return [UserSettingOrgStatsItemOptions[0], UserSettingOrgStatsItemOptions[1]];
  } else if (level == UserSettingOrgStatsItemEnum.Option1) {
    return [UserSettingOrgStatsItemOptions[0]];
  }
};

const UserSettingForm: React.FC<UserSettingFormProps> = (props) => {
  return <ProFormSelect name={["settings", "org_stats_item"]} label="Statistic Level" options={UserSettingOrgStatsItemOptions} />;
};

export default UserSettingForm;

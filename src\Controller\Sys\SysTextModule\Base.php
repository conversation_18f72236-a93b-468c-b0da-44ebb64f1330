<?php

declare(strict_types=1);

namespace App\Controller\Sys\SysTextModule;

use App\Controller\BaseController;
use App\Service\Sys\SysTextModule\SysTextModuleService;
use Respect\Validation\Validator as v;
use Slim\Container;

abstract class Base extends BaseController
{
    public SysTextModuleService $sysTextModuleService;

    public function __construct(Container $container)
    {
        parent::__construct($container);
        $this->sysTextModuleService = $container->get(SysTextModuleService::class);
    }
}

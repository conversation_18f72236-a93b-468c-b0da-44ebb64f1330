<?php

declare(strict_types=1);

namespace App\Service\Email\EmailTemplate;

use App\Service\BaseService;
use App\Repository\Email\EmailTemplateRepository;
use Slim\Container;

abstract class Base extends BaseService
{
    private const REDIS_KEY = 'Email:%s';
    protected EmailTemplateRepository $emailTemplateRepository;

    public function __construct(Container $container)
    {
        $this->emailTemplateRepository = $container->get(EmailTemplateRepository::class);
    }

    public function getEmailTemplateRepository()
    {
        return $this->emailTemplateRepository;
    }
}


<?php

declare(strict_types=1);

namespace App\Service\Sys\SysTextModule;

use App\Models\Sys\SysTextModule;

final class SysTextModuleService extends Base
{
    public function create(array $input): SysTextModule
    {
        $number = $input['number'];
        if (SysTextModule::query()->find($number)) {
            \App\Exception\Base::raiseInvalidRequest('Number ' . $number . ' already exists.');
        }

        return SysTextModule::query()->create($input);
    }

    public function update($id, $input): SysTextModule
    {
        $row = SysTextModule::findOrFail($id);

        $newNumber = $input['number'];
        $exists = SysTextModule::query()
            ->where('number', '!=', $id)->where('number', $newNumber)->exists();
        if ($exists) {
            \App\Exception\Base::raiseInvalidRequest('Number ' . $newNumber . ' already exists.');
        }
        $row->update($input);
        return $row;
    }

    public function getOne(int $id, array $params=[]): SysTextModule
    {
        return $this->sysTextModuleRepository->getQueryBuilder()->where('id', $id)->firstOrFail();
    }

    public function getSysTextModulesByPage(
        int $page,
        int $perPage,
        ?array $params
    ): array
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($perPage < 1) {
            $perPage = self::DEFAULT_PER_PAGE_PAGINATION;
        }

        return $this->sysTextModuleRepository->getSysTextModulesByPage(
            $page,
            $perPage,
            $params
        );
    }

    public function delete($ids): void
    {
        $arr = is_array($ids) ? $ids : explode(',', $ids);
        $this->sysTextModuleRepository->getQueryBuilder()->whereIn('number', $arr)->delete();
    }
}

<?php

declare(strict_types=1);

namespace App\Controller\Offer\Offer;

use App\Controller\BaseController;
use App\Service\Offer\OrgOffer\OrgOfferService;
use Slim\Container;
use Slim\Http\Request;
use Slim\Http\Response;

final class GetOrgOfferList extends BaseController
{
    private OrgOfferService $orgOfferService;

    public function __construct(Container $container)
    {
        parent::__construct($container);

        $this->orgOfferService = $container->get(OrgOfferService::class);
    }

    public function __invoke(Request $request, Response $response): Response
    {
        $page = (int)$request->getQueryParam('page', 1);
        $perPage = (int)$request->getQueryParam('perPage', 20);
        $params = $request->getParams();

       $result = $this->orgOfferService->getOrgOffersByPage($page, $perPage, $params);

        return $this->jsonResponse($response, 'success', $result, 200);
    }
}
<?php

declare(strict_types=1);

namespace App\Controller\User;

use App\Exception\User;
use Slim\Http\Request;
use Slim\Http\Response;

final class GetCurrentUser extends Base
{
    public function __invoke(Request $request, Response $response, array $args): Response
    {
        $tokenUser = $this->getUserFromToken($request);
        if (!$tokenUser) User::raiseInvalidRequest('', 404);
        $user = $this->getFindUserService()->getOne((int) $tokenUser['user_id'] );
        unset($user['password']);

        return $this->jsonResponse($response, 'success', $user, 200);
    }
}

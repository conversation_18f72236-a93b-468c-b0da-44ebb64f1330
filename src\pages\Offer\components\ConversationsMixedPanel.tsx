import { But<PERSON>, Col, message, Row, Space, Spin, Typography } from "antd";
import SupplierConversationsPanel from "./partials/SupplierConversationsPanel";
import { ProForm, ProFormInstance, ProFormTextArea } from "@ant-design/pro-components";
import CustomerConversationsPanel from "./partials-customer/CustomerConversationsPanel";
import { Dispatch, SetStateAction, useEffect, useRef } from "react";
import { updateOrCreateOfferExt } from "@/services/app/Offer/offer-ext";
import Util from "@/util";
import { FolderOpenOutlined } from "@ant-design/icons";
import { useModel } from "@umijs/max";
import { DictCode } from "@/constants";

type ConversationsMixedPanelSearchParamsType = { tmp?: any };

type ConversationsMixedPanelProps = {
  searchParams?: ConversationsMixedPanelSearchParamsType;
  isEditing?: number;
  loadOrgOfferDetail?: () => void;
  orgOffer?: APIOrg.Offer;

  loadOfferExt?: () => void;
  offerExt?: API.OfferExt;
  loadingExt?: boolean;
  setLoadingExt?: Dispatch<SetStateAction<boolean>>;
};

const ConversationsMixedPanel: React.FC<ConversationsMixedPanelProps> = ({
  orgOffer,
  isEditing,
  loadOrgOfferDetail,
  loadOfferExt,
  offerExt,
  loadingExt,
  setLoadingExt,
}) => {
  const formRef = useRef<ProFormInstance>();

  const { getDictByCode } = useModel("app-settings");
  // const { setFormChanges } = useModel("org-offer-form-change");

  useEffect(() => {
    if (offerExt) {
      formRef.current?.setFieldsValue(offerExt);
    } else {
      formRef.current?.resetFields();
    }
  }, [offerExt]);

  return (
    <Row gutter={16}>
      {isEditing ? (
        <Col span={9}>
          <SupplierConversationsPanel supplier={orgOffer?.supp_supplier} offer_no={orgOffer?.offer_sid} />
        </Col>
      ) : null}
      <Col flex="auto">
        <Spin spinning={loadingExt}>
          <ProForm
            formRef={formRef}
            onValuesChange={(changedValues, formValues) => {
              // setFormChanges((prev: any) => ({ ...prev, ...changedValues }));
            }}
            submitter={{
              render: () => {
                return (
                  <Space size={12}>
                    <Button
                      type="primary"
                      onClick={() => {
                        const values = formRef.current?.getFieldsValue();
                        const hide = message.loading("Saving offer notes...", 0);
                        updateOrCreateOfferExt({ ...values, offer_no: orgOffer?.offer_sid })
                          .then((res) => {
                            hide();
                            message.success("Offer notes saved successfully.");
                            loadOfferExt?.();
                            // setFormChanges({});
                          })
                          .catch(Util.error)
                          .finally(() => {
                            hide();
                          });
                      }}
                    >
                      Save
                    </Button>
                  </Space>
                );
              },
            }}
          >
            <h3>
              Internal (EK/VK): &nbsp;&nbsp;{" "}
              <Typography.Link
                // href={`${getDictByCode(DictCode.EK_VK_LOCAL_FILE_PATH)}`}
                // target="_blank"
                onClick={(e) => {
                  e.preventDefault();
                  const folderPath = `${getDictByCode(DictCode.EK_VK_LOCAL_FILE_PATH)}`;

                  try {
                    window.open(`file:///${folderPath.replace(/\\/g, "/")}`, "_blank");
                  } catch (error) {
                    navigator.clipboard
                      .writeText(folderPath)
                      .then(() => {
                        message.info("Folder path copied to clipboard!");
                      })
                      .catch(() => {
                        message.error(`Unable to open folder. Please navigate to: ${folderPath}`);
                      });
                  }
                }}
              >
                <FolderOpenOutlined />
              </Typography.Link>
            </h3>
            <ProFormTextArea name="notes_ek_vk" fieldProps={{ rows: 2 }} placeholder="Enter Internal (EK/VK)" />

            <h3>Notes:</h3>
            <ProFormTextArea name="notes" fieldProps={{ rows: 15 }} placeholder={"Enter offer notes."} />
          </ProForm>
        </Spin>
      </Col>
      {isEditing ? (
        <Col span={9}>
          <CustomerConversationsPanel offer_no={orgOffer?.offer_sid} />
        </Col>
      ) : null}
    </Row>
  );
};

export default ConversationsMixedPanel;

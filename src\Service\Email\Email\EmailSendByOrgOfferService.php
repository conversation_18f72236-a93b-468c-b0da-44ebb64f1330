<?php

declare(strict_types=1);

namespace App\Service\Email\Email;

use App\Exception\BaseException;
use App\Lib\FileLib;
use App\Lib\Func;
use App\Lib\FuncModel;
use App\Lib\SysMsg;
use App\Models\Customer\Customer;
use App\Models\Customer\CustomerContact;
use App\Models\Email\Email;
use App\Models\Email\EmailServer;
use App\Models\File;
use App\Models\Supplier\SupplierContact;
use App\Models\Sys\Dict;

/**
 * Biz Target: Sending Emails to Customer
 */
final class EmailSendByOrgOfferService extends Base
{
    /**
     * @param $input
     * @param \Psr\Http\Message\UploadedFileInterface[]|File[] $files
     * @return void
     * @throws \Exception
     */
    public function send($input, $files, $params = [])
    {
        /*$emailMode = (int)FuncModel::getDictValue(Dict::CODE_EMAIL_IS_OAUTH);
        if ($emailMode != 0) {
            return $this->sendV2($input, $files, $params);
        }*/

        $sender = $input['sender'] ?? '';
        $receiver = $input['receiver'] ?? '';
        if (!$sender || !$receiver) {
            BaseException::raiseInvalidRequest('Sender and Receiver are required.');
        }

        /** @var EmailServer $server */
        $server = EmailServer::query()->where('is_oauth', 0)->firstOrFail();

        if (!$server->smtp_host)
            BaseException::raiseInvalidRequest('SMTP host is required!');
        if (!$server->smtp_port)
            BaseException::raiseInvalidRequest('SMTP port is required!');

        // From
        $senders = Func::parseEmailStrList($sender);
        if ($senders)
            $sender = $senders[0];
        else
            BaseException::raiseInvalidRequest('From is required.');


        $input['text_html'] = \App\Service\Email\EmailTemplate\Base::extractInlineImage($input['text_html']);

        $mail = $this->getPhpMailerInstance($server, $params);

        $mail->setFrom(...$sender);

        //Recipients
        $receivers = Func::parseEmailStrList($receiver);
        $receiversKv = [];
        if ($receivers) {
            foreach ($receivers as $to) {
                $mail->addAddress(...$to);
                $receiversKv[$to[0]] = $to[1] ?? '';
            }
        } else {
            BaseException::raiseInvalidRequest("No receivers!");
        }

        $bccRequested = $input['bcc'] ?? '';
        $bcc = ($bccRequested ? $bccRequested . ',' : '') . FuncModel::getDictValue(Dict::CODE_EMAIL_BCC);
        $bccList = Func::parseEmailStrList($bcc);
        $bccKv = [];
        if ($bccList) {
            foreach ($bccList as $e) {
                $mail->addBCC(...$e);
                $bccKv[$e[0]] = $e[1] ?? '';
            }
        }

        //Attachments
        // $mail->addAttachment('/var/tmp/file.tar.gz');         //Add attachments
        // $mail->addAttachment('/tmp/image.jpg', 'new.jpg');    //Optional name
        $dbAttachments = [];
        if ($files) {
            $ind = 0;
            foreach ($files as $attachment) {
                if ($attachment instanceof File) {
                    /** @var File $attachment */
                    $mail->addAttachment($attachment->abs_path);

                    $dbAttachments[] = [
                        'org_name' => $attachment->file_name,
                        'name' => $attachment->clean_file_name,
                        'ext' => (new FileLib(''))->mime2ext($attachment->type),
                        'mime_type' => $attachment->type,
                        'size' => $attachment->size,
                        'file_path' => $attachment->org_path,
                    ];
                } else {
                    $attachmentsPath = Func::pathJoin(self::getEmailAttachmentPathOrSubUrl(), $sender[0], 'sent');
                    if (!file_exists($attachmentsPath)) {
                        mkdir($attachmentsPath, 0755, true);
                    }

                    $newFileName = sprintf("%s_%02s_%s", time(), $ind, Func::getSafeFilePath($attachment->getClientFilename() ?? 'tmp'));
                    $filePath = Func::pathJoin($attachmentsPath, $newFileName);
                    $attachment->moveTo($filePath);
                    $mail->addAttachment($filePath);

                    $dbAttachments[] = [
                        'org_name' => $attachment->getClientFilename(),
                        'name' => $newFileName,
                        'ext' => (new FileLib(''))->mime2ext($attachment->getClientMediaType()),
                        'mime_type' => $attachment->getClientMediaType(),
                        'size' => $attachment->getSize(),
                        'file_path' => Func::pathJoinUrl(self::getEmailAttachmentPathOrSubUrl(true), $sender[0], 'sent', $newFileName),
                    ];
                }
                $ind++;
            }
        }

        //Content
        $mail->Subject = $input['subject'] ?? '';
        $mail->Body = $input['text_html'] ?? '';
        $mail->AltBody = strip_tags($mail->Body);

        // Save data
        $model = new Email();
        $model->email_account_id = null;
        $model->box = 'SENT';
        $model->sender = $sender[0];
        $model->sender_host = substr($sender[0], strpos($sender[0] ?? '', '@') + 1);
        $model->sender_name = $sender[1];
        $model->receiver = $receiver;
        $model->subject = $mail->Subject;
        $model->text_html = $mail->Body;
        $model->text_plain = $mail->AltBody;
        $model->date = Func::dtDbDatetimeStr();
        $model->from_host = $model->sender_host;
        $model->from_name = $sender[1];
        $model->attachments = $dbAttachments;
        $model->has_attachments = count($dbAttachments) > 0;
        $model->supplier_id = $input['supplier_id'] ?? null;
        $model->customer_id = $input['customer_id'] ?? null;
        $model->email_template_id = null;
        $model->offer_no = $input['offer_no'];

        $model->to = $receiversKv;
        $model->bcc = $bccKv;

        $model->save();

        // Save receivers list in database
        $toArrObj = [];
        foreach ($receiversKv as $email_addr => $name) {
            $toArrObj[] = [
                'email_id' => $model->id,
                'email' => $email_addr,
                'name' => $name,
            ];
        }
        $model->receivers()->upsert($toArrObj, ['email_id', 'email']);

        // Sending emails
        $okList = [];
        try {
            foreach ($toArrObj as $x) {
                $mail->clearAddresses();
                $mail->addAddress($x['email'], $x['name']);

                // pre-checking
                $textHtml = $model->text_html;

                /** @var CustomerContact|null $customerContact */
                $customerContact = CustomerContact::query()->where('email', $x['email'])->first();

                $anrede = $customerContact?->salutation ?? '';
                if (!$anrede) {
                    $var = $input['lang'] ?? 'DE';
                    $var = 'OFFER_DEFAULT_ANREDE_' . $var;
                    $anrede = FuncModel::getDictValue($var);
                }
                $textHtml = \Safe\preg_replace("/\@Anrede/", '<span style="font-size: 12px">' . ($anrede) . '</span>', $textHtml);

                $userName = $sender[1] ?? 'WHC';
                $textHtml = \Safe\preg_replace("/\@Username/", '<span style="font-size: 12px">' . ($userName) . '</span>', $textHtml);

                $mail->Body = $textHtml;
                $mail->AltBody = strip_tags($mail->Body);

                // Add tracking pixel
                $mail->Body .= Func::getPixelImageHtml($model->id, $sender[0], $x['email']);
                if ($mail->send()) {
                    $okList[] = Func::buildEmailStr($x['email'], $x['name']);
                }
            }

            if (count($okList) == count($toArrObj)) {
                if (!($params['skip_success_msg'] ?? false)) {
                    SysMsg::get_instance()->success('Email has been sent.');
                }
            } else {
                SysMsg::get_instance()->info("Partially sent emails to " . implode(', ', $okList));
            }
        } catch (\Exception $e) {
            if ($okList) {
                SysMsg::get_instance()->info("Partially sent emails to " . implode(', ', $okList));
            }
            BaseException::raiseInternalServerError("Message could not be sent. Mailer Error: {$mail->ErrorInfo}. " . ($e->getMessage()));
        }
    }
}
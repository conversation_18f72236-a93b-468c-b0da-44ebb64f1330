<?php

declare(strict_types=1);

namespace App\Service\Customer\Customer;

use App\Repository\Customer\CustomerRepository;
use App\Service\BaseService;
use Slim\Container;

abstract class Base extends BaseService
{
    protected CustomerRepository $customerRepository;

    public function __construct(Container $container)
    {
        $this->customerRepository = $container->get(CustomerRepository::class);
    }

    public function getCustomerRepository()
    {
        return $this->customerRepository;
    }
}


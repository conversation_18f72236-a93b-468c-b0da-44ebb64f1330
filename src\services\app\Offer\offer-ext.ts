import { request } from 'umi';
import { paramsSerializer } from '../api';
import { RequestConfig } from '@umijs/max';
import { RequestOptionsType } from '@ant-design/pro-components';

const urlPrefix = '/api/offer/offer-ext';

/**
 * Get OfferExts list
 *
 * GET /api/offer/offer-ext
 */
export async function getOfferExtListByPage(params: API.PageParams, sort?: any, filter?: any) {
  return request<API.ResultList<API.OfferExt>>(`${urlPrefix}`, {
    method: 'GET',
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == 'success',
    total: res.message.pagination.totalRows,
    pagination: res.message.pagination, // For total row pagination hack.
  }));
}

export async function getOfferExt(id?: number, params?: API.OfferExt & API.PageParams) {
  return getOfferExtListByPage({ id, ...params, pageSize: 1, page: 1 }).then((res) => {
    return res.data?.[0];
  });
}

export async function getOfferExtByOfferNo(offer_no?: number | string, params?: API.OfferExt & API.PageParams) {
  return getOfferExtListByPage({ offer_no: offer_no, ...params, pageSize: 1, page: 1 }).then((res) => {
    return res.data?.[0];
  });
}

/**
 * Create supplier.
 *
 *  POST /api/offer/offer-ext */
export async function addOfferExt(
  data?: API.OfferExt | FormData,
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'POST',
    data,
    ...(options || {}),
  };
  return request<API.ResultObject<API.OfferExt>>(`${urlPrefix}`, config).then((res) => res.message);
}

/**
 * Update OfferExt data.
 *
 *  PUT /api/offer/offer-ext/{id}/update */
export async function updateOfferExt(
  id?: number,
  data?: API.OfferExt | FormData,
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'PUT',
    data,
    ...(options || {}),
  };
  return request<API.ResultObject<API.OfferExt>>(`${urlPrefix}/${id}/update`, config).then(
    (res) => res.message,
  );
}



/**
 * Update or Create OfferExt list.
 *
 *  POST /api/offer/offer-ext/updateOrCreate */
export async function updateOrCreateOfferExt(
  data?: API.OfferExt | FormData,
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: 'POST',
    data,
    ...(options || {}),
  };
  return request<API.ResultObject<API.OfferExt>>(`${urlPrefix}/updateOrCreate`, config).then(
    (res) => res.message,
  );
}



/**
 * Get AC List dropdown list.
 *
 * GET /api/offer/offer-ext/getOfferExtACList
 */
export async function getOfferExtACList(params: API.PageParams) {
  return request<API.ResultObject<RequestOptionsType[]>>(`${urlPrefix}/getOfferExtACList`, {
    method: 'GET',
    params: {
      ...params,
      perPage: 500,
      page: params.current,
      sort: { id: 'descend' },
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message);
}

/** delete DELETE /api/offer/offer-ext/{id} */
export async function deleteOfferExt(id?: string | number, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/${id}`, {
    method: 'DELETE',
    ...(options || {}),
  });
}

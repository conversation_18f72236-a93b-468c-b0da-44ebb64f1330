<?php

declare(strict_types=1);

namespace App\Controller\Email\Email;

use App\Models\Email\Email;
use Slim\Http\Request;
use Slim\Http\Response;

final class GetReasonableSenders extends Base
{
    public function __invoke(Request $request, Response $response, array $args): Response
    {
        $params = $request->getParams();
        $id = intval($params['id'] ?? 0);

        /** @var Email $email */
        $email = Email::query()->find($id);

        // Sender
        $senders = [];
        if ($email) {
            $senders[] = [
                'key' => $email->sender,
                'name' => $email->sender_name,
                'source' => 'direct',
            ];
        } else {
            // $emails = Email::
        }

        $source = 'direct';
        $optionsList = [];
        foreach ($senders as $sender) {
            // type: 'divider'
            if ($sender['source'] != $source ) {
                $optionsList[] = [
                    'key' => $sender['source'],
                    'type' => 'divider',
                    'label' => $sender['source'],
                ];
            }
            $key = $sender['key'];
            if ($sender['name']) {
                $key = $sender['name'] . ' <'.$key . '>';
            }
            $optionsList[] = [
                'key' => $key,
                'label' => $key,
                'name' => $sender['name'],
                'source' => $sender['source'],
            ];

            $source = $sender['source'];
        }

        return $this->jsonResponse($response, 'success', $optionsList, 200);
    }
}

<?php

declare(strict_types=1);

namespace App\Service\Sys\Trademark;

use App\Models\Sys\Trademark;
use Illuminate\Database\Eloquent\Model;

/**
 * @deprecated
 */
final class TrademarkService extends Base
{
    public function create(array $input): Trademark
    {
        return Trademark::create($input);
    }

    public function update($id, $input): Trademark
    {
        $row = Trademark::findOrFail($id);
        $row->update($input);
        return $row;
    }

    /**
     * @param int $id
     * @param array $params
     * @return Trademark|Model
     */
    public function getOne(int $id, array $params=[]): Trademark
    {
        return $this->trademarkRepository->getQueryBuilder()->where('id', $id)->first();
    }

    public function getTrademarksByPage(
        int $page,
        int $perPage,
        ?array $params
    ): array
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($perPage < 1) {
            $perPage = self::DEFAULT_PER_PAGE_PAGINATION;
        }

        return $this->trademarkRepository->getTrademarksByPage(
            $page,
            $perPage,
            $params
        );
    }

    public function delete($ids): void
    {
        $arr = is_array($ids) ? $ids : explode(',', $ids);
        $this->trademarkRepository->getQueryBuilder()->whereIn('id', $arr)->delete();
    }
}

import type { ProFormInstance } from '@ant-design/pro-form';
import { CheckboxOptionType } from 'antd';
import React, { useEffect, useState } from 'react';

/**
 * Auto completion list of Active User
 */
export default (
    defaultParams?: Record<string, any>,
    formRef?: React.MutableRefObject<ProFormInstance | undefined>,
    eleOptions?: any,
) => {
    const [options, setOptions] = useState<CheckboxOptionType[]>([]);
    const [selectedOptionValues, setSelectedOptionValues] = useState<(string | number)[]>([]);

    useEffect(() => {
        const arr: CheckboxOptionType[] = [
            { value: 'all', label: 'All' },
            { value: '-', label: 'N/A' },
        ];
        for (let i = 0; i <= 9; i++) {
            arr.push({
                value: i,
                label: i,
            });
        }
        setOptions(arr);
    }, []);


    useEffect(() => {
        if (options?.length) {
            setOptions(prev => {
                const newPrev = [...prev];
                if (selectedOptionValues?.includes('all')) {
                    newPrev.forEach((x) => {
                        if (x.value != 'all') {
                            x.disabled = true;
                        }
                    });
                } else {
                    newPrev.forEach((x) => {
                        if (x.value != 'all') {
                            x.disabled = false;
                        }
                    });
                }

                return newPrev;
            })
        }
    }, [options?.length, selectedOptionValues]);

    /* const orgAList = useMemo(() => {
        return orgAOptions.map((x) => x.value);
    }, [orgAOptions]); */

    return {
        options,
        setOptions,
        selectedOptionValues,
        setSelectedOptionValues,
    };
};

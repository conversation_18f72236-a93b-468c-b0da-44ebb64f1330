<?php

namespace App\Models\Supplier;

use App\Models\BaseModel;

/**
 * @property integer $supplier_id
 * @property string $type
 * @property string $value
 * @property string $value_long
 */
class SupplierMeta extends BaseModel
{
    public const TYPE_PRODUCT_TRADEMARK = 'product_trademark';
    public const TYPE_PRODUCT_CATEGORY = 'product_category';
    public const TYPE_SUPPLIER = 'supplier_type';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'supplier_meta';

    protected $casts = [
        'value' => SupplierMetaValueCast::class,
    ];

    /**
     * @var array
     */
    protected $fillable = ['supplier_id', 'type', 'value', 'value_long'];
}

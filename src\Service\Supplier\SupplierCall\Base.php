<?php

declare(strict_types=1);

namespace App\Service\Supplier\SupplierCall;

use App\Service\BaseService;
use App\Repository\Supplier\SupplierCallRepository;
use Slim\Container;

abstract class Base extends BaseService
{
    protected SupplierCallRepository $supplierCallRepository;

    public function __construct(Container $container)
    {
        $this->supplierCallRepository = $container->get(SupplierCallRepository::class);
    }

    public function getSupplierCallRepository()
    {
        return $this->supplierCallRepository;
    }
}


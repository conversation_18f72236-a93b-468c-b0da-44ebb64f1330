<?php

declare(strict_types=1);

namespace App\Service\Supplier\SupplierContact;

use App\Models\Supplier\SupplierContact;

final class SupplierContactService extends Base
{
    public function create(array $input): SupplierContact
    {
        return SupplierContact::create($input);
    }

    public function update($id, $input): SupplierContact
    {
        $row = SupplierContact::findOrFail($id);
        $row->update($input);
        return $row;
    }

    public function getOne(int $id, array $params=[]): SupplierContact
    {
        return $this->supplierContactRepository->getQueryBuilder()->where('id', $id)->first();
    }

    public function getSupplierContactsByPage(
        int $page,
        int $perPage,
        ?array $params
    ): array
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($perPage < 1) {
            $perPage = self::DEFAULT_PER_PAGE_PAGINATION;
        }

        return $this->supplierContactRepository->getSupplierContactsByPage(
            $page,
            $perPage,
            $params
        );
    }

    public function delete($ids): void
    {
        $arr = is_array($ids) ? $ids : explode(',', $ids);
        $this->supplierContactRepository->getQueryBuilder()->whereIn('id', $arr)->delete();
    }
}

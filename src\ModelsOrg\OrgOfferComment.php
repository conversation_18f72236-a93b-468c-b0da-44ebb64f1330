<?php

namespace App\ModelsOrg;

/**
 * @property string $id
 * @property string $offer_id
 * @property string $comment
 * @property string $created_on
 * @property string $created_by
 * @property string $updated_on
 * @property string $updated_by
 * @property string $sc_id
 * @property string $customer_id
 * @property integer $customer_order
 */
class OrgOfferComment extends BaseModel
{
    use \Staudenmeir\EloquentEagerLimit\HasEagerLimit;

    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'offer_comments';

    /**
     * The "type" of the auto-incrementing ID.
     * 
     * @var string
     */
    protected $keyType = 'string';

    /**
     * Indicates if the IDs are auto-incrementing.
     * 
     * @var bool
     */
    public $incrementing = false;

    /**
     * @var array
     */
    protected $fillable = ['offer_id', 'comment', 'created_on', 'created_by', 'updated_on', 'updated_by', 'sc_id', 'customer_id', 'customer_order'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function sysConfig()
    {
        return $this->belongsTo('App\ModelsOrg\OrgSysConfig', 'sc_id', 'code');
    }
}

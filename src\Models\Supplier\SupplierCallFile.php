<?php

namespace App\Models\Supplier;

use App\Models\BaseModel;

/**
 * @property integer $call_id
 * @property integer $file_id
 * @property SupplierCall $supplierCall
 * @property File $file
 */
class SupplierCallFile extends BaseModel
{
    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'supplier_call_file';

    /**
     * @var array
     */
    protected $fillable = [];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function supplierCall()
    {
        return $this->belongsTo('App\Models\Supplier\SupplierCall', 'call_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function file()
    {
        return $this->belongsTo('App\Models\Supplier\File');
    }
}

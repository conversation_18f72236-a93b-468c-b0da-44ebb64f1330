<?php

declare(strict_types=1);

namespace App\Controller\Supplier\Supplier;

use App\App\Constants;
use App\Exception\BaseException;
use App\ModelsOrg\OrgOffer;
use App\ModelsOrg\OrgSupplier;
use Slim\Http\Request;
use Slim\Http\Response;

/**
 * Get all offers for a specific supplier
 */
final class GetSupplierOffers extends Base
{
    public function __invoke(Request $request, Response $response): Response
    {
        $page = (int)$request->getQueryParam('page', 1);
        $perPage = (int)$request->getQueryParam('perPage', Constants::DEFAULT_PER_PAGE_PAGINATION);
        $params = $request->getParams();

        // Get supplier_id from supp_supplier_id
        $suppSupplierId = $params['supp_supplier_id'] ?? null;
        if (!$suppSupplierId) {
            BaseException::raiseInvalidRequest('supp_supplier_id is required');
        }

        // Find the org supplier ID
        $orgSupplier = OrgSupplier::query()
            ->where('supp_supplier_id', $suppSupplierId)
            ->first();

        if (!$orgSupplier) {
            BaseException::raiseInvalidRequest('Org Supplier not found');
        }

        // Build query for offers
        $query = OrgOffer::query()
            ->where('supplier_id', $orgSupplier->id)
        ;

        // Apply status filter if provided
        if (isset($params['status'])) {
            $query->where('status', $params['status']);
        }

        if ($params['sort'] ?? null) {
            $this->userRepository->applyOrderBy($query, $params);
        } else {
            $query->orderBy('updated_on', 'desc');
        }

        // Get total count
        $total = $query->count();

        $result = $this->userRepository->getResultsWithPagination($query, $page, $perPage, $total);

        return $this->jsonResponse($response, 'success', $result, 200);
    }
}

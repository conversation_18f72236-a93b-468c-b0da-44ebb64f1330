<?php

declare(strict_types=1);

namespace App\Controller\Supplier\Supplier;

use App\ModelsOrg\OrgOffer;
use Slim\Http\Request;
use Slim\Http\Response;

/**
 * Get all offers for a specific supplier
 */
final class GetSupplierOffers extends Base
{
    public function __invoke(Request $request, Response $response): Response
    {
        $page = (int)$request->getQueryParam('page', 1);
        $perPage = (int)$request->getQueryParam('perPage', 20);
        $params = $request->getParams();

        // Get supplier_id from supp_supplier_id
        $suppSupplierId = $params['supp_supplier_id'] ?? null;
        if (!$suppSupplierId) {
            return $this->jsonResponse($response, 'error', ['message' => 'supp_supplier_id is required'], 400);
        }

        // Find the org supplier ID
        $orgSupplier = \App\ModelsOrg\OrgSupplier::query()
            ->where('supp_supplier_id', $suppSupplierId)
            ->first();

        if (!$orgSupplier) {
            return $this->jsonResponse($response, 'error', ['message' => 'Supplier not found'], 404);
        }

        // Build query for offers
        $query = OrgOffer::query()
            ->where('supplier_id', $orgSupplier->id)
            ->orderBy('created_on', 'desc');

        // Apply status filter if provided
        if (isset($params['status'])) {
            $query->where('status', $params['status']);
        }

        // Get total count
        $total = $query->count();

        // Apply pagination
        $offset = ($page - 1) * $perPage;
        $offers = $query->offset($offset)->limit($perPage)->get()->toArray();

        $result = [
            'pagination' => [
                'totalRows' => $total,
                'totalPages' => ceil($total / $perPage),
                'currentPage' => $page,
                'perPage' => $perPage,
                'hasMore' => $page < ceil($total / $perPage),
            ],
            'data' => $offers,
        ];

        return $this->jsonResponse($response, 'success', $result, 200);
    }
}

<?php

declare(strict_types=1);

namespace App\Repository\Offer;

use App\Models\Offer\OfferTemplateLang;
use App\Repository\BaseRepositoryORM;
use Illuminate\Database\Eloquent\Builder;

final class OfferTemplateLangRepository extends BaseRepositoryORM
{
    public function getQueryOfferTemplateLangsByPage($params = []): Builder
    {
        $qb = $this->getQueryBuilder();

        if (!empty($params)) {
            $qb = $this->applyOrderBy($qb, $params);
        }

        return $qb;
    }

    /**
     * Get lists by pagination.
     *
     * @param int $page
     * @param int $perPage
     * @param array|null $params
     * @return array
     */
    public function getOfferTemplateLangsByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        $query = $this->getQueryOfferTemplateLangsByPage($params);
        $total = $this->getCountByQuery($query);

        $result = $this->getResultsWithPagination(
            $query,
            $page,
            $perPage,
            $total
        );

        return $result;
    }

    public function getQueryBuilder(): Builder
    {
        return OfferTemplateLang::query();
    }
}
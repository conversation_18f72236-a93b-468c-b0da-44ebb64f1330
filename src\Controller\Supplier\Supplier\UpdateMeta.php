<?php

declare(strict_types=1);

namespace App\Controller\Supplier\Supplier;

use App\Exception\BaseException;
use Slim\Http\Request;
use Slim\Http\Response;

final class UpdateMeta extends Base
{
    public function __invoke(Request $request, Response $response, array $args): Response
    {
        $id = $args['id'] ?? null;
        if (!$id) {
            \App\Exception\Base::raiseInvalidRequest('ID is required.');
        }

		$input = (array) $request->getParsedBody();

        if (!key_exists('meta', $input)) {
            BaseException::raiseInvalidRequest('Meta is required!');
        }

        $row = $this->supplierService->updateMeta($id, $input['meta']);

        return $this->jsonResponse($response, 'success', $row, 200);
    }
}

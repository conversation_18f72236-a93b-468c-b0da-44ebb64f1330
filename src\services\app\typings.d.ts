type Nullable<T> = T | null;

type JSONValue = Record<string, any>;

type ZeroOrOne = 0 | 1;

declare namespace API {
  type BaseResult = {
    status?: 'success' | 'error' | 'info';
    code?: number;
    message?: any;
    messageFlash?: Record<string, any>;
  };

  type PaginatedResult<T> = {
    data: T[];
    totalRows?: T[];
    summary?: T[];
    avgRow?: T;
    success: boolean;
    total: number;
    pagination?: any;
  };

  type ResultList<T> = Omit<BaseResult, 'message'> & {
    message: PaginatedResult<T>;
  };

  type ResultObject<T> = Omit<BaseResult, 'message'> & {
    message: T;
  };

  type Result<T> = Omit<BaseResult, 'message'> & {
    message: PaginatedResult<T>;
  };

  type Downloadable = {
    type?: string;
    key?: string;
    file?: string;
    url?: string;
    sqls?: any[];
    extra?: any; // extra data
  } & {
    b64?: string; // base 64 data
  };

  type ResultDownloadable = ResultObject<Downloadable>;

  type CurrentUser = {
    user_id?: number;
    username?: string;
    email?: string;
    name?: string;
    avatar?: string;
    status?: number;
    role?: number;
    created_on?: string;
    updated_on?: string;
    last_login_on?: string;
    nonce?: string;
    client_detail?: string;
    settings?: Record<string, any> & {
      org_stats_item?: string;
    };
    password?: string;
    confirmPassword?: string;

    initials?: string;
  };

  type Pagination = {
    totalRows: number;
    totalPages: number;
    currentPage: number;
    perPage: number;
    hasMore?: boolean;
  };

  type LoginResult = {
    Authorization?: string;
    type?: string;
  };

  type UserListItem = CurrentUser;
  type UserList = {
    data: UserListItem[];
    success: boolean;
    total: number;
  };
  type userParams = {
    page?: number;
    perPage?: number;
  };

  type PageParams = {
    current?: number;
    pageSize?: number;
    keyword?: string;
    keyWords?: string;
    with?: string;
  } & Record<string, any>;

  type PageParamsExt = {
    current?: number;
    pageSize?: number;
    keyword?: string;
  } & Record<string, any>;

  /**
   * -------------------------------- Biz -----------------------------------------------
   */
  type Dict = {
    code?: string; //PK
    type?: string;
    label?: string;
    sort?: number;
    value?: any;
    status?: 0 | 1;
    settings?: Record<string, any>;
    desc?: string;
    parent_code?: string;
    casted_value?: any; // specific data

    parent?: Dict;
  } & { category_code?: string };

  type DictList = PaginatedResult<Dict>;

  type CountryRegion = {
    id?: number;
    country_code?: string;
    code?: string;
    default_name?: string;

    // relations
    country?: Country;
  };

  type CountryRegionList = PaginatedResult<CountryRegion>;

  type Country = {
    id?: number;
    code?: string;
    iso3_code?: string;
    name?: string;
    dial_code?: string;

    // relations
    country_regions?: CountryRegion[];
  };

  type CountryList = PaginatedResult<Country>;

  type ProductCategory = {
    id?: number; //PK
    name?: string;
    is_active?: ZeroOrOne;
  };

  type HtmlFieldType =
    | 'number'
    | 'number2'
    | 'text'
    | 'textarea'
    | 'switch'
    | 'select'
    | 'multiselect'
    | 'checkbox'
    | 'radio'
    | 'date'
    | 'divider';

  type File = {
    id?: number;
    file_name?: string;
    clean_file_name?: string;
    path?: string;
    org_path?: string;
    size?: number;
    type?: string;
    created_at?: string;
    updated_at?: string;
  } & {
    url?: string;
    thumb_url?: string;
    abs_path?: string;
    uid?: string;
  };

  type FileList = {
    data: File[];
    success: boolean;
    total: number;
  };


  type Supplier = {
    id?: number;
    internal_name?: string;
    name?: string;
    org_a?: string;
    is_active?: number;
  } & CreatorData & UpdaterData & {
    // addresses?: SupplierAddress[];
    contacts?: SupplierContact[]; // active contacts only.
    contacts_all?: SupplierContact[];
    address?: SupplierAddress;
    ext?: SupplierExt;
    calls?: SupplierCall[];
    meta?: SupplierMeta[];
    product_categories?: SupplierMeta[];
    product_trademarks?: SupplierMeta[];

  } & {
    matrix?: Record<string | number, {
      email?: string;
      id?: number;  // template ID
      last_seen_on?: string;
    }>;

    matrix2?: Record<string | number, API.EmailReceiver & { email_template_id?: number, subject?: string, sent_on?: string } & {
      email?: string;
      id?: number;  // template ID
      last_seen_on?: string;
    }>;

    calls_phone_count?: number;
  } & {
    // stats from WHC_Task
    task_invoice_stats?: Partial<APITask.FinDetail> & Partial<APITask.LoInvoiceCheck>;
    org_supplier?: APIOrg.Supplier;
  };

  type SupplierAddress = {
    id?: number;
    supplier_id?: number;
    company?: string;
    city?: string;
    country_id?: number;
    fax?: string;
    postcode?: string;
    region?: any;
    region_id?: number;
    street1?: string;
    street2?: string;
    telephone?: string;
  } & TimestampData & {
    fullname?: string;
    street?: string;
    supplier?: Supplier;
    country?: Country;
    country_region?: CountryRegion;
  }

  type SupplierContact = {
    id?: number;
    supplier_id?: number;
    department?: string;
    email?: string;
    is_active?: ZeroOrOne;
    is_default?: ZeroOrOne;
    firstname?: string;
    lastname?: string;
    middlename?: string;
    prefix?: string;
    suffix?: string;
    telephone?: string;
    note?: string;
    is_asp?: ZeroOrOne;
    is_asp_reg?: ZeroOrOne;
    is_blocked?: ZeroOrOne;
  } & TimestampData & {
    fullname?: string;
  };

  type SupplierExt = {
    id?: number;
    supplier_id?: number;
    note?: string;
    user_id?: number;
    relevance?: number;
  } & UpdaterData & CreatorData & {
    supplier?: Supplier;
    user?: CurrentUser;
  };

  type SupplierCall = {
    id?: number;
    supplier_id?: number;
    type?: number;
    direction?: string | null;
    offer_id?: number;
    offer_no?: number | string;
    note?: string;
    comment?: string;
  } & UpdaterData & CreatorData & {
    supplier?: Supplier;
    org_supplier?: APIOrg.Supplier;
    files?: File[];
    offer_ext?: OfferExt;
  } & {
    // merged values
    uid?: string;
    date?: string;
    email_subject?: string;
    email_open_count?: string;
    email_open_first_seen_on?: string;
    email_open_seen_updated_on?: string;
    org_supplier?: APIOrg.Supplier;
    org_offer?: APIOrg.Offer;
  };

  type SupplierMeta = {
    supplier_id?: number;
    type?: string;
    value?: string | number;
    value_long?: string;
  } & {
    label?: string | number; // casted value of `value`.
  }

  /** 
   * Customer management
   * --------------------------------------------------------------------------------------------- */
  type Customer = {
    id?: number;
    name?: string;
    org_a?: string;
    is_active?: number;
  } & CreatorData & UpdaterData & {
    contacts?: CustomerContact[]; // active contacts only.
    contacts_count?: number;
    contacts_all?: CustomerContact[];
    address?: CustomerAddress;
    ext?: CustomerExt;
  } & {
    // calc values
    existsOfferNoStatus?: boolean;
    email_kv?: Record<string, number>; // map of email address to email ID.
  };

  type CustomerAddress = {
    id?: number;
    supplier_id?: number;
    company?: string;
    city?: string;
    country_id?: number;
    fax?: string;
    postcode?: string;
    region?: any;
    region_id?: number;
    street1?: string;
    street2?: string;
    telephone?: string;
  } & TimestampData & {
    fullname?: string;
    street?: string;
    supplier?: Customer;
    country?: Country;
    country_region?: CountryRegion;
  }

  type CustomerContact = {
    id?: number;
    supplier_id?: number;
    department?: string;
    email?: string;
    is_active?: ZeroOrOne;
    is_default?: ZeroOrOne;
    firstname?: string;
    lastname?: string;
    middlename?: string;
    prefix?: string;
    suffix?: string;
    telephone?: string;
    note?: string;
    lang?: string;
    salutation?: string;
    is_blocked?: ZeroOrOne;
  } & TimestampData & {
    fullname?: string;
    customer?: Customer;
    is_offer_sent?: ZeroOrOne;
  };

  type CustomerExt = {
    id?: number;
    customer_id?: number;
    note?: string;
    user_id?: number;
    relevance?: number;
  } & UpdaterData & CreatorData & {
    customer?: Customer;
    user?: CurrentUser;
  };

  type CustomerCall = {
    id?: number;
    customer_id?: number;
    type?: number;
    direction?: string;
    offer_no?: number | string;
    note?: string;
    ref_type?: string;
    ref_id?: string;
  } & UpdaterData & CreatorData & {
    customer?: Customer;
    // files?: File[]; // not used yet.
    customers?: Customer[]; // calculated object array
  } & {
    // merged values
    uid?: string;
    date?: string;
    email_subject?: string;
    email_open_count?: string;
    email_open_first_seen_on?: string;
    email_open_seen_updated_on?: string;
  };

  /** 
   * Offer Related
   * --------------------------------------------------------------------------------------------- */
  type OfferTemplate = {
    id?: number;
    offer_no?: string;
    title?: string;
  } & {
    offer_template_langs?: OfferTemplateLang[];
    files?: File[];
  } & UpdaterData & CreatorData;

  type OfferTemplateLang = {
    id?: number;
    offer_template_id?: number;
    lang?: string;
    subject?: string;
    header?: string;
    body?: string;
    footer?: string;
  } & {
    offer_template?: OfferTemplate;
  } & UpdaterData & CreatorData;

  type OfferExt = {
    id?: number;
    offer_no?: string;
    notes?: string;
    notes_ek_vk?: string;
    status?: number;
    is_brand?: ZeroOrOne;
    is_b_group_appr?: ZeroOrOne;
    prio?: number;
    details?: {
      us_newsletter_dt?: string;
      us_blog_dt?: string;
    }
  } & {
    newsletters?: API.OfferNewsletter[];
    blog?: API.OfferBlog;
  } & UpdaterData & CreatorData;


  type OfferBlog = {
    id?: number;
    offer_no?: string;
    title?: string;
    description?: string;
    status?: number;
  } & {
    files?: File[];
  } & UpdaterData & CreatorData;

  type OfferBlogFile = {
    blog_id?: number;
    file_id?: number;
  }


  type OfferNewsletter = {
    id?: number;
    offer_no?: string;
    product_title?: string;
    case_qty?: number;
    ve_pallet?: number;
    price?: number;
    sort_order?: number;
    details?: Record<string, any>;
  } & {
    files?: File[];
  } & UpdaterData & CreatorData;

  type OfferNewsletterFile = {
    offer_newsletter_id?: number;
    file_id?: number;
  }




  /** 
   * Email management
   * --------------------------------------------------------------------------------------------- */
  type EmailAccountSettings = {
    imapSince?: string;
  };

  type EmailAccount = {
    id?: number;
    email?: string;
    server_id?: number;
    status?: number;
    sender_name?: string;
    pop_type?: 'IMAP' | 'POP';

    settings?: EmailAccountSettings;
    email_server?: EmailServer;
  } & CreatorData &
    UpdaterData;

  type EmailAccountList = PaginatedResult<EmailAccount>;

  type EmailServer = {
    id?: number;
    domain?: string;
    imap_host?: string;
    imap_port?: number;
    imap_port_ssl?: number;
    imap_ssl?: number;
    pop_host?: string;
    pop_port?: number;
    pop_port_ssl?: number;
    smtp_host?: string;
    smtp_port?: number;
    smtp_user?: string;
    smtp_password?: string;
    settings?: Record<string, any>;
    is_oauth?: number;
  };

  type EmailServerList = PaginatedResult<EmailServer>;

  type EmailAttachment = {
    org_name?: string;
    name?: string;
    ext?: string;
    mime_type?: string;
    size?: number;
    subtype?: string;
    file_path?: string;
    id?: string;
  };

  type Email = {
    id?: number;
    email_account_id?: number;
    message_id?: string;
    mail_id?: number;
    box?: string;
    sender?: string;
    sender_host?: string;
    sender_name?: string;
    receiver?: string;
    subject?: string;
    date?: string;
    date_str?: string;

    text_html?: string;
    text_plain?: string;
    has_attachments?: boolean;
    is_seen?: boolean;
    is_answered?: boolean;
    is_recent?: boolean;
    is_flagged?: boolean;
    is_deleted?: boolean;
    is_draft?: boolean;
    from_host?: string;
    from_name?: string;
    to?: string[];
    reply_to?: string[];
    cc?: string[];
    bcc?: string[];
    attachments?: EmailAttachment[];
    mime_version?: string;
    content_type?: string;
    is_hidden?: number; // Hidden status

    // extra info
    supplier_id?: number;
    customer_id?: number;
    email_template_id?: number;
    status?: string;
  } & {
    // relations
    email_account?: EmailAccount;
    receivers?: EmailReceiver[];
    supplier?: Supplier;
    customer?: Customer;
    email_template?: EmailTemplate;
  } & CreatorData & { text?: string };

  type EmailList = PaginatedResult<Email>;

  type EmailTemplate = {
    id?: number;
    category_id?: number;
    sort?: number;
    title?: string;
    subject?: string;
    text_html?: string;
    text_plain?: string;
    created_on?: string;
    updated_on?: string;
  } & {
    category?: EmailTemplateCategory;
  };

  type EmailTemplateCategory = {
    id?: number;
    cat1?: string;
    sort?: number;
  } & {
    email_templates?: EmailTemplate[];
  }

  type EmailReceiver = {
    email_id?: number;
    email?: string;
    name?: string;
    open_count?: number;
    open_first_seen_on?: string;
    open_seen_updated_on?: string;
  } & {
    email_obj?: Email;
  }

  /** 
   * Sys related models
   * --------------------------------------------------------------------------------------------- */
  type SysTextModule = {
    number: number;
    text?: string;
  };

  type SysTextModuleList = PaginatedResult<SysTextModule>;


  type SysLog = {
    id?: number;
    category?: string;
    name?: string;
    note?: string;
    status?: string;
    ean_id?: number;
    ref1?: string;
    ref2?: string;
    request_uri?: string;
    request_method?: string;
    created_by?: number;
    created_on?: string;
  } & CreatorData & {
    user?: CurrentUser;
    item_ean?: Ean;
  }



  type AppSettings = {
    dict?: Record<string, any>;
    drSelection: Record<string, string[]>;
    serverTz?: number;
    FY_START_MONTH?: number;
  };
  type LoginParams = {
    username?: string;
    password?: string;
    autoLogin?: boolean;
    type?: string;
  };

  type ErrorResponse = {
    /** business contract error code */
    errorCode: string;
    /** business misinformation */
    errorMessage?: string;
    /** Whether the business request is successful */
    success?: boolean;
  };

  type TimestampData = {
    created_on?: string;
    updated_on?: string;
  };

  type CreatorData = {
    created_by?: any;
    created_on?: string;
  };
  type UpdaterData = {
    updated_by?: any;
    updated_on?: string;
  };

  type AppApiResponse = {
    code?: number;
    message?: any;
    status?: string;
  };
}


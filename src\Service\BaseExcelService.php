<?php

namespace App\Service;

use App\Lib\ExcelExportLib;
use App\Lib\FileLib;
use App\Lib\Func;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Query\Builder;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Exception;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Worksheet\PageSetup;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Writer\Xls;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class BaseExcelService
{
    const CREATOR = 'FsOne';
    const KEYWORD = 'Foodstore One office 2007 openxml php';

    // XLS Data types
    const DATA_TYPE_STRING = ExcelExportLib::DATA_TYPE_STRING;
    const DATA_TYPE_NUM = ExcelExportLib::DATA_TYPE_NUM;
    const DATA_TYPE_INT = ExcelExportLib::DATA_TYPE_INT;
    const DATA_TYPE_DATE = ExcelExportLib::DATA_TYPE_DATE;
    const DATA_TYPE_DATETIME = ExcelExportLib::DATA_TYPE_DATETIME;
    const DATA_TYPE_YN = 'yn';
    const DATA_TYPE_STRING_DIGIT = 'string_digit';
    const DATA_TYPE_DIGIT = '0';


    const DATA_TYPE_NUM_IN_MIXED = 'numInMixed';  // read mode only, it can be formatted in German or English

    protected Spreadsheet $spreadsheet;
    protected $isLandscape = false;

    protected $basePath = '/tmp';   // /{basePath}/{relPath}/{file_name}.{extension}
    protected $relPath = '/tmp';    //
    protected $extension = 'xlsx';

    /**
     * @var array [ArrayShape(['creator' => [ArrayShape(['type' => '', 'label' => '', dbField => '', maxLength => '', xlsFormat => ''])]])]
     */
    /* Example
     * 'postcode' => [
        'type' => self::DATA_TYPE_STRING,
        'label' => 'Item No',
        'maxLength' => 10,
        'required' => true,
        'dbField' => ['sa_zip'],
        'xlsFormat' => ''
    ],*/
    /* [
            [
                'type' => self::DATA_TYPE_STRING_DIGIT,
                'label' => 'EAN',
                'dbField' => ['item_ean', 'ean'],
                'width' => 17,
                'align' => Alignment::HORIZONTAL_LEFT,
            ],
            [
                'type' => self::DATA_TYPE_INT,  // 4
                'label' => 'Qty',
                'dbField' => ['qty'],
                'summable' => true,
                'width' => 10,
            ],
            [
                'type' => self::DATA_TYPE_NUM,
                'label' => 'Price Total',
                'dbField' => ['sum_qty'],
                'width' => 13,
                'summable' => true,
                // 'xlsFormat' => ExcelExportLib::FORMAT_I_NORM,
                // 'align' => Alignment::HORIZONTAL_CENTER,
                'getValue' => function ($row, $defKey, $def, $rowNo) {
                    $priceColName = Coordinate::stringFromColumnIndex(5);
                    $qtyColName = Coordinate::stringFromColumnIndex(4);
                    return "={$priceColName}{$rowNo}*{$qtyColName}{$rowNo}";
                },
            ],
            [
                'type' => self::DATA_TYPE_STRING,
                'label' => 'NAN',
                'dbField' => ['nan'],
                'width' => 10,
            ],
            [
                'type' => self::DATA_TYPE_INT,
                'label' => 'Pallet (pcs)',
                'dbField' => ['qty_pallet'],
                'width' => 10,
            ],
            [
                'type' => self::DATA_TYPE_NUM,
                'label' => 'Pallet Price',
                'dbField' => ['price_pallet'],
                'width' => 10,
            ],
            [
                'type' => self::DATA_TYPE_NUM,
                'label' => 'Pallet Price Total',
                'dbField' => ['total'],
                'width' => 15,
                'summable' => true,
                'getValue' => function ($row, $defKey, $def, $rowNo) {
                    $qty = Coordinate::stringFromColumnIndex(4) . $rowNo;
                    $priceColName = Coordinate::stringFromColumnIndex(5) . $rowNo;
                    $qty_pkg = Coordinate::stringFromColumnIndex(10) . $rowNo;
                    $price_pallet = Coordinate::stringFromColumnIndex(11) . $rowNo;

                    return "=IF($qty_pkg<>0,  {$price_pallet}*${qty}/{$qty_pkg}, \"\")";
                },
            ],
        ]
     */

    // In case export, we should set linear array
    // In case import, we should set key-value array
    protected array $dbFieldToXlsCol = [];

    protected ?string $docTitle = null;
    protected ?string $fileName = null;
    protected bool $isFileNameTimestamp = true;

    protected int $headerRowNo = 1;
    protected int $totalRowNo = 2;
    protected int $dataRowNo = 3;
    protected string $freezeCell = '';


    public function setDocTitle($title): void
    {
        $this->docTitle = $title;
    }

    public function setFileName($title): void
    {
        $this->fileName = $title;
    }

    public function setIsFileNameTimestamp($isFileNameTimestamp): void
    {
        $this->isFileNameTimestamp = $isFileNameTimestamp;
    }


    /**
     * @param array $dbFieldToXlsCol
     */
    public function setDbFieldToXlsCol(array $dbFieldToXlsCol): void
    {
        $this->dbFieldToXlsCol = $dbFieldToXlsCol;
    }

    /**
     * For export.
     * Public set settings to export excel.
     *
     * @param $docTitle
     * @param $headerRowNo
     * @param $totalRowNo
     * @param $dataRowNo
     * @param array $options
     * @return void
     */
    public function setOptions($docTitle, $headerRowNo, $totalRowNo, $dataRowNo, array $options = []): void
    {
        if ($docTitle) {
            $this->docTitle = $docTitle;
        }
        $this->headerRowNo = $headerRowNo;
        $this->totalRowNo = $totalRowNo;
        $this->dataRowNo = $dataRowNo;

        $this->relPath = $options['relPath'] ?? '/tmp';
        $this->basePath = $options['basePath'] ?? PRIVATE_DATA_PATH;
    }

    /**
     * Create a spreadsheet object with this title.
     * Either use an existing Excel template file or create a new one.
     *
     * @param $docTitle
     * @param array $options [ArrayShape(['creator' => "", 'keyword' => ""])]
     * @return Spreadsheet
     *
     * @throws Exception
     */
    public function createSpreadsheet($docTitle, array $options = []): Spreadsheet
    {
        $mergedOptions = array_merge($options, [
            'creator' => self::CREATOR,
            'keyword' => self::KEYWORD,
        ]);
        $this->spreadsheet = ExcelExportLib::createSpreadsheet($docTitle, $mergedOptions);

        return $this->spreadsheet;
    }

    /**
     * Get value by object/array paths
     *
     * Get property value of object by dot notion variable.
     * e.g. user.name.first_name
     *      user.0.name.first_name
     *
     * @param object|array|Model $obj
     * @param array $paths
     * @return mixed
     */
    protected function getProperty(mixed $obj, array $paths): mixed
    {
        if (is_array($obj)) {
            return array_reduce($paths, function ($nextObj, $p) {
                if (!$nextObj) return null;

                if (is_array($nextObj)) {
                    return $nextObj[$p] ?? null;
                } else {
                    return (is_numeric($p) ? ($nextObj[$p] ?? null) : $nextObj?->$p);
                }
            }, $obj);
        } else {
            return array_reduce($paths, function ($nextObj, $p) {
                if (!$nextObj) return null;
                if (is_array($nextObj)) {
                    return $nextObj[$p] ?? null;
                } else {
                    return (is_numeric($p) ? ($nextObj[$p] ?? null) : $nextObj?->$p);
                }
            }, $obj);
        }
    }

    /**
     * For export, get db value and convert it to excel type
     *
     * @param $dbRow
     * @param mixed $defKey
     * @param array $def
     * @param $rowNo
     * @return false|float|int|mixed|string|null
     */
    protected function getDbValue(&$dbRow, mixed $defKey, array $def, $rowNo = null)
    {
        $value = null;
        if (isset($def['getValue']) && is_callable($def['getValue'])) {
            $value = $def['getValue']($dbRow, $defKey, $def, $rowNo);
        } else {
            $dbFieldArr = is_array($def['dbField']) ? $def['dbField'] : explode('.', $def['dbField']);
            if ($dbFieldArr) {
                $dbValue = $this->getProperty($dbRow, $dbFieldArr);
                $value = $dbValue;
                if ($dbValue) {
                    $colType = $def['type'] ?? self::DATA_TYPE_STRING;
                    switch ($colType) {
                        case ExcelExportLib::DATA_TYPE_INT:
                            $value = Func::safeInt($dbValue);
                            break;
                        case ExcelExportLib::DATA_TYPE_NUM:
                            $value = Func::safeDouble($dbValue);
                            break;
                        case ExcelExportLib::DATA_TYPE_DATE:
                            $value = \PhpOffice\PhpSpreadsheet\Shared\Date::PHPToExcel($dbValue);
                            break;
                        case self::DATA_TYPE_YN:
                            $value = $dbValue ? 'Y' : '';
                            break;
                    }
                }
            }
        }
        return $value;
    }

    /**
     * For export
     *
     * @param $obj
     * @param $rowNo
     * @return array
     */
    protected function fillExcelRow(&$obj, $rowNo = null)
    {
        $row = [];
        foreach ($this->dbFieldToXlsCol as $key => $def) {
            $dbValue = $this->getDbValue($obj, $key, $def, $rowNo);
            $row[$key] = $dbValue;
        }

        return $row;
    }

    /**
     * Export main functions
     *
     * @param Builder|\Illuminate\Database\Eloquent\Builder $builder
     * @param array $inputParams
     * @return string[]
     * @throws Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function export(Builder|\Illuminate\Database\Eloquent\Builder $builder, array $inputParams = [])
    {
        if (!$this->docTitle) {
            $this->docTitle = $inputParams['docTitle'] ?? 'Report';
        }

        if (!$this->fileName) {
            $this->fileName = $inputParams['fileName'] ?? Func::getSafeFilePath($this->docTitle);
        }

        // 1. Create a spreadsheet
        $this->createSpreadsheet($this->docTitle);
        $sheet = ExcelExportLib::getActiveSheet($this->spreadsheet);
        if ($this->isLandscape)
            $sheet->getPageSetup()->setOrientation(PageSetup::ORIENTATION_LANDSCAPE);
        $sheet->getPageSetup()->setFitToWidth(true);

        // 2. Header
        $headerCols = [];
        foreach ($this->dbFieldToXlsCol as $i => $def) {
            $headerCols[] = $def['label'] ?? $def['dbField'];
            $width = $def['width'] ?? 15;
            $sheet->getColumnDimensionByColumn($i + 1)->setAutoSize(false)->setWidth($width);
        }
        $sheet->fromArray($headerCols, NULL, 'A' . $this->headerRowNo);

        // 3. Body
        $r = $this->dataRowNo;

        $builder->chunk(200, function ($items) use (&$r, &$sheet) {
            foreach ($items as $item) {
                $row = $this->fillExcelRow($item, $r);
                $sheet->fromArray(array_values($row), '', 'A' . $r);

                // apply styles
                foreach ($this->dbFieldToXlsCol as $key => $def) {
                    if (isset($def['getBgColor']) && is_callable($def['getBgColor'])) {
                        $bgColor = $def['getBgColor']($item, $key, $def, $r);
                        if ($bgColor) {
                            $sheet->getCell([$key + 1, $r, $key + 1, $r])->getStyle()->getFill()
                                ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                                ->getStartColor()
                                ->setARGB($bgColor);
                        }
                    }
                }

                $r++;
            }
        });

        // 4. Cell formatting & Total columns
        $lastDataRowNo = $r - 1;
        unset($r);
        foreach ($this->dbFieldToXlsCol as $ind => $def) {
            $colNo = $ind + 1;
            $style = $sheet->getStyle([$colNo, $this->dataRowNo, $colNo, $lastDataRowNo]);
            $numberFormat = $style->getNumberFormat();

            $xlsFormatType = $def['xlsFormat'] ?? null;
            $nfStr = $xlsFormatType;
            if ($xlsFormatType) {
                // $numberFormat->setFormatCode($xlsFormatType);
            } else {
                $dataType = $def['type'] ?? null;
                if ($dataType) {
                    if ($dataType == self::DATA_TYPE_INT) {
                        $nfStr = ExcelExportLib::FORMAT_I;
                    } elseif ($dataType == self::DATA_TYPE_NUM) {
                        $nfStr = ExcelExportLib::FORMAT_D;
                    } elseif ($dataType == self::DATA_TYPE_DATE) {
                        $nfStr = ExcelExportLib::FORMAT_DATE_DE;
                    } elseif ($dataType == self::DATA_TYPE_STRING_DIGIT) {
                        $nfStr = '0';
                    }
                }
            }
            if ($nfStr !== null) {
                $numberFormat->setFormatCode($nfStr);
                if ($this->totalRowNo) {
                    $sheet->getStyle([$colNo, $this->totalRowNo, $colNo, $this->totalRowNo])
                        ->getNumberFormat()->setFormatCode($nfStr);
                }
            }

            if ($def['align'] ?? null) {
                $style->getAlignment()->setHorizontal($def['align']);
            }

            if ($this->totalRowNo && ($def['summable'] ?? false)) {
                $colName = Coordinate::stringFromColumnIndex($colNo);
                $sheet->setCellValue([$colNo, $this->totalRowNo], sprintf("=SUBTOTAL(9,%s%s:%s%s)", $colName, $this->totalRowNo + 1, $colName, $lastDataRowNo));
            }
        }

        // 5. Styling
        $headerColsCnt = count($this->dbFieldToXlsCol);
        // Header row
        $sheet->getStyle([1, $this->headerRowNo, $headerColsCnt, $this->headerRowNo])
            ->applyFromArray(array_merge(ExcelExportLib::$styleBold, ExcelExportLib::$borderThick));
        // total row
        if ($this->totalRowNo) {
            $sheet->getStyle([1, $this->totalRowNo, $headerColsCnt, $this->totalRowNo])
                ->applyFromArray(array_merge(ExcelExportLib::$styleBold, ExcelExportLib::$borderThick));
        }
        // Entire data range
        $sheet->getStyle([1, $this->headerRowNo, $headerColsCnt, $lastDataRowNo])
            ->applyFromArray(ExcelExportLib::$borderThick);

        foreach ($this->dbFieldToXlsCol as $ind => $def) {
            if ($def['styleArea'] ?? null) {
                $sheet->getStyle([$ind + 1, $this->headerRowNo, $ind + 1, $lastDataRowNo])
                    ->applyFromArray($def['styleArea']);
            }

            if ($def['borderLeft'] ?? null) {
                $sheet->getStyle([$ind + 1, $this->headerRowNo, $ind + 1, $lastDataRowNo])
                    ->getBorders()->getLeft()->applyFromArray($def['borderLeft']);
            }

            if ($def['borderRight'] ?? null) {
                $sheet->getStyle([$ind + 1, $this->headerRowNo, $ind + 1, $lastDataRowNo])
                    ->getBorders()->getRight()->applyFromArray($def['borderRight']);
            }

            if ($def['hidden'] ?? false) {
                $sheet->getColumnDimensionByColumn($ind + 1)->setVisible(false);
            }
        }

        // Extra rendering
        $this->writeData($sheet, $inputParams);

        // auto filter
        $sheet->setAutoFilter([1, $this->headerRowNo, $headerColsCnt, $lastDataRowNo]);
        if ($this->freezeCell) {
            $sheet->freezePane($this->freezeCell);
        }

        // 6. Saving exported file and return downloadable data array.
        $basePath = Func::pathJoin($this->basePath, $this->relPath);
        FileLib::checkAndCreateDir($basePath);

        $fileName = $this->fileName . ($this->isFileNameTimestamp ? "_" . time() : '') . '.' . $this->extension;
        if ($this->extension == 'xlsx') {
            $writer = new Xlsx($this->spreadsheet);
        } else if ($this->extension == 'csv') {
            $writer = new \PhpOffice\PhpSpreadsheet\Writer\Csv($this->spreadsheet);
        } else if ($this->extension == 'xls') {
            $writer = new Xls($this->spreadsheet);
        } else {
            throw new \Exception('Excel Writer extension is not set.');
        }
        $writer->save($basePath . DS . $fileName);

        return FileLib::getDownloadableFileObject($this->extension, Func::pathJoinUrl($this->relPath, $fileName), true);
    }

    public function writeData(Worksheet $sheet, $data = [])
    {
        // Note: Should be implement in child classes.
    }

    /**
     * @return Spreadsheet
     */
    public function getSpreadsheet(): Spreadsheet
    {
        return $this->spreadsheet;
    }

    /**
     * Read a row data in Xls file.
     *
     * @param Worksheet $sheet
     * @param $rowNo
     * @return array|null
     */
    public static function readRowByNo(Worksheet &$sheet, $rowNo = 0)
    {
        $ind = 0;
        foreach ($sheet->getRowIterator() as $row) {
            // Fetch data
            if ($ind == $rowNo) {
                $cellIterator = $row->getCellIterator();
                $cellIterator->setIterateOnlyExistingCells(false);
                $data = [];
                foreach ($cellIterator as $cell) {
                    $data[] = $cell->getValue();
                }
                return $data;
            }
            $ind++;
        }

        return null;
    }

    /**
     * For import.
     *
     * Get header mapping info while checking the required header cols.
     *
     * Returns array map: [xlsColInd => dbFieldDef array]
     *
     * @param Worksheet $sheet
     * @param array $fieldDefs
     * @param int $headerRowNo
     * @param string $labelCheckMode 'text' | 'colInd' | 'colStr'
     * @return array|null
     */
    public static function getValidHeaderDefs(Worksheet &$sheet, array $fieldDefs, int $headerRowNo = 0, string $labelCheckMode = 'text'): ?array
    {
        $header = self::readRowByNo($sheet, $headerRowNo);
        if (!$header) return null;

        $dbDef = [];
        $requiredLabelsInHeader = [];
        foreach ($header as $ind => $col) {
            $colStr = Coordinate::stringFromColumnIndex($ind);
            foreach ($fieldDefs as $def) {
                if (($labelCheckMode == 'text' && $def['label'] == trim($col))
                    || ($labelCheckMode == 'colInd' && $def['label'] == $ind)
                    || ($labelCheckMode == 'colStr' && $def['label'] == $colStr)
                ) {
                    $dbDef[$ind] = $def;
                    if ($def['required'] ?? false) {
                        $requiredLabelsInHeader[] = $def['label'];
                    }
                    break;
                }
            }
        }
        $validHeader = true;
        foreach ($fieldDefs as $def) {
            if (($def['required'] ?? false) && (
                    ($labelCheckMode == 'text' && !in_array($def['label'], $requiredLabelsInHeader))
                    || ($labelCheckMode == 'colInd' && !in_array($def['label'], $requiredLabelsInHeader))
                    || ($labelCheckMode == 'colStr' && !in_array($def['label'], $requiredLabelsInHeader))
                )
            ) {
                $validHeader = false;
                break;
            }
        }
        if (!$validHeader) {
            return null;
        }

        return $dbDef;
    }


    /**
     * For Import
     *
     * Get db friendly value by db field definition from xls content
     *
     * @param $colValue
     * @param array $def
     * @return int|mixed|string
     */
    public static function parseCellValue($colValue, array $def): mixed
    {
        $dbValue = $colValue;
        if ($colValue) {
            if ($def['type'] === self::DATA_TYPE_INT) {
                $dbValue = Func::safeInt($colValue);
            } else if ($def['type'] === self::DATA_TYPE_NUM) {
                $dbValue = Func::safeDouble($colValue);
            } else if ($def['type'] === self::DATA_TYPE_DATE) {
                $ts = Func::dtStrToTimeYmd($colValue, $def['format'] ?? DATE_FORMAT_YMD);
                if ($ts)
                    $dbValue = Func::dtDbDatetimeStr($ts, DATE_FORMAT_YMD);
            } else if ($def['type'] === self::DATA_TYPE_DATETIME) {
                $ts = Func::dtStrToTimeYmd($colValue, $def['format'] ?? DATE_FORMAT_YMD_HIS);
                if ($ts)
                    $dbValue = Func::dtDbDatetimeStr($ts, DATE_FORMAT_YMD_HIS);
            }
        }

        return $dbValue;
    }


    /**
     * Get Worksheet from csv or xls or xlsx file.
     *
     * NOTE: if csv, we may need to pass delimiter in options parameter.
     *
     *
     * @param string $filePath
     * @param array $options
     *
     * @return Worksheet
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
    public static function getWorksheet(string $filePath, array $options = []): Worksheet
    {
        $fileExt = pathinfo($filePath, PATHINFO_EXTENSION);

        if ($fileExt == 'csv') {
            $csv = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
            $csv->setDelimiter($options['delimiter'] ?? ';');

            // This may be problem on server? Not sure!!!
            // $spreadsheet = $csv->loadSpreadsheetFromString($file->getStream()->getContents());
            // We directly load CSV from tmp file path.

            // to do
            // Phpspreadsheet checks this type
            // $tmp = mime_content_type($file->file);
        } else {
            $csv = $fileExt == 'xls' ? new \PhpOffice\PhpSpreadsheet\Reader\Xls() : new \PhpOffice\PhpSpreadsheet\Reader\Xlsx();
        }

        $spreadsheet = $csv->load($filePath);

        return $spreadsheet->getActiveSheet();
    }

    public function dbField2Str(&$dbField)
    {
        return is_string($dbField) || is_numeric($dbField) ? $dbField : implode('.', $dbField);
    }

    /**
     * e.g. returns XLS columns: 'A', 'B', 'C', etc.
     *
     * @param $dbField
     * @return string
     */
    public function findXlsColumn($dbField)
    {
        if (!$this->dbFieldToXlsCol) return '';

        // Coordinate::stringFromColumnIndex($this->baseColInd);
        $str = $this->dbField2Str($dbField);
        foreach ($this->dbFieldToXlsCol as $ind => &$x) {
            $comparedStr = $this->dbField2Str($x['dbField']);
            if ($comparedStr === $str) {
                return Coordinate::stringFromColumnIndex($ind + 1);
            }
        }
        return '';
    }
}
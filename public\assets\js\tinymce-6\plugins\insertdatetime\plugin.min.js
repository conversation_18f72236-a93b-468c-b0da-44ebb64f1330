/**
 * TinyMCE version 6.1.0 (2022-06-29)
 */
!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.PluginManager");const t=e=>t=>t.options.get(e),r=t("insertdatetime_dateformat"),a=t("insertdatetime_timeformat"),n=t("insertdatetime_formats"),s=t("insertdatetime_element"),i="Sun Mon Tue Wed Thu Fri Sat Sun".split(" "),o="Sunday Monday Tuesday Wednesday Thursday Friday Saturday Sunday".split(" "),l="Jan Feb Mar Apr May Jun Jul Aug Sep Oct Nov Dec".split(" "),m="January February March April May June July August September October November December".split(" "),c=(e,t)=>{if((e=""+e).length<t)for(let r=0;r<t-e.length;r++)e="0"+e;return e},d=(e,t,r=new Date)=>(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=t.replace("%D","%m/%d/%Y")).replace("%r","%I:%M:%S %p")).replace("%Y",""+r.getFullYear())).replace("%y",""+r.getYear())).replace("%m",c(r.getMonth()+1,2))).replace("%d",c(r.getDate(),2))).replace("%H",""+c(r.getHours(),2))).replace("%M",""+c(r.getMinutes(),2))).replace("%S",""+c(r.getSeconds(),2))).replace("%I",""+((r.getHours()+11)%12+1))).replace("%p",r.getHours()<12?"AM":"PM")).replace("%B",""+e.translate(m[r.getMonth()]))).replace("%b",""+e.translate(l[r.getMonth()]))).replace("%A",""+e.translate(o[r.getDay()]))).replace("%a",""+e.translate(i[r.getDay()]))).replace("%%","%"),u=(e,t)=>{if(s(e)){const r=d(e,t);let a;a=/%[HMSIp]/.test(t)?d(e,"%Y-%m-%dT%H:%M"):d(e,"%Y-%m-%d");const n=e.dom.getParent(e.selection.getStart(),"time");n?((e,t,r,a)=>{const n=e.dom.create("time",{datetime:r},a);t.parentNode.insertBefore(n,t),e.dom.remove(t),e.selection.select(n,!0),e.selection.collapse(!1)})(e,n,a,r):e.insertContent('<time datetime="'+a+'">'+r+"</time>")}else e.insertContent(d(e,t))};var p=tinymce.util.Tools.resolve("tinymce.util.Tools");e.add("insertdatetime",(e=>{(e=>{const t=e.options.register;t("insertdatetime_dateformat",{processor:"string",default:e.translate("%Y-%m-%d")}),t("insertdatetime_timeformat",{processor:"string",default:e.translate("%H:%M:%S")}),t("insertdatetime_formats",{processor:"string[]",default:["%H:%M:%S","%Y-%m-%d","%I:%M:%S %p","%D"]}),t("insertdatetime_element",{processor:"boolean",default:!1})})(e),(e=>{e.addCommand("mceInsertDate",((t,a)=>{u(e,null!=a?a:r(e))})),e.addCommand("mceInsertTime",((t,r)=>{u(e,null!=r?r:a(e))}))})(e),(e=>{const t=n(e),r=(e=>{let t=e;return{get:()=>t,set:e=>{t=e}}})((e=>{const t=n(e);return t.length>0?t[0]:a(e)})(e)),s=t=>e.execCommand("mceInsertDate",!1,t);e.ui.registry.addSplitButton("insertdatetime",{icon:"insert-time",tooltip:"Insert date/time",select:e=>e===r.get(),fetch:r=>{r(p.map(t,(t=>({type:"choiceitem",text:d(e,t),value:t}))))},onAction:e=>{s(r.get())},onItemAction:(e,t)=>{r.set(t),s(t)}});const i=e=>()=>{r.set(e),s(e)};e.ui.registry.addNestedMenuItem("insertdatetime",{icon:"insert-time",text:"Date/time",getSubmenuItems:()=>p.map(t,(t=>({type:"menuitem",text:d(e,t),onAction:i(t)})))})})(e)}))}();
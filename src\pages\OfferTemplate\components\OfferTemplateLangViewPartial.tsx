import React from "react";
import { <PERSON><PERSON>, <PERSON>, Col, Divider, Row, Space, Typography, Image } from "antd";
import { FileOutlined, FilePdfOutlined, FileTextOutlined, FileWordOutlined } from "@ant-design/icons";
import styles from "./OfferTemplateLangViewPartial.less";
import { sEllipsed } from "@/util";

const { Title, Text } = Typography;

export type OfferTemplateLangViewPartialProps = {
  values?: any;
};

const OfferTemplateLangViewPartial: React.FC<OfferTemplateLangViewPartialProps> = (props) => {
  const { values } = props;

  // Extract language data
  const deData = values?.DE || {};
  const enData = values?.EN || {};
  const files = values?.files || [];

  return (
    <>
      {/* Subject Row */}
      <Row gutter={24}>
        <Col span={12}>
          <div className={styles.section}>
            <Title level={5}>Subject DE</Title>
            <div className={styles.contentBox}>
              <Text>{deData.subject || "-"}</Text>
            </div>
          </div>
        </Col>
        <Col span={12}>
          <div className={styles.section}>
            <Title level={5}>Subject EN</Title>
            <div className={styles.contentBox}>
              <Text>{enData.subject || "-"}</Text>
            </div>
          </div>
        </Col>
      </Row>

      {/* Header/Salutation Row */}
      <Row gutter={24}>
        <Col span={12}>
          <div className={styles.section}>
            <Title level={5}>Salutation DE</Title>
            <div className={styles.contentBox}>
              {deData.header ? <div dangerouslySetInnerHTML={{ __html: deData.header }} /> : <Text type="secondary">No content</Text>}
            </div>
          </div>
        </Col>
        <Col span={12}>
          <div className={styles.section}>
            <Title level={5}>Salutation EN</Title>
            <div className={styles.contentBox}>
              {enData.header ? <div dangerouslySetInnerHTML={{ __html: enData.header }} /> : <Text type="secondary">No content</Text>}
            </div>
          </div>
        </Col>
      </Row>

      {/* Body Row */}
      <Row gutter={24}>
        <Col span={12}>
          <div className={styles.section}>
            <Title level={5}>Body DE</Title>
            <div className={styles.contentBox} style={{ minHeight: 300 }}>
              {deData.body ? <div dangerouslySetInnerHTML={{ __html: deData.body }} /> : <Text type="secondary">No content</Text>}
            </div>
          </div>
        </Col>
        <Col span={12}>
          <div className={styles.section}>
            <Title level={5}>Body EN</Title>
            <div className={styles.contentBox} style={{ minHeight: 300 }}>
              {enData.body ? <div dangerouslySetInnerHTML={{ __html: enData.body }} /> : <Text type="secondary">No content</Text>}
            </div>
          </div>
        </Col>
      </Row>

      {/* Files Section - Positioned exactly like in the form */}
      <div className={styles.section}>
        <Title level={5}>Files</Title>
        <Row gutter={12}>
          {files.length > 0 ? (
            files.map((a: any, index: number) => {
              const fileUrl = `${a.url}`;

              const eleStyles = {
                style: {
                  fontSize: 48,
                  color: "grey",
                },
              };
              let ele = null;
              if (a.type == "application/pdf") {
                ele = <FilePdfOutlined {...eleStyles} />;
              } else if (a.clean_file_name?.endsWith(".docx") || a.clean_file_name?.endsWith(".doc")) {
                ele = <FileWordOutlined {...eleStyles} />;
              } else if (a.clean_file_name?.endsWith(".txt")) {
                ele = <FileTextOutlined {...eleStyles} />;
              } else {
                ele = <FileOutlined {...eleStyles} />;
              }

              return (
                <Col key={a.id} span={12}>
                  <Space direction="horizontal" size={8} style={{ width: "100%" }}>
                    {a.type?.includes("image") ? (
                      <Avatar shape="square" size={64} src={<Image src={fileUrl} />} style={{ border: "1px solid #efefef", borderRadius: 8 }} />
                    ) : (
                      <div
                        style={{
                          border: "1px solid #efefef",
                          borderRadius: 8,
                          textAlign: "center",
                          width: 64,
                          height: 64,
                          paddingTop: 8,
                        }}
                      >
                        {ele}
                      </div>
                    )}

                    <Typography.Link href={fileUrl} ellipsis target="_blank" className="text-sm" style={{ textAlign: "center" }}>
                      {sEllipsed(a.clean_file_name, 25, 5)}
                    </Typography.Link>
                  </Space>
                </Col>
              );
            })
          ) : (
            <Text type="secondary">No files attached</Text>
          )}
        </Row>
      </div>

      {/* Footer Row */}
      <Row gutter={24}>
        <Col span={12}>
          <div className={styles.section}>
            <Title level={5}>Footer DE</Title>
            <div className={styles.contentBox}>
              {deData.footer ? <div dangerouslySetInnerHTML={{ __html: deData.footer }} /> : <Text type="secondary">No content</Text>}
            </div>
          </div>
        </Col>
        <Col span={12}>
          <div className={styles.section}>
            <Title level={5}>Footer EN</Title>
            <div className={styles.contentBox}>
              {enData.footer ? <div dangerouslySetInnerHTML={{ __html: enData.footer }} /> : <Text type="secondary">No content</Text>}
            </div>
          </div>
        </Col>
      </Row>
    </>
  );
};

export default OfferTemplateLangViewPartial;

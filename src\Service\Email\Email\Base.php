<?php

declare(strict_types=1);

namespace App\Service\Email\Email;

use App\Lib\Func;
use App\Models\Email\EmailServer;
use App\Service\BaseService;
use App\Repository\Email\EmailRepository;
use App\Service\Email\EmailAccount\EmailAccountService;
use PHPMailer\PHPMailer\PHPMailer;
use Slim\Container;

abstract class Base extends BaseService
{
    private const REDIS_KEY = 'Email:%s';
    protected EmailRepository $emailRepository;

    public function __construct(Container $container)
    {
        $this->emailRepository = $container->get(EmailRepository::class);
    }

    public function getEmailRepository()
    {
        return $this->emailRepository;
    }

    public function getPhpMailerInstance(EmailServer $server, $params=[]) {
        $mail = new PHPMailer(true);

        //Server settings
        $mail->isSMTP();                                            //Send using SMTP
        $mail->Host = $server->smtp_host;                           //Set the SMTP server to send through
        $mail->SMTPAuth = true;                                   //Enable SMTP authentication
        $mail->Username = $server->smtp_user;                     //SMTP username
        $mail->Password = (string)Func::mcrypt('decrypt', $server->smtp_password, EmailAccountService::SEC_KEY, EmailAccountService::SEC_IV);                               //SMTP password
        $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;            //Enable implicit TLS encryption
        $mail->Port = $server->smtp_port;                           //TCP port to connect to; use 587 if you have set `SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS`
        $mail->CharSet = 'UTF-8';

        if (Func::isDev()) {
            $mail->port = 587;
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
            $mail->SMTPOptions = array(
                'ssl' => array(
                    'verify_peer' => false,
                    'verify_peer_name' => false,
                    'allow_self_signed' => true,
                )
            );
        }

        $mail->isHTML(true);                                  //Set email format to HTML

        return $mail;
    }

    public static function getEmailAttachmentPathOrSubUrl($isSubUrl = false): string
    {
        $subPath = DS . 'attachments';

        return $isSubUrl ? Func::pathToUrl($subPath) : PRIVATE_DATA_PATH . $subPath;
    }
}


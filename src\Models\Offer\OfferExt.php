<?php

namespace App\Models\Offer;

use App\Models\BaseModel;

/**
 * @property integer $id
 * @property string $offer_no
 * @property string $notes
 * @property string $notes_ek_vk
 * @property integer $status
 * @property boolean $is_brand
 * @property boolean $is_b_group_appr
 * @property boolean $details
 * @property integer $prio
 * @property string $created_on
 * @property integer $created_by
 * @property string $updated_on
 * @property integer $updated_by
 *
 * @property OfferBlog $blog
 * @property OfferNewsletter[] $newsletters
 */
class OfferExt extends BaseModel
{
    public const STATUS_CLOSED = 0;
    public const STATUS_ACTIVE = 1;
    public const STATUS_IN_PROGRESS = 2;

    public $timestamps = true;

    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'offer_ext';

    /**
     * @var array
     */
    protected $fillable = ['offer_no', 'notes', 'notes_ek_vk', 'status', 'prio', 'is_brand', 'is_b_group_appr', 'details', 'created_on', 'created_by', 'updated_on', 'updated_by'];

    protected $casts = [
        'details' => 'json',
    ];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function blog()
    {
        return $this->hasOne(OfferBlog::class, 'offer_no', 'offer_no');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function newsletters()
    {
        return $this->hasMany(OfferNewsletter::class, 'offer_no', 'offer_no');
    }
}

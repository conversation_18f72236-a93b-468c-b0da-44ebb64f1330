<?php

namespace App\ModelsOrg;

use Illuminate\Database\Eloquent\Builder;


/**
 * * @method static create(array $input)
 * * @method static updateOrCreate(array $attributes, array $values)
 * * @method static findOrFail($id)
 * * @method static firstOrNew(array $attributes, array $values=[])
 * * @method static firstOrCreate(array $attributes, array $values=[])
 * * @method static Builder from(string $string)
 * * @method static insert(array $items)
 */
class BaseModel extends \App\Models\BaseModel
{
    protected $connection = ORG_DB;
}
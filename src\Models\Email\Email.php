<?php

namespace App\Models\Email;

use App\Lib\Func;
use App\Models\BaseModel;
use App\Models\Supplier\Supplier;
use PhpImap\IncomingMail;

/**
 * @property integer $id
 * @property integer $email_account_id
 * @property string $message_id
 * @property integer $mail_id
 * @property string $box
 * @property string $sender
 * @property string $sender_host
 * @property string $sender_name
 * @property string $receiver
 * @property string $subject
 * @property string $date
 * @property string $date_str
 * @property string $text_html
 * @property string $text_plain
 * @property boolean $has_attachments
 * @property boolean $is_seen
 * @property boolean $is_answered
 * @property boolean $is_recent
 * @property boolean $is_flagged
 * @property boolean $is_deleted
 * @property boolean $is_draft
 * @property string $from_host
 * @property string $from_name
 * @property string $to
 * @property string $reply_to
 * @property string $cc
 * @property string $bcc
 * @property integer $is_hidden
 * @property string $created_on
 * @property int $created_by
 * @property integer $supplier_id
 * @property integer $customer_id
 * @property integer $email_template_id
 * @property string $offer_no
 * @property string $status
 * @property array $attachments
 * @property string $mime_version
 * @property string $content_type
 *
 * @property string $text   // calculated one
 * @property EmailAccount $emailAccount
 * @property Supplier $supplier
 * @property EmailTemplate $emailTemplate
 */
class Email extends BaseModel
{
    public const UPDATED_AT = null;

    public $timestamps = [
        self::CREATED_AT
    ];

    public $appends = ['text'];

    protected $casts = [
        'to' => 'array',
        'cc' => 'array',
        'reply_to' => 'array',
        'bcc' => 'array',
        'attachments' => 'array',
    ];

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'email';

    /**
     * The "type" of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'integer';

    /**
     * @var array
     */
    protected $fillable = ['email_account_id', 'message_id', 'mail_id', 'box', 'sender', 'sender_host', 'sender_name', 'receiver', 'subject', 'date', 'date_str', 'text_html', 'text_plain', 'has_attachments', 'is_seen', 'is_answered', 'is_recent', 'is_flagged', 'is_deleted', 'is_draft', 'from_host', 'from_name', 'to', 'reply_to', 'cc', 'bcc', 'created_on'
        , 'status'
        , 'supplier_id'
        , 'customer_id'
        , 'email_template_id'
        , 'offer_no'
        , 'attachments'
        , 'content_type'
        , 'mime_version'
        , 'is_hidden'
        , 'created_by'
    ];

    public function getTextAttribute()
    {
        return ($this->attributes['text_html'] ?? null) ? $this->attributes['text_html'] : $this->attributes['text_plain'] ?? null;
    }

    public function setTextPlainAttribute($value)
    {
        $encoding = mb_detect_encoding($value, ['UTF-8', 'ISO-8859-1']); // Add more encodings to support here
        if ($encoding !== 'UTF-8') {
            $this->attributes['text_plain'] = mb_convert_encoding($value, 'UTF-8', $encoding);
        } else {
            $this->attributes['text_plain'] = $value;
        }
    }

    public function setTextHtmlAttribute($value)
    {
        $encoding = mb_detect_encoding($value, ['UTF-8', 'ISO-8859-1']); // Add more encodings to support here
        if ($encoding !== 'UTF-8') {
            $this->attributes['text_html'] = mb_convert_encoding($value, 'UTF-8', $encoding);
        } else {
            $this->attributes['text_html'] = $value;
        }
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function emailAccount()
    {
        return $this->belongsTo('App\Models\Email\EmailAccount');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function receivers()
    {
        return $this->hasMany('App\Models\Email\EmailReceiver', 'email_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function supplier()
    {
        return $this->belongsTo('App\Models\Supplier\Supplier', 'supplier_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function customer()
    {
        return $this->belongsTo('App\Models\Customer\Customer', 'customer_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function emailTemplate()
    {
        return $this->belongsTo('App\Models\Email\EmailTemplate', 'email_template_id');
    }

    public static function bindData(Email &$model, IncomingMail &$mail, $email_account_id = null)
    {
        $model->email_account_id = $email_account_id;

        $model->message_id = $mail->messageId;
        $model->mail_id = $mail->id;
        $model->subject = $mail->subject;
        $model->text_plain = $mail->textPlain;
        $model->text_html = $mail->textHtml;
        $model->box = $mail->mailboxFolder;
        $model->sender = $mail->senderAddress;
        $model->sender_name = $mail->senderName;
        $model->sender_host = $mail->senderHost;
        $model->from_host = $mail->fromHost;
        $model->from_name = $mail->fromName;
        $model->receiver = $mail->toString;
        $model->to = $mail->to;
        $model->cc = $mail->cc;
        $model->bcc = $mail->bcc;
        $model->reply_to = $mail->replyTo;
        $model->has_attachments = $mail->hasAttachments();
        $model->is_answered = $mail->isAnswered;
        $model->is_deleted = $mail->isDeleted;
        $model->is_seen = $mail->isSeen;
        $model->is_draft = $mail->isDraft;
        $model->is_flagged = $mail->isFlagged;
        $model->is_recent = $mail->isRecent;
        $model->mime_version = $mail->mimeVersion;
        $model->content_type = $mail->contentType;
        $model->date = Func::dtDbDatetimeStr(strtotime($mail->date));
        $model->date_str = $mail->date;
    }

    /**
     * Get a model from IncomingMail object
     *
     * @param IncomingMail $mail
     * @param $email_account_id
     * @return Email
     */
    public static function findOrNewByMail(IncomingMail $mail, $email_account_id = null): Email
    {
        $where = [
            'email_account_id' => $email_account_id,
            'message_id' => $mail->messageId,
            'mail_id' => $mail->id,
        ];

        /** @var Email $model */
        $model = Email::firstOrNew($where);

        self::bindData($model, $mail, $email_account_id);

        return $model;
    }

    /**
     * M365
     *
     * @param Email $model
     * @param \Webklex\PHPIMAP\Message $mail
     * @param $email_account_id
     * @return void
     */
    public static function bindData2(Email &$model, \Webklex\PHPIMAP\Message &$mail)
    {
        $model->message_id = $mail->getMessageId()->toString();
        $model->mail_id = $mail->getUid();

        $model->subject = $mail->getSubject()->toString();
        $model->text_plain = $mail->getTextBody();
        $model->text_html = $mail->getHTMLBody();

        // sender
        $senderAttr = $mail->sender->first();
        $model->sender = $senderAttr->mail;
        $model->sender_host = $senderAttr->host;
        $model->sender_name = $senderAttr->personal;

        // to
        $toAttr = $mail->getTo()->toArray();
        $model->receiver = $toAttr[0]->full;
        $model->to = array_reduce($toAttr, function($prev, $x) {
            $prev[$x->mail] = $x->personal;
            return $prev;
        }, []);

        // from
        $fromAttr = $mail->getFrom()->first();
        $model->from_host = $fromAttr->host;
        $model->from_name = $fromAttr->personal;

        // cc
        $arr = $mail->getCc()?->toArray() ?? [];
        $model->cc = array_reduce($arr, function($prev, $x) {
            $prev[$x->mail] = $x->personal;
            return $prev;
        }, []);

        // bcc
        $arr = $mail->getCc()?->toArray() ?? [];
        $model->bcc = array_reduce($arr, function($prev, $x) {
            $prev[$x->mail] = $x->personal;
            return $prev;
        }, []);

        // replyTo
        $arr = $mail->getReplyTo()?->toArray() ?? [];
        $model->bcc = array_reduce($arr, function($prev, $x) {
            $prev[$x->mail] = $x->personal;
            return $prev;
        }, []);
        $model->has_attachments = $mail->hasAttachments();

        // $model->is_answered = $mail->isAnswered;
        // $model->is_deleted = $mail->isDeleted;
        // $model->is_seen = $mail->isSeen;
        // $model->is_draft = $mail->isDraft;
        // $model->is_flagged = $mail->isFlagged;
        // $model->is_recent = $mail->isRecent;
        // $model->mime_version = $mail->getM;
        $model->content_type = $mail->get('content_type')->toString();
        $model->date = $mail->date;
        // $model->date_str = $mail->date;
    }

    /**
     * M365
     * Get a model from IncomingMail object
     *
     * @param \Webklex\PHPIMAP\Message $mail
     * @param $email_account_id
     * @return Email
     */
    public static function findOrNewByMail2(\Webklex\PHPIMAP\Message $mail, $email_account_id = null, $params = []): Email
    {
        $where = [
            'email_account_id' => $email_account_id,
            'message_id' => $mail->getMessageId(),
            'mail_id' => $mail->getUid(),
        ];

        /** @var Email $model */
        $model = Email::firstOrNew($where, $params);

        self::bindData2($model, $mail);

        return $model;
    }
}

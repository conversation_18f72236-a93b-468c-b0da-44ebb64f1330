<?php

declare(strict_types=1);

namespace App\Exception;

use Throwable;

abstract class Base extends \Exception
{
    public static function raiseDBSaveError(Throwable $previous = null)
    {
        throw new \Exception('Failed to save data.', 500, $previous);
    }

    public static function raiseInternalServerError($message = null, $code = 500, Throwable $previous = null)
    {
        throw new \Exception($message ?? 'Internal server error!', $code, $previous);
    }

    public static function raiseInvalidRequest($message = null, $code = 400, Throwable $previous = null)
    {
        throw new \Exception($message ?? 'Invalid request!', $code, $previous);
    }

    /**
     * @throws \Exception
     */
    public static function raiseNotFound($message = null, $code = 404, Throwable $previous = null)
    {
        throw new \Exception($message ? $message . ' not found!' : 'Not found!', $code, $previous);
    }

    public static function raiseUnauthorized($message = null, $code = 401, Throwable $previous = null)
    {
        throw new \Exception($message ? $message : 'Forbidden: you are not authorized.', $code, $previous);
    }

    public static function raiseForbidden($message = null, $code = 403, Throwable $previous = null)
    {
        throw new \Exception($message ? $message : 'Forbidden: you don\'t have a permission.', $code, $previous);
    }
}

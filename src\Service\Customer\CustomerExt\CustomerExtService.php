<?php

declare(strict_types=1);

namespace App\Service\Customer\CustomerExt;

use App\Models\Customer\CustomerExt;

final class CustomerExtService extends Base
{
    public function create(array $input): CustomerExt
    {
        return CustomerExt::create($input);
    }

    public function update($id, $input): CustomerExt
    {
        $row = CustomerExt::findOrFail($id);
        $row->update($input);
        return $row;
    }

    public function updateByCustomerId($id, $input): CustomerExt
    {
        /** @var CustomerExt $row */
        $row = CustomerExt::query()->firstOrNew(['customer_id' => $id], $input);
        $row->fill($input);
        $row->save();
        return $row;
    }

    public function getOne(int $id, array $params=[]): CustomerExt
    {
        return $this->customerExtRepository->getQueryBuilder()->where('id', $id)->first();
    }

    public function getCustomerExtsByPage(
        int $page,
        int $perPage,
        ?array $params
    ): array
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($perPage < 1) {
            $perPage = self::DEFAULT_PER_PAGE_PAGINATION;
        }

        return $this->customerExtRepository->getCustomerExtsByPage(
            $page,
            $perPage,
            $params
        );
    }

    public function delete($ids): void
    {
        $arr = is_array($ids) ? $ids : explode(',', $ids);
        $this->customerExtRepository->getQueryBuilder()->whereIn('id', $arr)->delete();
    }
}

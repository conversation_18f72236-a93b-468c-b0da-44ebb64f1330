<?php

declare(strict_types=1);

namespace App\Repository\Sys;

use App\Lib\Func;
use App\Models\Sys\SysTextModule;
use App\Repository\BaseRepositoryORM;
use Illuminate\Database\Eloquent\Builder;
use Monolog\Logger;

final class SysTextModuleRepository extends BaseRepositoryORM
{
    private function getQuerySysTextModulesByPage($params = []): Builder
    {
        $qb = $this->getQueryBuilder();

        if (!empty($params)) {
            if ($params['numberExact'] ?? '') {
                $qb->where('number', $params['numberExact'] ?? '');
            } else if ($params['number'] ?? '') {
                $qb->where('number', 'LIKE', '%'.($params['number'] ?? '') . '%');
            }

            if ($params['numbers'] ?? null) {
                $qb->whereIn('number', Func::csvToArr($params['numbers']));
            }

            if ($params['text'] ?? '') {
                $qb->where('text', 'LIKE',"%{$params['text']}%");
            }

            if ($keyWord = $params['keyWord'] ?? '') {
                $qb->where(function (Builder $builder) use (&$keyWord) {
                    $builder->where('number', 'like', $keyWord . "%")
                        ->orWhere('text', 'LIKE',"%{$keyWord}%");
                });
            }

            $qb = $this->applyOrderBy($qb, $params);
        }

        return $qb;
    }

    /**
     * Get lists by pagination.
     *
     * @param int $page
     * @param int $perPage
     * @param array|null $params
     * @return array
     */
    public function getSysTextModulesByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        $query = $this->getQuerySysTextModulesByPage($params);
        $total = $this->getCountByQuery($query);

        $result = $this->getResultsWithPagination(
            $query,
            $page,
            $perPage,
            $total
        );

        return $result;
    }

    public function getQueryBuilder(): Builder
    {
        return SysTextModule::query();
    }
}
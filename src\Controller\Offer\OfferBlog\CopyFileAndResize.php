<?php

declare(strict_types=1);

namespace App\Controller\Offer\OfferBlog;

use App\Exception\BaseException;
use Slim\Http\Request;
use Slim\Http\Response;

final class CopyFileAndResize extends Base
{
    public function __invoke(Request $request, Response $response): Response
    {
        $input = $request->getParams();

        $offer_no = $input['offer_no'] ?? null;
        if (!$offer_no) {
            BaseException::raiseInvalidRequest('Offer No is required!');
        }

        $file_id = $input['file_id'] ?? null;
        if (!$file_id) {
            BaseException::raiseInvalidRequest('File ID is required!');
        }

        $row = $this->offerBlogService->copyFileAndResize($input, $file_id);

        return $this->jsonResponse($response, 'success', $row, 201);
    }
}

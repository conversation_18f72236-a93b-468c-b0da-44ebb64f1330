<?php

declare(strict_types=1);

namespace App\Repository\Offer;

use App\Lib\Func;
use App\Models\Offer\OfferExt;
use App\Repository\BaseRepositoryORM;
use Illuminate\Database\Eloquent\Builder;

final class OfferExtRepository extends BaseRepositoryORM
{
    public function getQueryOfferExtsByPage($params = []): Builder
    {
        $qb = $this->getQueryBuilder();

        if (!empty($params)) {
            if ($params['offer_no'] ?? null) {
                $qb->where('offer_no', $params['offer_no']);
            }


            if (Func::keyExistsInWithParam($params, 'blogCnt')) {
                $qb->withCount('blog');
            }
            if (Func::keyExistsInWithParam($params, 'newsletterCnt')) {
                $qb->withCount('newsletters');
            }

            $qb = $this->applyOrderBy($qb, $params);
        }

        return $qb;
    }

    /**
     * Get lists by pagination.
     *
     * @param int $page
     * @param int $perPage
     * @param array|null $params
     * @return array
     */
    public function getOfferExtsByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        $query = $this->getQueryOfferExtsByPage($params);
        $total = $this->getCountByQuery($query);

        $result = $this->getResultsWithPagination(
            $query,
            $page,
            $perPage,
            $total
        );

        return $result;
    }

    public function getQueryBuilder(): Builder
    {
        return OfferExt::query();
    }
}
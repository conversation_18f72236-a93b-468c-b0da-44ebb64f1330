<?php

namespace App\Models;

use App\Lib\Func;
use App\Models\Casts\Csv;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Events\Dispatcher;

/**
 * * @method static create(array $input)
 * * @method static updateOrCreate(array $attributes, array $values)
 * * @method static findOrFail($id)
 * * @method static firstOrNew(array $attributes, array $values=[])
 * * @method static firstOrCreate(array $attributes, array $values=[])
 * * @method static Builder from(string $string)
 * * @method static insert(array $items)
 */
class BaseModel extends Model
{
    protected $connection = 'default';

    const CREATED_AT = 'created_on';
    const UPDATED_AT = 'updated_on';

    protected $hidden = array('pivot');

    public $timestamps = false;

    protected static function boot()
    {
        parent::boot();

        // NOTE: This is not done automatically by some reason.
        // So we manually create an instance of event here. 2022-11-25
        if (!isset(static::$dispatcher))
            static::setEventDispatcher(new Dispatcher());

        /*Relation::enforceMorphMap([
            'IBO' => Ibo::class,
            'OrderItem' => MagOrderItem::class,
            'StockStb' => StockStable::class,
        ]);*/
    }

    protected static function booted()
    {
        parent::booted();

        // Register global model events
        static::creating(function(Model $model) {
            if (in_array('updated_by', $model->getFillable())) {
                $model->setAttribute('updated_by', Func::getSesUserId());
            }
            if (in_array('created_by', $model->getFillable())) {
                $model->setAttribute('created_by', Func::getSesUserId());
            }
        });

        static::updating(function(Model $model) {
            if (in_array('updated_by', $model->getFillable())) {
                $model->setAttribute('updated_by', Func::getSesUserId());
            }
        });

        static::updated(function(Model $model) {
        });
    }

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }

    public static function findOrNew($id, $attributes = [])
    {
        $obj = static::find($id);
        return $obj ?: new static($attributes);
    }

    public static function getBoundData(&$item, $applyReverseCast = false): array
    {
        $tmpObj = new static($item);
        $attributes = $tmpObj->getFillable();

        $data = [];
        foreach ($attributes as $key) {
            $data[$key] = $tmpObj->getAttributeValue($key);
        }

        // specific case in case CASTing fields are string type.
        if ($applyReverseCast) {
            foreach ($tmpObj->casts as $key => $type) {
                if ($type == 'array' || $type == 'json') {
                    $data[$key] = $data[$key] ? json_encode($data[$key]) : null;
                } else if ($type == Csv::class) {
                    $data[$key] = $data[$key] ? implode(',', $data[$key]) : null;
                }
            }
        }
        return $data;
    }

    /**
     * Useful for filtering existed attributes only for upsert/insert.
     *
     * @param $item
     * @param $applyReverseCast
     * @return array
     */
    public static function getBoundDataByExistedAttributes(&$item, $applyReverseCast = false): array
    {
        $tmpObj = new static($item);
        $attributes = $tmpObj->getFillable();

        // We filter by existed keys.
        $existedKeys = array_keys($item);

        $data = [];
        foreach ($attributes as $key) {
            if (in_array($key, $existedKeys)) {
                $data[$key] = $tmpObj->getAttributeValue($key);
            }
        }

        // specific case in case CASTing fields are string type.
        if ($applyReverseCast) {
            foreach ($tmpObj->casts as $key => $type) {
                if (key_exists($key, $data)) {
                    if ($type == 'array' || $type == 'json') {
                        $data[$key] = $data[$key] ? json_encode($data[$key]) : null;
                    }
                }
            }
        }
        return $data;
    }

    public static function getDummyQuery(): \Illuminate\Database\Query\Builder {
        return Func::getDb()->query()->fromRaw("dual");
    }
}
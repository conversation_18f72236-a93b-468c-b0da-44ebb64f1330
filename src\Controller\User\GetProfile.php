<?php

declare(strict_types=1);

namespace App\Controller\User;

use App\Exception\User;
use Slim\Http\Request;
use Slim\Http\Response;

final class GetProfile extends Base
{
    public function __invoke(Request $request, Response $response, array $args): Response
    {
        $address = $args['address'] ?? null;

        // validation
        if (!$address) {
            throw new User('User not found', 404);
        }

        $with = explode(',', $request->getParam('with', ''));
        $address = strtolower($address);

        $user = $this->getFindUserService()->getOneByAddress($address, 1);
        if (!$user['nonce']) {
            $nonce = $this->getFindUserService()->getUserRepository()->updateOneTimeNonce($user['user_id']);
            if ($nonce) {
                $user['nonce'] = $nonce;
            }
        }

        return $this->jsonResponse($response, 'success', $user, 200);
    }
}

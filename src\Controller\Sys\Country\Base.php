<?php

declare(strict_types=1);

namespace App\Controller\Sys\Country;

use App\Controller\BaseController;
use App\Service\Sys\Country\CountryService;
use Respect\Validation\Validator as v;
use Slim\Container;

abstract class Base extends BaseController
{
    public CountryService $countryService;

    public function __construct(Container $container)
    {
        parent::__construct($container);
        $this->countryService = $container->get(CountryService::class);
    }

	public function validate(array $input): bool {
        $validator = v::key('name', v::stringType()->length(1, 30))
            ->key('value', v::numeric()->addRule(v::min(0))->addRule(v::max(100)));

        if (!$validator->validate($input)) {
            \App\Exception\Base::raiseInvalidRequest();
        }
        return true;
    }
}

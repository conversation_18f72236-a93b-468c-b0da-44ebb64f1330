<?php

declare(strict_types=1);

namespace App\Service\File;

use App\Lib\FileLib;
use App\Service\BaseService;
use Intervention\Image\ImageManager;
use League\Flysystem\Filesystem;
use League\Flysystem\UnixVisibility\PortableVisibilityConverter;
use League\Flysystem\Local;
use Monolog\Logger;
use Respect\Validation\Validator as v;
use Slim\Container;

abstract class Base extends BaseService
{
    private const REDIS_KEY = 'file:%s';

    protected Container $container;

    protected Logger $logger;

    protected Filesystem $fs;

    protected ImageManager $im;

    protected FileLib $fileLib;

    protected string $baseImagePath;

    public function __construct(Container $container) {
        $this->container = $container;
        $this->redisService = $container->get('redis_service');
        $this->logger = $container->get('logger');

        $this->baseImagePath = $this->getUploadDir();
        $this->fs = self::getFilesystem($this->baseImagePath);
        $this->im = $this->getImageManager();
        $this->fileLib = new FileLib($this->baseImagePath);
    }

    protected function getUploadDir(): string
    {
        return $this->container->get('settings')['upload_dir'];
    }

    public function getFs() {
        return $this->fs;
    }

    public static function getFilesystem($basePath): Filesystem
    {
        return new Filesystem(
            new Local\LocalFilesystemAdapter($basePath, PortableVisibilityConverter::fromArray([
                'file' => [
                    'public' => 0644,
                    'private' => 0644,
                ],
                'dir' => [
                    'public' => 0755,
                    'private' => 0755,
                ],
            ]))
        );
    }

    /**
     * @return ImageManager
     */
    public function getImageManager(): ImageManager
    {
        $driver = 'gd';
        if (isset($this->config['driver'])) {
            $driver = $this->config['driver'];
        }

        return new ImageManager([
            'driver' => $driver,
        ]);
    }

    /**
     * @throws \Exception
     */
    protected static function validateNoteName(string $name): string
    {
        if (! v::length(1, 50)->validate($name)) {
            throw new \Exception('The name of the note is invalid.', 400);
        }

        return $name;
    }

    public function getFileLib(): FileLib {
        return $this->fileLib;
    }

    public function getIm(): ImageManager {
        return $this->im;
    }
}

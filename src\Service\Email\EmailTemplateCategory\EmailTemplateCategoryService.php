<?php

declare(strict_types=1);

namespace App\Service\Email\EmailTemplateCategory;

use App\Models\Email\EmailTemplateCategory;

final class EmailTemplateCategoryService extends Base
{
    public function create(array $input): EmailTemplateCategory
    {
        /** @var EmailTemplateCategory $row */
        $row =  EmailTemplateCategory::create($input);

        if ($input['gen_sort'] ?? false) {
            $row->sort = intval(EmailTemplateCategory::query()
                    ->where('id', '!=', $row->id)
                    ->max('sort')
                ) + 1;
            $row->save();
        }

        return $row;
    }

    public function update($id, $input): EmailTemplateCategory
    {
        $row = EmailTemplateCategory::findOrFail($id);
        $row->update($input);

        if ($input['gen_sort'] ?? false) {
            $row->sort = intval(EmailTemplateCategory::query()
                    ->where('id', '!=', $row->id)
                    ->max('sort')
                ) + 1;
            $row->save();
        }
        return $row;
    }

    public function getOne(int $id, array $params=[]): EmailTemplateCategory
    {
        return $this->emailTemplateCategoryRepository->getQueryBuilder()->where('id', $id)->first();
    }

    public function getEmailTemplateCategorysByPage(
        int $page,
        int $perPage,
        ?array $params
    ): array
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($perPage < 1) {
            $perPage = self::DEFAULT_PER_PAGE_PAGINATION;
        }

        return $this->emailTemplateCategoryRepository->getEmailTemplateCategoriesByPage(
            $page,
            $perPage,
            $params
        );
    }

    public function delete($ids): void
    {
        $arr = is_array($ids) ? $ids : explode(',', $ids);
        $this->emailTemplateCategoryRepository->getQueryBuilder()->whereIn('id', $arr)->delete();
    }
}

<?php

declare(strict_types=1);

namespace App\Service\Email\EmailServer;

use App\Service\BaseService;
use App\Repository\Email\EmailServerRepository;
use Slim\Container;

abstract class Base extends BaseService
{
    private const REDIS_KEY = 'Email:%s';
    protected EmailServerRepository $emailServerRepository;

    public function __construct(Container $container)
    {
        $this->emailServerRepository = $container->get(EmailServerRepository::class);
    }

    public function getEmailServerRepository()
    {
        return $this->emailServerRepository;
    }
}


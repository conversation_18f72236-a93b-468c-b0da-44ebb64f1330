import { SupplierCallDirection, SupplierCallDirectionKv, SupplierCallType, SupplierCallTypeKv } from "@/constants";
import CreateForm from "@/pages/Supplier/SupplierCall/components/CreateForm";
import UpdateForm from "@/pages/Supplier/SupplierCall/components/UpdateForm";
import { deleteSupplierCall, getSupplierCallListByPage } from "@/services/app/Supplier/supplier-call";
import Util, { nl2br, sn } from "@/util";
import { DeleteOutlined, EditOutlined, EyeOutlined, MessageOutlined } from "@ant-design/icons";
import {
  ActionType,
  PageContainer,
  ProColumns,
  ProForm,
  ProFormCheckbox,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
  ProTable,
} from "@ant-design/pro-components";
import { Button, Card, Col, message, Popconfirm, Popover, Radio, Row, Space, Typography } from "antd";
import { useEffect, useRef, useState } from "react";
import SNotesViewerModal from "@/components/SNotesViewerModal";
import { SupplierCallTypeComp } from "../Supplier/components/SupplierCallList";
import SupplierCallFilesIcon from "./components/SupplierCallFilesIcon";
import { getSupplierListByPage } from "@/services/app/Supplier/supplier";
import useUserOptions from "@/hooks/BasicData/useUserOptions";
import useOrgAOptions from "@/hooks/BasicData/useOrgAOptions";
import { CheckboxOptionType } from "antd/lib";
import { getCustomerListByPage } from "@/services/app/Customer/customer";

export type SearchFormValueType = Partial<API.SupplierCall>;

type RowType = API.SupplierCall & {
  customer?: API.Customer;
};

/**
 * Show Supplier x Customer conversations
 *
 * key 'with parmas': mergeSupplierCustomer
 *
 * @param props
 * @returns
 */
const SupplierCallListPage: React.FC = (props) => {
  const actionRef = useRef<ActionType>();
  const searchFormRef = useRef<ProFormInstance>();

  const [currentRow, setCurrentRow] = useState<RowType>();
  const [loading, setLoading] = useState(false);

  const [createModalVisible, handleCreateModalVisible] = useState<boolean>(false);
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);

  //   const [replyModalVisible, handleReplyModalVisible] = useState<boolean>(false);
  //   const [openStaffEmailModal, setOpenStaffEmailModal] = useState<boolean>(false);

  //   const [inc_internal_email, set_inc_internal_email] = useState<boolean>(false);
  const [offer_mode, set_offer_mode] = useState<string>("inc");

  const [notesModalVisible, setNotesModalVisible] = useState<boolean>(false);
  const [offerNotesModalVisible, setOfferNotesModalVisible] = useState<boolean>(false);

  const { userOptions } = useUserOptions({ include_blank_option: true, include_all_option: true });
  const { orgAOptions, setSelectedOrgA, orgAList, selectedOrgA } = useOrgAOptions();

  useEffect(() => {
    const formValues = Util.getSfValues("cu_sf_all_call_list_page", {});
    searchFormRef.current?.resetFields();
    searchFormRef.current?.setFieldsValue(formValues);
    setSelectedOrgA(formValues.in_org_a || []);
  }, [setSelectedOrgA]);

  useEffect(() => {
    if (orgAList) {
      const formValues = Util.getSfValues("cu_sf_all_call_list_page", {});
      if (!("in_org_a" in formValues)) {
        searchFormRef.current?.setFieldValue("in_org_a", orgAList);
        setSelectedOrgA(orgAList);
        actionRef.current?.reload();
      }
    }
  }, [orgAList, setSelectedOrgA]);

  useEffect(() => {
    if (Object.keys(userOptions)?.length) {
      const formValues = Util.getSfValues("cu_sf_all_call_list_page", {});
      if (!("in_created_by" in formValues)) {
        searchFormRef.current?.setFieldValue(
          "in_created_by",
          userOptions.map((x) => x.value),
        );
        actionRef.current?.reload();
      }
    }
  }, [userOptions]);

  const columns: ProColumns<RowType>[] = [
    {
      title: "Created on",
      dataIndex: ["date"],
      defaultSortOrder: "descend",
      sorter: true,
      width: 120,
      align: "center",
      ellipsis: true,
      render: (dom, record) => Util.dtToDMYHHMMTz(record.date),
    },
    {
      title: "Type",
      dataIndex: ["type"],
      width: 24,
      ellipsis: true,
      align: "center",
      render: (__, record) => {
        return <SupplierCallTypeComp type={record.type} internal={record.uid?.startsWith("call_")} />;
      },
      /* onCell: (record) => {
        return { style: { backgroundColor: record?.uid?.startsWith("call_supp_") ? "lightgreen" : "auto" } };
      }, */
    },
    {
      title: "Direction",
      dataIndex: ["direction"],
      width: 30,
      ellipsis: true,
      align: "center",
      render: (dom, record) => {
        return record.direction ? <span className="c-grey">{SupplierCallDirectionKv[record.direction] ?? record.direction}</span> : null;
      },
    },
    {
      title: "OrgA",
      dataIndex: ["org_supplier", "org_a"],
      width: 30,
      ellipsis: true,
      align: "center",
    },
    {
      title: "Customer / Supplier",
      dataIndex: ["supplier", "name"],
      sorter: true,
      width: 160,
      ellipsis: true,
      render: (__, entity) => {
        let suppEle = null;
        let custEle = null;
        if (entity.uid?.startsWith("call_supp")) {
          suppEle = entity.supplier ? (
            <Typography.Link
              href={`${PUBLIC_PATH}suppliers/detail/${entity.supplier?.id}`}
              target="_blank"
              ellipsis
              style={{ fontSize: 11, fontStyle: "italic" }}
            >
              {entity.supplier?.name || ""}
            </Typography.Link>
          ) : null;
        } else if (entity.uid?.startsWith("call_cust")) {
          custEle = entity.customer?.name || "  ";
        }
        return custEle ? custEle : suppEle;
      },
      onCell: (record) => {
        return {
          className: record.uid?.startsWith("call_supp") ? "text-right" : "",
        };
      },
    },
    {
      title: "Offer No",
      dataIndex: ["offer_no"],
      ellipsis: true,
      align: "center",
      width: 60,
      render: (__, record) => {
        return record.offer_no ? (
          <Typography.Link href={`${PUBLIC_PATH}offers/detail/${record.offer_no}`} target="_blank">
            {record.offer_no}
          </Typography.Link>
        ) : null;
      },
    },
    {
      title: "Title",
      dataIndex: ["org_offer", "offer"],
      ellipsis: true,
      tooltip: "Offer info in WHC_Org",
      width: 400,
    },

    {
      title: "Offer Notes",
      dataIndex: ["offer_ext", "notes"],
      ellipsis: true,
      width: 24,
      render: (__, record) => {
        return record?.offer_ext?.notes ? (
          <Row wrap={false}>
            <Col flex="20px">
              <Popover
                title={`Offer Notes: ${SupplierCallTypeKv[record.type || 0]}${record.direction ? ` / ${SupplierCallDirectionKv[record.direction]}` : ""}`}
                content={
                  <div dangerouslySetInnerHTML={{ __html: nl2br(record?.offer_ext?.notes) }} style={{ maxHeight: 600, overflowY: "auto" }}></div>
                }
                trigger={["hover"]}
                styles={{ root: { maxWidth: 1200 } }}
                placement="right"
              >
                <div>
                  <MessageOutlined
                    className="c-blue cursor-pointer"
                    onClick={() => {
                      setCurrentRow(record);
                      setOfferNotesModalVisible(true);
                    }}
                  />
                </div>
              </Popover>
            </Col>
          </Row>
        ) : null;
      },
    },
    {
      title: "Notes",
      dataIndex: ["note"],
      ellipsis: true,
      width: 400,
      render: (__, record) => {
        return record?.note ? (
          <Row wrap={false}>
            <Col flex={"auto"}>
              <Typography.Text ellipsis>{Util.stripTags(record.note)}</Typography.Text>
            </Col>
            <Col flex="20px">
              <Popover
                title={`${SupplierCallTypeKv[record.type || 0]}${record.direction ? ` / ${SupplierCallDirectionKv[record.direction]}` : ""}`}
                content={<div dangerouslySetInnerHTML={{ __html: nl2br(record.note) }} style={{ maxHeight: 600, overflowY: "auto" }}></div>}
                trigger={["hover"]}
                styles={{ root: { maxWidth: 1200 } }}
                placement="right"
              >
                <div>
                  <EyeOutlined
                    className="c-blue cursor-pointer"
                    onClick={() => {
                      setCurrentRow(record);
                      setNotesModalVisible(true);
                    }}
                  />
                </div>
              </Popover>
            </Col>
          </Row>
        ) : null;
      },
    },
    {
      title: "Initials",
      dataIndex: ["user", "initials"],
      ellipsis: true,
      className: "",
      width: 30,
    },
    {
      title: "Prio",
      dataIndex: ["offer_ext", "prio"],
      ellipsis: true,
      className: "",
      width: 30,
    },

    /* {
      title: "Subject / Comment",
      dataIndex: ["email_subject"],
      width: 300,
      ellipsis: true,
      render(__, entity) {
        return entity.uid?.includes("call_") ? entity.comment : entity.email_subject;
      },
    }, */
    {
      title: "",
      dataIndex: "files",
      sorter: false,
      width: 20,
      hideInSearch: true,
      render(__, record) {
        return <SupplierCallFilesIcon files={record.files} />;
      },
    },
    /* {
      title: "First Opening",
      dataIndex: ["email_open_first_seen_on"],
      width: 120,
      ellipsis: true,
      render: (__, record) => Util.dtToDMYHHMMTz(record.email_open_first_seen_on),
    },
    {
      title: "How Often Opened",
      dataIndex: ["date"],
      width: 80,
      ellipsis: true,
      align: "right",
      render: (__, record) => ni(record.email_open_count),
    }, */
    {
      valueType: "option",
      render(__, entity) {
        return (
          <Space size={0}>
            {entity.uid?.startsWith("call_supp_") && (
              <Button
                type="link"
                color="primary"
                icon={<EditOutlined color="primary" />}
                onClick={() => {
                  setCurrentRow(entity);
                  handleUpdateModalVisible(true);
                }}
              />
            )}
            {entity.uid?.startsWith("call_supp_") && (
              <Popconfirm
                className="cursor-pointer c-red"
                title={<div>Are you sure you want to delete?</div>}
                okText="Yes"
                cancelText="No"
                styles={{ root: { width: 300 } }}
                onConfirm={async () => {
                  const hide = message.loading("Deleting a selected log...", 0);
                  deleteSupplierCall(entity.id)
                    .then(() => {
                      message.success("Deleted successfully.");
                      actionRef.current?.reload();
                    })
                    .catch(Util.error)
                    .finally(() => hide());
                }}
              >
                <div>
                  <Button type="link" danger icon={<DeleteOutlined color="danger" />} />
                </div>
              </Popconfirm>
            )}
          </Space>
        );
      },
    },
  ];

  useEffect(() => {
    actionRef.current?.reload();
  }, [offer_mode]);

  return (
    <PageContainer>
      <Card style={{ marginBottom: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          submitter={{
            render(props, dom) {
              return <div style={{ marginLeft: "auto", display: "flex", alignItems: "center" }}>{dom}</div>;
            },
            searchConfig: { submitText: "Search" },
            submitButtonProps: { loading, htmlType: "submit", style: { marginLeft: 8 } },
            onSubmit: () => actionRef.current?.reload(),
            onReset: () => {
              searchFormRef.current?.resetFields();
              actionRef.current?.reload();
            },
          }}
        >
          <ProFormCheckbox.Group
            name="in_org_a"
            label="Org A"
            tooltip="Org A in WHC_Org Supplier"
            options={orgAOptions as any}
            fieldProps={{
              onChange(checkedValues) {
                const oldCheckedValues = [...selectedOrgA];
                // const currentSelectedValues = checkedValues.filter((x) => !oldCheckedValues.includes(x));
                const currentRemovedValues = oldCheckedValues.filter((x: string) => !checkedValues.includes(x));

                // when `all` is unchecked
                if (currentRemovedValues.includes("all")) {
                  const newCheckedValues = ["-"];
                  setSelectedOrgA(newCheckedValues);
                } else {
                  setSelectedOrgA(checkedValues);
                }
                actionRef.current?.reload();
              },
            }}
          />
          <ProFormCheckbox.Group
            name="in_created_by"
            label="Initials"
            options={userOptions as CheckboxOptionType[]}
            fieldProps={{
              onChange(checkedValue) {
                actionRef.current?.reload();
              },
            }}
          />
          <ProFormText name="offer_no" label="Offer No" placeholder="Offer No..." width={120} />
          <ProFormSelect
            name="supp_supplier_id"
            label="Supplier"
            width={140}
            showSearch
            allowClear
            request={async (params) => {
              return getSupplierListByPage(params)
                .then((res) => {
                  return res.data?.map((x) => ({ ...x, value: x.id, label: x.name }));
                })
                .catch((err) => {
                  Util.error(err);
                  return [];
                });
            }}
            fieldProps={{
              popupMatchSelectWidth: false,
              onChange(value, option) {
                actionRef.current?.reload();
              },
            }}
          />
          <ProFormSelect
            name="customer_id"
            label="Customer"
            width={140}
            showSearch
            allowClear
            request={async (params) => {
              return getCustomerListByPage(params)
                .then((res) => {
                  return res.data?.map((x) => ({ ...x, value: x.id, label: x.name }));
                })
                .catch((err) => {
                  Util.error(err);
                  return [];
                });
            }}
            fieldProps={{
              popupMatchSelectWidth: false,
              onChange(value, option) {
                actionRef.current?.reload();
              },
            }}
          />
          <ProFormText name="ft_note" label="Notes" placeholder="Search Notes..." width={240} />
        </ProForm>
      </Card>

      <ProTable<RowType, API.PageParams>
        headerTitle={
          <Space size={32}>
            <span>All Conversations</span>
            <div style={{ fontWeight: "normal" }}>
              <label htmlFor="offer_mode" style={{ marginRight: 8 }}>
                Offer
              </label>
              <Radio.Group
                id="offer_mode"
                onChange={(e) => set_offer_mode(e.target.value)}
                value={offer_mode}
                options={[
                  { value: "inc", label: "Inc" },
                  { value: "exc", label: "Exc" },
                  { value: "only", label: "Only" },
                ]}
              />
            </div>
          </Space>
        }
        actionRef={actionRef}
        rowKey="uid"
        size="small"
        revalidateOnFocus={false}
        options={{ fullScreen: true, density: false, setting: false }}
        search={false}
        sticky
        scroll={{ x: 800 }}
        pagination={{
          showSizeChanger: true,
          defaultPageSize: sn(Util.getSfValues("cu_sf_all_call_list_page_p")?.pageSize ?? 20),
        }}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues("cu_sf_all_call_list_page", searchFormValues);
          Util.setSfValues("cu_sf_all_call_list_page_p", params);

          setLoading(true);
          return getSupplierCallListByPage(
            {
              ...searchFormValues,
              ...params,
              with: "mergeSupplierCustomer,user,offerExt",
              offer_mode,
            },
            sort,
            filter,
          )
            .then((res) => {
              if (currentRow?.id) {
                setCurrentRow(res.data.find((x) => x.id == currentRow.id));
              }
              return res;
            })
            .finally(() => setLoading(false));
        }}
        onRequestError={Util.error}
        columns={columns}
        columnEmptyText=""
      />

      <CreateForm
        initialValues={{
          type: SupplierCallType.Phone,
          direction: SupplierCallDirection.Out,
        }}
        modalVisible={createModalVisible}
        handleModalVisible={handleCreateModalVisible}
        onSubmit={(value) => {
          actionRef.current?.reload();
        }}
      />

      <UpdateForm
        initialValues={{ ...currentRow }}
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        onSubmit={(value) => {
          actionRef.current?.reload();
        }}
      />

      <SNotesViewerModal
        id="supplier-call-notes-viewer"
        title={`${SupplierCallTypeKv[currentRow?.type || 0]}${currentRow?.direction ? ` / ${SupplierCallDirectionKv[currentRow?.direction]}` : ""} Notes`}
        content={currentRow?.note}
        modalVisible={notesModalVisible}
        handleModalVisible={setNotesModalVisible}
      />

      <SNotesViewerModal
        id="offer-notes-viewer"
        title={`Offer Notes: ${SupplierCallTypeKv[currentRow?.type || 0]}${currentRow?.direction ? ` / ${SupplierCallDirectionKv[currentRow?.direction]}` : ""}`}
        content={currentRow?.offer_ext?.notes}
        modalVisible={offerNotesModalVisible}
        handleModalVisible={setOfferNotesModalVisible}
      />
    </PageContainer>
  );
};

export default SupplierCallListPage;

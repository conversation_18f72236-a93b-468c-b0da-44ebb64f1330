<?php

declare(strict_types=1);

namespace App\Service\Email\Email;

use App\Exception\BaseException;
use App\Lib\FileLib;
use App\Lib\Func;
use App\Lib\FuncModel;
use App\Lib\SysMsg;
use App\Models\Customer\Customer;
use App\Models\Email\Email;
use App\Models\Email\EmailAccount;
use App\Models\Email\EmailServer;
use App\Models\File;
use App\Models\Supplier\Supplier;
use App\Models\Sys\Dict;
use App\Repository\Email\EmailAccountRepository;
use App\Service\Email\EmailAccount\EmailAccountService;
use App\Service\MediaApi\MediaApiBaseService;
use Greew\OAuth2\Client\Provider\Azure;
use Illuminate\Database\Eloquent\Model;
use PhpImap\Exceptions\ConnectionException;
use PhpImap\Mailbox;
use PHPMailer\PHPMailer\OAuth;
use PHPMailer\PHPMailer\PHPMailer;
use Webklex\PHPIMAP\Attachment;
use Webklex\PHPIMAP\ClientManager;


final class EmailService extends Base
{
    /**
     * @param array $input
     * @return \Illuminate\Database\Eloquent\Model|Email
     */
    public function create(array $input)
    {
        return Email::query()->create($input);
    }

    public function update($id, $input): Email
    {
        $row = Email::findOrFail($id);
        $row->update($input);
        return $row;
    }

    /**
     * @param $input
     * @param \Psr\Http\Message\UploadedFileInterface[] $files
     * @return void
     * @throws \Exception
     */
    public function sendV2($input, $files, $params = [])
    {
        $sender = $input['sender'] ?? '';
        $receiver = $input['receiver'] ?? '';
        if (!$sender || !$receiver) {
            BaseException::raiseInvalidRequest('Sender and Receiver are required.');
        }

        $id = $input['id'] ?? null;
        /** @var Email $orgEmail */
        $orgEmail = Email::query()
            ->where('id', $id)
            ->with('emailAccount', function ($builder) {
                $builder->with('emailServer');
            })->first();


        // $domain = substr($sender, strpos($sender, '@') + 1);
        /** @var EmailServer $server */
        $server = EmailServer::query()->where('imap_host', 'outlook.office365.com')->firstOrFail();

        /** @var EmailAccount $smtpEmailAccount */
        $smtpEmailAccount = EmailAccount::query()->where('server_id', $server->id)->firstOrFail();

        if (!$server->smtp_host)
            BaseException::raiseInvalidRequest('SMTP host is required!');
        if (!$server->smtp_port)
            BaseException::raiseInvalidRequest('SMTP port is required!');

        // From
        $senders = Func::parseEmailStrList($sender);
        if ($senders)
            $sender = $senders[0];
        else
            BaseException::raiseInvalidRequest('From is required.');


        $mail = new PHPMailer(true);

        //Server settings
        $mail->isSMTP();                                            //Send using SMTP
        $mail->Host = $server->smtp_host;                           //Set the SMTP server to send through
        $mail->SMTPAuth = true;                                   //Enable SMTP authentication
        $mail->Username = $server->smtp_user;                     //SMTP username
        // $mail->Password =  EmailService::getM365Token();
        $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;            //Enable implicit TLS encryption
        // $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;            //Enable implicit TLS encryption
        $mail->Port = $server->smtp_port;                           //TCP port to connect to; use 587 if you have set `SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS`
        $mail->CharSet = 'UTF-8';
        $mail->SMTPDebug = 3;


        // Set OAuth2 token
        $m365Setting = Func::getContainer()->get('settings')['m365'];
        if (!$m365Setting) BaseException::raiseInternalServerError("Outlook setting is not configured!");


        $mail->AuthType = 'XOAUTH2';
        $mail->setOAuth(new OAuth([
            'provider' => new Azure([
                'clientId' => $m365Setting['clientId'],
                'tenantId' => $m365Setting['tenant'],
                'clientSecret' => $m365Setting['clientSecret'],
                'redirectUri' => 'http://localhost',
            ]),
            'clientId' => $m365Setting['clientId'],
            'refreshToken' => EmailService::getM365Token(),
            'clientSecret' => $m365Setting['clientSecret'],
            'userName' => $smtpEmailAccount->email,
        ]));


        $mail->SMTPOptions = array(
            'ssl' => array(
                'verify_peer' => false,
                'verify_peer_name' => false,
                'allow_self_signed' => true
            )
        );


        $mail->setFrom(...$sender);

        //Recipients
        $receivers = Func::parseEmailStrList($receiver);
        if ($receivers) {
            foreach ($receivers as $to) {
                $mail->addAddress(...$to);
            }
        }

        $bcc = FuncModel::getDictValue(Dict::CODE_EMAIL_BCC);
        if ($bcc) {
            $bccList = Func::parseEmailStrList($bcc);
            foreach ($bccList as $e) {
                $mail->addBCC(...$e);
            }
        }

        //Attachments
        $dbAttachments = [];
        if ($files) {
            $attachmentsPath = Func::pathJoin(self::getEmailAttachmentPathOrSubUrl(), $sender[0], 'sent');
            if (!file_exists($attachmentsPath)) {
                mkdir($attachmentsPath, 0755, true);
            }

            $ind = 0;
            foreach ($files as $attachment) {
                $newFileName = sprintf("%s_%02s_%s", time(), $ind, $attachment->getClientFilename() ?? 'tmp');
                $filePath = Func::pathJoin($attachmentsPath, $newFileName);
                $attachment->moveTo($filePath);
                $mail->addAttachment($filePath);

                // to do After tracking attachments, remove it.
                // Func::getLogger()->error("[DEBUG][MANUAL][attached]" . $newFileName . ', ' . $filePath);
                $dbAttachments[] = [
                    'org_name' => $attachment->getClientFilename(),
                    'name' => $newFileName,
                    'ext' => (new FileLib(''))->mime2ext($attachment->getClientMediaType()),
                    'mime_type' => $attachment->getClientMediaType(),
                    'size' => $attachment->getSize(),
                    'file_path' => Func::pathJoinUrl(self::getEmailAttachmentPathOrSubUrl(true), $sender[0], 'sent', $newFileName),
                ];
                $ind++;
            }
        }

        //Content
        $mail->isHTML(true);                                  //Set email format to HTML
        $mail->Subject = $input['subject'] ?? '';
        $mail->Body = $input['text_html'] ?? '';
        $mail->AltBody = strip_tags($mail->Body);

        // Save data
        $model = new Email();
        $model->email_account_id = $orgEmail ? $orgEmail->email_account_id : null;
        $model->box = 'SENT';
        $model->sender = $sender[0];
        $model->sender_host = substr($sender[0], strpos($sender[0] ?? '', '@') + 1);
        $model->sender_name = $sender[1];
        $model->receiver = $receiver;
        $model->subject = $mail->Subject;
        $model->text_html = $mail->Body;
        $model->text_plain = $mail->AltBody;
        $model->date = Func::dtDbDatetimeStr();

        $model->from_host = $model->sender_host;
        $model->from_name = $sender[1];

        $toArr = [];
        foreach ($receivers as $to) {
            $toArr[$to[0]] = $to[1] ?? null;
        }
        $model->to = $toArr;

        $model->attachments = $dbAttachments;
        $model->has_attachments = count($dbAttachments) > 0;
        $model->save();

        // Add tracking pixel.
        $mail->Body .= Func::getPixelImageHtml($model->id, $sender[0], $receiver);

        try {
            if ($mail->send()) {
                SysMsg::get_instance()->success('Message has been sent.');
            }
        } catch (\Exception $e) {
            BaseException::raiseInternalServerError("Message could not be sent. Mailer Error: {$mail->ErrorInfo}");
        }
    }

    /**
     * @param $input
     * @param \Psr\Http\Message\UploadedFileInterface[]|File[] $files
     * @return void
     * @throws \Exception
     */
    public function send($input, $files, $params = [])
    {
        $emailMode = (int)FuncModel::getDictValue(Dict::CODE_EMAIL_IS_OAUTH);
        if ($emailMode != 0) {
            return $this->sendV2($input, $files, $params);
        }

        $sender = $input['sender'] ?? '';
        $receiver = $input['receiver'] ?? '';
        if (!$sender || !$receiver) {
            BaseException::raiseInvalidRequest('Sender and Receiver are required.');
        }

        $id = $input['id'] ?? null;
        /** @var Email $orgEmail */
        $orgEmail = $id ? Email::query()->where('id', $id)
            ->with('emailAccount', function ($builder) {
                $builder->with('emailServer');
            })->first() : null;

        /** @var EmailAccount | null $senderObj */
        // $senderObj = EmailAccount::query()->where('email', $sender)->first();
        // $domain = substr($sender, strpos($sender, '@') + 1);

        /** @var EmailServer $server */
        $server = $orgEmail?->emailAccount?->emailServer
            ?? EmailServer::query()->where('is_oauth', 0)->firstOrFail();

        if (!$server->smtp_host)
            BaseException::raiseInvalidRequest('SMTP host is required!');
        if (!$server->smtp_port)
            BaseException::raiseInvalidRequest('SMTP port is required!');

        // From
        $senders = Func::parseEmailStrList($sender);
        if ($senders)
            $sender = $senders[0];
        else
            BaseException::raiseInvalidRequest('From is required.');

        if ($input['text_html'] && Func::hasInlineImage($input['text_html'])) {
            /** @var MediaApiBaseService $mediaApiService */
            $mediaApiService = Func::getContainer()->get(MediaApiBaseService::class);
            $input['text_html'] = $mediaApiService->extractInlineImage($input['text_html']);
            if (!$input['text_html']) {
                BaseException::raiseInternalServerError("Html body processing was failed!");
            }
        }

        $mail = new PHPMailer(true);

        //Server settings
        $mail->isSMTP();                                            //Send using SMTP
        $mail->Host = $server->smtp_host;                           //Set the SMTP server to send through
        $mail->SMTPAuth = true;                                   //Enable SMTP authentication
        $mail->Username = $server->smtp_user;                     //SMTP username
        $mail->Password = (string)Func::mcrypt('decrypt', $server->smtp_password, EmailAccountService::SEC_KEY, EmailAccountService::SEC_IV);                               //SMTP password
        $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;            //Enable implicit TLS encryption
        $mail->Port = $server->smtp_port;                           //TCP port to connect to; use 587 if you have set `SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS`
        $mail->CharSet = 'UTF-8';

        if (Func::isDev()) {
            $mail->port = 587;
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
            $mail->SMTPOptions = array(
                'ssl' => array(
                    'verify_peer' => false,
                    'verify_peer_name' => false,
                    'allow_self_signed' => true,
                )
            );
        }

        $mail->setFrom(...$sender);

        //Recipients
        $receivers = Func::parseEmailStrList($receiver);
        if ($receivers) {
            foreach ($receivers as $to) {
                $mail->addAddress(...$to);
            }
        } else {
            BaseException::raiseInvalidRequest("No receivers!");
        }

        $bccRequested = $input['bcc'] ?? '';
        $bcc = ($bccRequested ? $bccRequested . ',' : '') . FuncModel::getDictValue(Dict::CODE_EMAIL_BCC);
        $bccList = Func::parseEmailStrList($bcc);
        if ($bccList) {
            foreach ($bccList as $e) {
                $mail->addBCC(...$e);
            }
        }

        //Attachments
        // $mail->addAttachment('/var/tmp/file.tar.gz');         //Add attachments
        // $mail->addAttachment('/tmp/image.jpg', 'new.jpg');    //Optional name
        $dbAttachments = [];
        if ($files) {
            if ($files[0] instanceof File) {
                $ind = 0;
                foreach ($files as $attachment) {
                    /** @var File $attachment */
                    $mail->addAttachment($attachment->abs_path);

                    $dbAttachments[] = [
                        'org_name' => $attachment->file_name,
                        'name' => $attachment->clean_file_name,
                        'ext' => (new FileLib(''))->mime2ext($attachment->type),
                        'mime_type' => $attachment->type,
                        'size' => $attachment->size,
                        'file_path' => $attachment->org_path,
                    ];
                    $ind++;
                }
            } else {
                $attachmentsPath = Func::pathJoin(self::getEmailAttachmentPathOrSubUrl(), $sender[0], 'sent');
                if (!file_exists($attachmentsPath)) {
                    mkdir($attachmentsPath, 0755, true);
                }

                $ind = 0;
                foreach ($files as $attachment) {
                    $newFileName = sprintf("%s_%02s_%s", time(), $ind, Func::getSafeFilePath($attachment->getClientFilename() ?? 'tmp'));
                    $filePath = Func::pathJoin($attachmentsPath, $newFileName);
                    $attachment->moveTo($filePath);
                    $mail->addAttachment($filePath);

                    $dbAttachments[] = [
                        'org_name' => $attachment->getClientFilename(),
                        'name' => $newFileName,
                        'ext' => (new FileLib(''))->mime2ext($attachment->getClientMediaType()),
                        'mime_type' => $attachment->getClientMediaType(),
                        'size' => $attachment->getSize(),
                        'file_path' => Func::pathJoinUrl(self::getEmailAttachmentPathOrSubUrl(true), $sender[0], 'sent', $newFileName),
                    ];
                    $ind++;
                }
            }
        }

        //Content
        $mail->isHTML(true);                                  //Set email format to HTML
        $mail->Subject = $input['subject'] ?? '';
        $mail->Body = $input['text_html'] ?? '';
        $mail->AltBody = strip_tags($mail->Body);

        // Save data
        $model = new Email();
        $model->email_account_id = $orgEmail ? $orgEmail->email_account_id : null;
        $model->box = 'SENT';
        $model->sender = $sender[0];
        $model->sender_host = substr($sender[0], strpos($sender[0] ?? '', '@') + 1);
        $model->sender_name = $sender[1];
        $model->receiver = $receiver;
        $model->subject = $mail->Subject;
        $model->text_html = $mail->Body;
        $model->text_plain = $mail->AltBody;
        $model->date = Func::dtDbDatetimeStr();
        $model->from_host = $model->sender_host;
        $model->from_name = $sender[1];
        $model->attachments = $dbAttachments;
        $model->has_attachments = count($dbAttachments) > 0;
        $model->supplier_id = $input['supplier_id'] ?? $orgEmail?->supplier_id ?? null;
        $model->customer_id = $input['customer_id'] ?? $orgEmail?->customer_id ?? null;
        $model->email_template_id = $input['email_template_id'] ?? null;

        // To
        $toArr = [];
        foreach ($receivers as $to) {
            $toArr[$to[0]] = $to[1] ?? '';

        }
        $model->to = $toArr;

        // BCC
        $bccArr = [];
        foreach ($bccList as $to) {
            $bccArr[$to[0]] = $to[1] ?? '';
        }
        $model->bcc = $bccArr;

        $model->save();

        // Save receivers list in database
        $toArrObj = [];
        foreach ($toArr as $email_addr => $name) {
            $toArrObj[] = [
                'email_id' => $model->id,
                'email' => $email_addr,
                'name' => $name,
            ];
        }
        $model->receivers()->insert($toArrObj);

        // Sending emails
        $okList = [];
        $isInternal = ($input['internal'] ?? false);
        $supplierId = $input['supplier_id'] ?? null;
        $customerId = $input['customer_id'] ?? null;
        try {
            foreach ($toArrObj as $x) {
                $mail->clearAddresses();
                $mail->addAddress($x['email'], $x['name']);

                // pre-checking
                $textHtml = $model->text_html;

                // If supplier is not specified, we try to find
                if ((!$supplierId && !$customerId) && !$isInternal && count($toArrObj) == 1) {
                    $input['supplier_id'] = Supplier::query()
                        ->whereHas('contacts', function($qb) use (&$x) {
                            $qb->where('email', $x['email']);
                        })->value('id');
                    $model->supplier_id = $input['supplier_id'];
                    $model->save();
                }

                // If supplier is not specified, we try to find
                if ((!$customerId && !$supplierId) && !$isInternal && count($toArrObj) == 1) {
                    $input['customer_id'] = Customer::query()
                        ->whereHas('contacts', function($qb) use (&$x) {
                            $qb->where('email', $x['email']);
                        })->value('id');
                    $model->customer_id = $input['customer_id'];
                    $model->save();
                }

                $firstName = null;
                if ($supplierId && !$isInternal) {
                    $supplier = Supplier::find($supplierId);
                    if ($supplier) {
                        $firstName = $supplier->firstname;
                    }
                } else if ($customerId && !$isInternal) {
                    $customer = Customer::find($customerId);
                    if ($customer) {
                        $firstName = $customer->firstname;
                    }
                } else {
                    if ($x['name']) {
                        $firstName = $x['name'];
                    }
                }

                $textHtml = \Safe\preg_replace("/\%salutation\%/", '<span style="font-size: 12px">Dear' . ($firstName ? ' ' : '') . ($firstName ?? '') . ',</span>', $textHtml);

                $mail->Body = $textHtml;
                $mail->AltBody = strip_tags($mail->Body);

                // Add tracking pixel
                $mail->Body .= Func::getPixelImageHtml($model->id, $sender[0], $x['email']);
                if ($mail->send()) {
                    $okList[] = Func::buildEmailStr($x['email'], $x['name']);
                }
            }

            if (count($okList) == count($toArrObj)) {
                if (!($params['skip_success_msg'] ?? false)) {
                    SysMsg::get_instance()->success('Email has been sent.');
                }
            } else {
                SysMsg::get_instance()->info("Partially sent emails to " . implode(', ', $okList));
            }
        } catch (\Exception $e) {
            if ($okList) {
                SysMsg::get_instance()->info("Partially sent emails to " . implode(', ', $okList));
            }
            BaseException::raiseInternalServerError("Message could not be sent. Mailer Error: {$mail->ErrorInfo}. " . ($e->getMessage()));
        }
    }

    /**
     * @param int $id
     * @param array $params
     * @return Email|Model
     */
    public function getOne(int $id, array $params = []): Email
    {
        return $this->emailRepository->getQueryBuilder()->where('id', $id)->first();
    }

    public function getEmailsByPage(int $page, int $perPage, ?array $params): array
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($perPage < 1) {
            $perPage = self::DEFAULT_PER_PAGE_PAGINATION;
        }

        return $this->emailRepository->getEmailsByPage(
            $page,
            $perPage,
            $params
        );
    }

    public function delete($ids): void
    {
        $arr = is_array($ids) ? $ids : explode(',', $ids);
        $this->emailRepository->getQueryBuilder()->whereIn('id', $arr)->delete();
    }

    public static function getM365Token($params = [])
    {
        /** @var \App\Models\Sys\SysOAuth $obj */
        $obj = \App\Models\Sys\SysOAuth::query()
            ->where('type', \App\Models\Sys\SysOAuth::TYPE_M365)
            ->firstOrFail();

        if ($obj->token && $obj->token_expired_at > time() - 3 * 60) {
            // OK, token is valid
        } else {
            if ($obj->refresh_token) {
                $m365Setting = Func::getContainer()->get('settings')['m365'];

                if (!$m365Setting) BaseException::raiseInternalServerError("Outlook setting is not configured!");
                $provider = new \League\OAuth2\Client\Provider\GenericProvider([
                    // Required
                    'clientId' => $m365Setting['clientId'],
                    'clientSecret' => $m365Setting['clientSecret'],
                    'redirectUri' => 'http://localhost',
                    // Optional
                    'urlAuthorize' => "https://login.microsoftonline.com/{$m365Setting['tenant']}/oauth2/v2.0/authorize",
                    'urlAccessToken' => "https://login.microsoftonline.com/{$m365Setting['tenant']}/oauth2/v2.0/token",
                    'urlResourceOwnerDetails' => 'https://outlook.office.com/api/v1.0/me',
                    // 'scopeSeparator' => ' ',
                    // 'scopes' => ['IMAP.AccessAsUser.All', 'SMTP.Send', 'offline_access'],
                ]);


                $tokens = $provider->getAccessToken('refresh_token', [
                    'refresh_token' => $obj->refresh_token,
                    // 'scope' => 'https://outlook.office.com/SMTP.Send offline_access',
                    // 'scope' => 'https://outlook.office365.com/IMAP.AccessAsUser.All offline_access',
                    // 'scope' => 'https://graph.microsoft.com/SMTP.Send',

                    // working
                    // 'scope' => 'https://outlook.office365.com/IMAP.AccessAsUser.All',
                    // working
                    // 'scope' => 'https://outlook.office.com/SMTP.Send',
                    // 'scope' => 'https://outlook.office365.com/IMAP.AccessAsUser.All',
                    // 'scope' => 'offline_access SMTP.Send',
                    // 'scope' => 'https://graph.microsoft.com/.default IMAP.AccessAsUser.All',
                    'scope' => 'IMAP.AccessAsUser.All',
                ]);

                $obj->refresh_token = $tokens->getRefreshToken();
                $obj->token = $tokens->getToken();
                $obj->token_expired_at = $tokens->getExpires();
                $obj->save();
            }
        }
        return $obj->token;
    }

    public function popEmailsV2($params = [])
    {
        /** @var EmailAccountRepository $repo */
        $repo = Func::getContainer()->get(EmailAccountRepository::class);

        $params['emailServer.is_oauth'] = 1;
        $accounts = $repo->getValidAccounts($params);

        if (empty($accounts)) {
            BaseException::raiseInvalidRequest("IMAP account for OAuth does not exist! Please create a new account in Email Accounts page.");
        }

        foreach ($accounts as $account) {
            // Note
            $attachmentsPath = Func::pathJoin(self::getEmailAttachmentPathOrSubUrl(), $account->email, 'inbox');
            if (!file_exists($attachmentsPath)) {
                mkdir($attachmentsPath, 0755, true);
            }

            // Since date filters
            $sinceDate = $account->emails_max_date ?? $account->settings['imapSince'] ?? date('Y-01-01');
            $sinceDate = substr($sinceDate, 0, 10);
            $sinceDateYmd = substr($sinceDate, 0, 10);
            $sinceDate = date('d.M.Y', strtotime($sinceDate));

            $cm = new ClientManager();
            $client = $cm->make([
                'host' => $account->emailServer->imap_host,
                'port' => $account->emailServer->imap_port_ssl,
                'encryption' => 'ssl',
                'validate_cert' => true,
                'username' => $account->email,
                // 'username' => '<EMAIL>',
                // 'username' => '<EMAIL>',
                'password' => EmailService::getM365Token(),
                'protocol' => 'imap',
                'authentication' => "oauth"
            ]);

            //Connect to the IMAP Server
            $client->connect();

            //Get all Mailboxes
            /** @var \Webklex\PHPIMAP\Support\FolderCollection $folders */
            $folders = $client->getFolders();

            try {
                //Loop through every Mailbox
                /** @var \Webklex\PHPIMAP\Folder $folder */
                foreach ($folders as $folder) {
                    if (!in_array($folder->name, ['INBOX'/*, 'Sent Items'*/])) {
                        continue;
                    }

                    $boxNameInDb = $folder->name == 'Sent Items' ? 'SENT' : $folder->name;

                    //Get all Messages of the current Mailbox $folder
                    $query = $folder->messages();
                    $query->since($sinceDate);
                    $query->setFetchOrderAsc();
                    $existedUids = $account->emails()
                        ->where('date', 'LIKE', $sinceDateYmd . "%")
                        ->select('mail_id')
                        ->pluck('mail_id')
                        ?->toArray();

                    if ($existedUids) {
                        $query->whereNot()->whereUidIn($existedUids);
                    }

                    $query->chunked(function ($messages, $chunk) use (&$account, &$boxNameInDb, &$attachmentsPath) {
                        /** @var \Webklex\PHPIMAP\Message $message */
                        foreach ($messages as $ind => $message) {
                            if ((int)$account->emails_max_mail_id > $message->getUid()) continue;

                            $emailModel = Email::findOrNewByMail2($message, $account->id);
                            $emailModel->box = $boxNameInDb;

                            if ($message->hasAttachments()) {
                                $attachments = $message->getAttachments();
                                $ind = 0;
                                $dbAttachments = [];

                                /** @var Attachment $attachment */
                                foreach ($attachments as $key => &$attachment) {
                                    $newFileName = sprintf("%010s_%02s_%s", $emailModel->mail_id, $ind, Func::getSafeFilePath($attachment->getClientFilename() ?? 'tmp'));
                                    $attachment->save($attachmentsPath . "/", $newFileName);

                                    $dbAttachments[] = [
                                        'org_name' => $attachment->name,
                                        'name' => $newFileName,
                                        'ext' => $attachment->getExtension(),
                                        'mime_type' => $attachment->getMimeType(),
                                        'size' => $attachment->getSize(),
                                        'file_path' => Func::pathJoinUrl(self::getEmailAttachmentPathOrSubUrl(true), $account->email, 'inbox', $newFileName),
                                        'id' => $attachment->id,
                                    ];
                                    $ind++;
                                }
                                $emailModel->attachments = $dbAttachments;
                            }

                            $emailModel->save();

                        }
                        // to do change chunk size
                    }, 10, 1);
                }

            } catch (\Exception  $ex) {
                Func::getLogger()->error($ex->getMessage());
                throw $ex;
            }
        }
    }

    /**
     * @param $params
     * @return void
     * @throws ConnectionException
     * @throws \PhpImap\Exceptions\InvalidParameterException
     * @throws \Psr\Container\ContainerExceptionInterface
     * @d eprecated replaced by OAuth 2.0
     *
     */
    public function popEmails($params = [])
    {
        $emailMode = (int)FuncModel::getDictValue(Dict::CODE_EMAIL_IS_OAUTH);
        if ($emailMode != 0) {
            return $this->popEmailsV2($params);
        }

        /** @var EmailAccountRepository $repo */
        $repo = Func::getContainer()->get(EmailAccountRepository::class);

        $accounts = $repo->getValidAccounts($params);

        foreach ($accounts as $account) {
            // Note
            $attachmentsPath = Func::pathJoin(self::getEmailAttachmentPathOrSubUrl(), $account->email, 'inbox');
            if (!file_exists($attachmentsPath)) {
                mkdir($attachmentsPath, 0755, true);
            }

            $imapHost = $account->emailServer->imap_host;
            $isSSL = $account->emailServer->imap_ssl;
            $imapPort = $account->emailServer->imap_port;
            $imapPortSSL = $account->emailServer->imap_port_ssl;

            // Since date filters
            $sinceDate = $account->emails_max_date ?? $account->settings['imapSince'] ?? date('Y-01-01');
            $sinceDate = substr($sinceDate, 0, 10);
            $sinceDate = date('d-M-Y', strtotime($sinceDate));

            // max UID
            $maxUid = $account->emails_max_mail_id;

            $imapPass = (string)Func::mcrypt('decrypt', $account->password, EmailAccountService::SEC_KEY, EmailAccountService::SEC_IV);

            $mailbox = new Mailbox(
                $isSSL ?
                    "{" . $imapHost . ":" . $imapPortSSL . "/imap/ssl/novalidate-cert/debug}" :  // IMAP server and mailbox folder
                    "{" . $imapHost . ":" . $imapPort . "/imap/novalidate-cert/debug}", // IMAP server and mailbox folder
                // '{local-max.com:993/imap/ssl/novalidate-cert}INBOX', // IMAP server and mailbox folder
                $account->email, // Username for the before configured mailbox
                $imapPass, // Password for the before configured username

                null, // $attachmentsPath, // Directory, where attachments will be saved (optional)
                'UTF-8', // Server encoding (optional)
                true, // Trim leading/ending whitespaces of IMAP path (optional)
                true // Attachment filename mode (optional; false = random filename; true = original filename)
            );

            // set some connection arguments (if appropriate)
            $mailbox->setConnectionArgs(
                CL_EXPUNGE // expunge deleted mails upon mailbox close
                // | OP_SECURE // don't do non-secure authentication
                , 3, []
            );

            // $mailbox->setImapSearchOption(SE_FREE);
            // $mailbox->setAttachmentsIgnore(true);

            try {
                // Get all emails (messages)
                // PHP.net imap_search criteria: http://php.net/manual/en/function.imap-search.php
                $criteria = "";
                if ($sinceDate) {
                    // $criteria .= ' SINCE "'.$sinceDate.'"';
                    // $criteria .= ' SINCE 20-Sep-2015';
                    $criteria .= ' SINCE ' . $sinceDate;
                }

                $mailsIds = $mailbox->searchMailbox($criteria ? $criteria : 'ALL');

                $mailsIds = array_filter($mailsIds, function ($x) use ($maxUid) {
                    return $x > $maxUid;
                });
            } catch (ConnectionException  $ex) {
                Func::getLogger()->error($ex->getMessage());
                throw $ex;
            }

            if ($mailsIds) {
                foreach ($mailsIds as $mailId) {
                    $mail = $mailbox->getMail($mailId);
                    $emailModel = Email::findOrNewByMail($mail, $account->id);
                    if ($mail->hasAttachments()) {
                        $attachments = $mail->getAttachments();
                        $ind = 0;
                        $dbAttachments = [];
                        foreach ($attachments as $key => &$attachment) {
                            $newFileName = sprintf("%010s_%02s_%s", $mailId, $ind, Func::getSafeFilePath($attachment->name));
                            $filePath = Func::pathJoin($attachmentsPath, $newFileName);
                            $attachment->setFilePath($filePath);
                            $attachment->saveToDisk();
                            $ind++;

                            $dbAttachments[] = [
                                'org_name' => $attachment->name,
                                'name' => $newFileName,
                                'ext' => $attachment->fileExtension,
                                'mime_type' => $attachment->mimeType,
                                'size' => $attachment->sizeInBytes,
                                'subtype' => $attachment->subtype,
                                'file_path' => Func::pathJoinUrl(self::getEmailAttachmentPathOrSubUrl(true), $account->email, 'inbox', $newFileName),
                                'id' => $attachment->id,
                            ];
                        }
                        $emailModel->attachments = $dbAttachments;
                    }
                    $emailModel->save();
                }
            }

            /*// Show, if $mail has one or more attachments
            echo "\nMail has attachments? ";
            if($mail->hasAttachments()) {
                echo "Yes\n";
            } else {
                echo "No\n";
            }

            // print_r($mail);

            // Print all information of $mail
            print_r($mail->subject);
            print_r($mail->mailboxFolder);
            print_r($mail->textHtml);
            print_r($mail->textPlain);

            $attachments = $mail->getAttachments();
            foreach ($attachments as $key => $attachment) {
                print_r($attachment->id . ', '. $attachment->mimeType . ', '. $attachment->filePath . PHP_EOL);
            }*/
        }
    }

    public function popEmailsTest($params = [])
    {
        $mailbox = new Mailbox(
            '{local-max.com:143/imap/novalidate-cert/debug}', // IMAP server and mailbox folder
            // '{local-max.com:993/imap/ssl/novalidate-cert}INBOX', // IMAP server and mailbox folder
            '<EMAIL>', // Username for the before configured mailbox
            '123', // Password for the before configured username

            __DIR__, // Directory, where attachments will be saved (optional)
            'UTF-8', // Server encoding (optional)
            true, // Trim leading/ending whitespaces of IMAP path (optional)
            false // Attachment filename mode (optional; false = random filename; true = original filename)
        );

        // set some connection arguments (if appropriate)
        $mailbox->setConnectionArgs(
            CL_EXPUNGE // expunge deleted mails upon mailbox close
            // | OP_SECURE // don't do non-secure authentication
            , 0, []
        );
        // $mailbox->setAttachmentsIgnore(true);
        try {
            // Get all emails (messages)
            // PHP.net imap_search criteria: http://php.net/manual/en/function.imap-search.php
            $mailsIds = $mailbox->searchMailbox('ALL');
            print_r($mailsIds);
        } catch (ConnectionException  $ex) {
            echo "IMAP connection failed: " . implode(",", $ex->getErrors('all'));
            die();
        }

        // If $mailsIds is empty, no emails could be found
        if (!$mailsIds) {
            die('Mailbox is empty');
        }


        $mail = $mailbox->getMail($mailsIds[2]);

        // Show, if $mail has one or more attachments
        echo "\nMail has attachments? ";
        if ($mail->hasAttachments()) {
            echo "Yes\n";
        } else {
            echo "No\n";
        }

        // print_r($mail);

        // Print all information of $mail
        print_r($mail->subject);
        print_r($mail->mailboxFolder);
        print_r($mail->textHtml);
        print_r($mail->textPlain);

        $attachments = $mail->getAttachments();
        foreach ($attachments as $key => $attachment) {
            print_r($attachment->id . ', ' . $attachment->mimeType . ', ' . $attachment->filePath . PHP_EOL);
        }
    }
}


/*


The imap_search function has a CRITERIA attribute you can use to limit the messages in a number of ways:

ALL - return all messages matching the rest of the criteria
ANSWERED - match messages with the \ANSWERED flag set
BCC "string" - match messages with "string" in the Bcc: field
BEFORE "date" - match messages with Date: before "date"
BODY "string" - match messages with "string" in the body of the message
CC "string" - match messages with "string" in the Cc: field
DELETED - match deleted messages
FLAGGED - match messages with the \FLAGGED (sometimes referred to as Important or Urgent) flag set
FROM "string" - match messages with "string" in the From: field
KEYWORD "string" - match messages with "string" as a keyword
NEW - match new messages
OLD - match old messages
ON "date" - match messages with Date: matching "date"
RECENT - match messages with the \RECENT flag set
SEEN - match messages that have been read (the \SEEN flag is set)
SINCE "date" - match messages with Date: after "date"
SUBJECT "string" - match messages with "string" in the Subject:
TEXT "string" - match messages with text "string"
TO "string" - match messages with "string" in the To : UNANSWERED - match messages that have not been answered
UNDELETED - match messages that are not deleted
UNFLAGGED - match messages that are not flagged
UNKEYWORD "string" - match messages that do not have the keyword "string"
UNSEEN - match messages which have not been read yet


 */
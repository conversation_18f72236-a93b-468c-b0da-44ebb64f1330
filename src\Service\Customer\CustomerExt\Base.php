<?php

declare(strict_types=1);

namespace App\Service\Customer\CustomerExt;

use App\Service\BaseService;
use App\Repository\Customer\CustomerExtRepository;
use Slim\Container;

abstract class Base extends BaseService
{
    protected CustomerExtRepository $customerExtRepository;

    public function __construct(Container $container)
    {
        $this->customerExtRepository = $container->get(CustomerExtRepository::class);
    }

    public function getCustomerExtRepository()
    {
        return $this->customerExtRepository;
    }
}


<?php

declare(strict_types=1);

namespace App\Controller\User;

use App\Exception\User;
use App\Lib\Func;
use App\Repository\UserRepository;
use Slim\Http\Request;
use Slim\Http\Response;

final class Create extends Base
{
    public function __invoke(Request $request, Response $response): Response
    {
        $input = (array)$request->getParsedBody();

        $newData = array_merge($input, [
            'email' => $input['email'] ?? '',
            'username' => $input['username'] ?? '',
            'name' => $input['name'] ?? '',
            'status' => $input['status'] ?? 0,
            'password' => $input['password'] ?? '',
            'role' => $input['role'] ?? \App\Models\User::ROLE_USER_NORMAL,
        ]);

        $repo = $this->getCreateUserService()->getUserRepository();
        if (!$newData['email'] || !$newData['username'] || !$newData['name'] || !$newData['password']) {
            User::raiseInvalidRequest('Required field missing!');
        }

        $exists = \App\Models\User::query()
            ->where('username', $newData['username'])
            ->orWhere('email', $newData['email'])->count() > 0;

        if ($exists) {
            User::raiseInvalidRequest('Username or Email already exists.');
        }

        $nonce = $repo->getNonce();
        $newData['nonce'] = $nonce;
        if (key_exists('password', $input)) {
            $newData['password'] = Func::passwordHash($input['password'] ?? '');
        }

        if ($newData['initials'] ?? null) {
            $newData['initials'] = strtoupper($newData['initials']);
        }

        $user = $this->getCreateUserService()->create($newData);

        return $this->jsonResponse($response, 'success', $user, 201);
    }
}

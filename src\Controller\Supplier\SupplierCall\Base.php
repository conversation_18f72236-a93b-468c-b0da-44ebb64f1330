<?php

declare(strict_types=1);

namespace App\Controller\Supplier\SupplierCall;

use App\Controller\BaseController;
use App\Exception\File;
use App\Service\Supplier\SupplierCall\SupplierCallService;
use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\UploadedFileInterface;
use Slim\Container;

abstract class Base extends BaseController
{
    public SupplierCallService $supplierCallService;

    /**
     * @var UploadedFileInterface[]
     */
    public ?array $uploadedFiles = null;

    public function __construct(Container $container)
    {
        parent::__construct($container);
        $this->supplierCallService = $container->get(SupplierCallService::class);
    }

    /**
     * @throws File
     */
    public function validateUploadedFiles(RequestInterface &$request, $key='files')
    {
        $this->uploadedFiles = $request->getUploadedFiles()[$key] ?? null;
        if ($this->uploadedFiles) {
            foreach ($this->uploadedFiles as $x) {
                if ($x->getError() !== UPLOAD_ERR_OK) {
                    throw new \App\Exception\File('Upload Error! Code: ' . $x->getError(), 500);
                }
            }
        }

        return $this->uploadedFiles;
    }
}

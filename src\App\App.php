<?php

declare(strict_types=1);

if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}
if (!defined('APP_PATH')) {
    define('APP_PATH', __DIR__ . '/../../public');
}
if (!defined('APP_SRC_PATH')) {
    define('APP_SRC_PATH', __DIR__ . '/..');
}
if (!defined('PRIVATE_DATA_PATH')) {
    define('PRIVATE_DATA_PATH', __DIR__ . '/../../public/data');
}

require __DIR__ . '/../../vendor/autoload.php';
$baseDir = __DIR__ . '/../../';
$dotenv = Dotenv\Dotenv::createImmutable($baseDir);
$envFile = $baseDir . '.env';
if (file_exists($envFile)) {
    $dotenv->load();
}
$dotenv->required(['DB_HOST', 'DB_NAME', 'DB_USER', 'DB_PASS', 'DB_PORT']);
$settings = require __DIR__ . '/Settings.php';

$app = new \Slim\App($settings);

(require __DIR__ . '/Cors.php')($app);
$container = $app->getContainer();

require __DIR__ . '/Dependencies.php';
require __DIR__ . '/Repositories.php';
require __DIR__ . '/Services.php';

$container['view'] = function ($container) {
    return new \Slim\Views\PhpRenderer(APP_SRC_PATH . '/View');
};

require __DIR__ . '/Routes.php';







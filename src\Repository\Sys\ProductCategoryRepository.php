<?php

declare(strict_types=1);

namespace App\Repository\Sys;

use App\Lib\Func;
use App\Lib\FuncModel;
use App\Models\Sys\ProductCategory;
use App\Repository\BaseRepositoryORM;
use Illuminate\Database\Eloquent\Builder;

final class ProductCategoryRepository extends BaseRepositoryORM
{
    public function getQueryProductCategorysByPage($params = []): Builder
    {
        $qb = $this->getQueryBuilder();

        if (!empty($params)) {
            if ($params['name'] ?? null){
                $qb->where('name', 'like', FuncModel::likeValue($params['name']));
            }
            if ($params['keyWords'] ?? null){
                $qb->where('name', 'like', FuncModel::likeValue($params['keyWords']));
            }


            $qb = $this->applyOrderBy($qb, $params);
        }

        return $qb;
    }

    /**
     * Get lists by pagination.
     *
     * @param int $page
     * @param int $perPage
     * @param array|null $params
     * @return array
     */
    public function getProductCategorysByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        $query = $this->getQueryProductCategorysByPage($params);
        $total = $this->getCountByQuery($query);

        $result = $this->getResultsWithPagination(
            $query,
            $page,
            $perPage,
            $total
        );

        return $result;
    }

    public function getQueryBuilder(): Builder
    {
        return ProductCategory::query();
    }
}
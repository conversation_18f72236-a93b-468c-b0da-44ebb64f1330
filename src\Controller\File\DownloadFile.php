<?php

declare(strict_types=1);

namespace App\Controller\File;

use App\App\Constants;
use App\Lib\FileLib;
use App\Lib\Func;
use App\Models\File;
use Slim\Http\Request;
use Slim\Http\Response;

final class DownloadFile extends Base
{
    public function __invoke(Request $request, Response $response, array $args): Response
    {
        $params = $request->getParams();
        $idSecured = $params['id'];
        // security key version.
        $kv = $params['kv'] ?? 1;

        $id = Func::mcrypt('decrypt', $idSecured, ...Constants::FILE_SEC_KEYS[$kv]);
        if (!$id) {
            \App\Exception\Base::raiseInvalidRequest('File not found or invalid request.');
        }

        $id = Func::safeInt($id);
        if (!$id) {
            \App\Exception\Base::raiseInvalidRequest('File ID is invalid.');
        }

        /** @var File $file */
        $file = File::findOrFail($id);
        $file->increment('hits');


        $key = $params['key'] ?? null;
        $type = $params['type'] ?? null;
        $force = $params['force'] ?? null;


        $filePath = $file->abs_path;
        if (!file_exists($filePath)) {
            \App\Exception\Base::raiseInvalidRequest('File not exists. Absolute path: ' . $filePath);
        }

        $tmpFileLib = new FileLib('');
        $mime = $tmpFileLib->mime2ext($file->type, true);

        $info = pathinfo($filePath);
        $filename = $info['basename'];
        $x = explode('.', $filename);
        $extension = end($x);

        $filesize = @filesize($filePath);

        /*if ($set_mime === TRUE) {
            if (count($x) === 1 or $extension === '') {
                /* If we're going to detect the MIME type,
                 * we'll need a file extension.
                 * /
                return;
            }
        }*/

        /* It was reported that browsers on Android 2.1 (and possibly older as well)
         * need to have the filename extension upper-cased in order to be able to
         * download it.
         *
         * Reference: http://digiblog.de/2011/04/19/android-and-the-download-file-headers/
         */
        if (count($x) !== 1 && isset($_SERVER['HTTP_USER_AGENT']) && preg_match('/Android\s(1|2\.[01])/', $_SERVER['HTTP_USER_AGENT'])) {
            $x[count($x) - 1] = strtoupper($extension);
            $filename = implode('.', $x);
        }

        if (($fp = @fopen($filePath, 'rb')) === FALSE) {
            \App\Exception\Base::raiseInvalidRequest('Internal server error while opening file.', 500);
        }

        // Clean output buffer
        if (ob_get_level() !== 0 && @ob_end_clean() === FALSE) {
            @ob_clean();
        }

        // Generate the server headers
        header('Content-Type: ' . $mime);
        if ($force)
            header('Content-Disposition: attachment; filename="' . $filename . '"');
        else
            header('Content-Disposition: inline; filename="' . $filename . '"');

        header('Expires: 0');
        header('Content-Transfer-Encoding: binary');
        header('Content-Length: ' . $filesize);
        header('Cache-Control: private, no-transform, no-store, must-revalidate');

        // Flush 1MB chunks of data
        while (!feof($fp) && ($data = fread($fp, 1048576)) !== FALSE) {
            echo $data;
        }

        fclose($fp);

        exit;

    }
}

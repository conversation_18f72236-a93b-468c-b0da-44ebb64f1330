<?php

declare(strict_types=1);

namespace App\Service\Customer\CustomerCall;

use App\Service\BaseService;
use App\Repository\Customer\CustomerCallRepository;
use Slim\Container;

abstract class Base extends BaseService
{
    protected CustomerCallRepository $customerCallRepository;

    public function __construct(Container $container)
    {
        $this->customerCallRepository = $container->get(CustomerCallRepository::class);
    }

    public function getCustomerCallRepository()
    {
        return $this->customerCallRepository;
    }
}


<?php

declare(strict_types=1);

namespace App\Controller\Email\Email;

use App\Lib\Func;
use App\Lib\SysMsg;
use App\Models\Email\Email;
use Slim\Http\Request;
use Slim\Http\Response;

final class GetNextEmail extends Base
{
    public function __invoke(Request $request, Response $response, array $args): Response
    {
        $params = $request->getParams();
        $dir = Func::safeInt($params['dir'] ?? 1);
        $uid = $params['uid'] ?? '';

        $qbBase = $this->emailService->getEmailRepository()->getQueryEmailsByPage($params);
        $qbBase->selectRaw("id")->limit(1);
        if (($params['is_hidden'] ?? null) !== null) {
            $qbBase->where('is_hidden', $params['is_hidden']);
        }

        $qb = $qbBase->clone();
        $qb->where('id', '!=', $uid);
        $baseDate = null;
        if ($uid) {
            $email = Email::query()->find($uid);
            if ($email) {
                $baseDate = $email->date;
            }
        }

        if ($dir == 1) {
            $qb->orderBy('date', 'DESC')->orderBy('id', 'DESC');
            if ($baseDate) $qb->where('date', '<=', $baseDate);
            $nextUid = $qb->value('id');

            // Disable cycled navigation
            /*if (!$nextUid) {
                $maxDate = $qbBase->max('date');
                $nextUid = $qbBase->where('date', $maxDate)->value('id');
            }*/
        } else {
            $qb->orderBy('date')->orderBy('id');
            if ($baseDate) $qb->where('date', '>=', $baseDate);
            $nextUid = $qb->value('id');

            // Disable cycled navigation
            /*if (!$nextUid) {
                $minDate = $qbBase->min('date');
                $nextUid = $qbBase->where('date', $minDate)->value('id');
            }*/
        }
        if (!$nextUid) {
            $nextUid = $uid;
            SysMsg::get_instance()->info("No next/prev email.");
        }

        return $this->jsonResponse($response, 'success', $nextUid, 200);
    }
}

<?php

declare(strict_types=1);

namespace App\Controller\Customer\CustomerContact;

use App\Controller\BaseController;
use App\Service\Customer\CustomerContact\CustomerContactService;
use Respect\Validation\Validator as v;
use Slim\Container;

abstract class Base extends BaseController
{
    public CustomerContactService $customerContactService;

    public function __construct(Container $container)
    {
        parent::__construct($container);
        $this->customerContactService = $container->get(CustomerContactService::class);
    }

	public function validate(array $input): bool {
        $validator = v::key('name', v::stringType()->length(1, 30))
            ->key('value', v::numeric()->addRule(v::min(0))->addRule(v::max(100)));

        if (!$validator->validate($input)) {
            \App\Exception\Base::raiseInvalidRequest();
        }
        return true;
    }
}

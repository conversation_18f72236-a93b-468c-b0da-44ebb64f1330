<?php

declare(strict_types=1);

namespace App\Repository\Customer;

use App\Lib\Func;
use App\Models\Customer\CustomerExt;
use App\Repository\BaseRepositoryORM;
use Illuminate\Database\Eloquent\Builder;

final class CustomerExtRepository extends BaseRepositoryORM
{
    private function getQueryCustomerExtsByPage($params = []): Builder
    {
        $qb = $this->getQueryBuilder();

        if (!empty($params)) {
            if (Func::keyExistsInWithParam($params, 'customer')) {
                $qb->with('customer');
            }

            $qb = $this->applyOrderBy($qb, $params);
        }

        return $qb;
    }

    /**
     * Get lists by pagination.
     *
     * @param int $page
     * @param int $perPage
     * @param array|null $params
     * @return array
     */
    public function getCustomerExtsByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        $query = $this->getQueryCustomerExtsByPage($params);
        $total = $this->getCountByQuery($query);

        $result = $this->getResultsWithPagination(
            $query,
            $page,
            $perPage,
            $total
        );

        return $result;
    }

    public function getQueryBuilder(): Builder
    {
        return CustomerExt::query();
    }
}
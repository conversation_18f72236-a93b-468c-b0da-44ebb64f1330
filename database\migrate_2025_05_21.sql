CREATE TABLE `supplier_call_file`
(
    `call_id`         int(10) unsigned NOT NULL,
    `file_id`        bigint(20) unsigned NOT NULL,
    PRIMARY KEY (`call_id`, `file_id`),
    <PERSON>EY `FK_supplier_call_file_file_id` (`file_id`),
    CONSTRAINT `FK_supplier_call_file_call_id` FOREIGN KEY (`call_id`) REFERENCES `supplier_call` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `FK_supplier_call_file_file_id` FOREIGN KEY (`file_id`) REFERENCES `file` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;


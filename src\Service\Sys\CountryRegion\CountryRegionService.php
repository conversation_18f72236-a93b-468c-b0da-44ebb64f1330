<?php

declare(strict_types=1);

namespace App\Service\Sys\CountryRegion;

use App\Models\Sys\CountryRegion;

final class CountryRegionService extends Base
{
    public function create(array $input): CountryRegion
    {
        return CountryRegion::query()->create($input);
    }

    public function update($input, $id): CountryRegion
    {
        $input['id'] = $id;
        return CountryRegion::query()->update($input);
    }

    public function getOne(int $id, array $params=[]): CountryRegion
    {
        return $this->countryRegionRepository->getQueryBuilder()->where('id', $id)->first();
    }

    public function getCountryRegionsByPage(
        int $page,
        int $perPage,
        ?array $params
    ): array
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($perPage < 1) {
            $perPage = self::DEFAULT_PER_PAGE_PAGINATION;
        }

        return $this->countryRegionRepository->getCountryRegionsByPage(
            $page,
            $perPage,
            $params
        );
    }

    public function delete($ids): void
    {
        $arr = is_array($ids) ? $ids : explode(',', $ids);
        $this->countryRegionRepository->getQueryBuilder()->whereIn('id', $arr)->delete();
    }
}

<?php

namespace App\Models\Supplier;

use App\Models\BaseModel;
use App\ModelsOrg\OrgSupplier;

/**
 * @property integer $id
 * @property string $name
 * @property string $internal_name
 * @property string $org_a
 * @property integer $is_active
 * @property string $created_on
 * @property integer $created_by
 * @property string $updated_on
 * @property integer $updated_by

 * @property SupplierAddress $address
 * @property SupplierExt $ext
 * @property SupplierCall[] $calls
 * @property SupplierCall[] $meta
 * @property SupplierMeta[] $productCategories
 * @property SupplierMeta[] $productTrademarks
 * @property SupplierContact[] $contacts
 * @property SupplierContact[] $contactsAll
 * @property SupplierInfo $info
 *
 * @property string $fullname
 */
class Supplier extends BaseModel
{
    protected $connection = 'default';

    public $timestamps = true;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'supplier';

    /**
     * The "type" of the auto-incrementing ID.
     * 
     * @var string
     */
    protected $keyType = 'string';

    /**
     * Indicates if the IDs are auto-incrementing.
     * 
     * @var bool
     */
    public $incrementing = true;

    /**
     * @var array
     */
    protected $fillable = ['internal_name', 'name', 'org_a', 'is_active', 'created_on', 'created_by', 'updated_on', 'updated_by'];


    protected $casts = [

    ];

    public static function getBoundData(&$item, $applyReverseCast = false): array
    {
        $data = parent::getBoundData($item, $applyReverseCast);

        $data['is_active'] = intval($data['is_active'] ?? 1);

        return $data;
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function address()
    {
        return $this->hasOne('App\Models\Supplier\SupplierAddress', 'supplier_id', 'id');
    }

    /**
     * Supplier Extension table.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function ext()
    {
        return $this->belongsTo('App\Models\Supplier\SupplierExt', 'id', 'supplier_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function calls()
    {
        return $this->hasMany('App\Models\Supplier\SupplierCall',  'supplier_id', 'id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function meta()
    {
        return $this->hasMany('App\Models\Supplier\SupplierMeta', 'supplier_id', 'id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function productCategories()
    {
        return $this->hasOne('App\Models\Supplier\SupplierMeta', 'supplier_id', 'id')
            ->where('type', SupplierMeta::TYPE_PRODUCT_CATEGORY);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function productTrademarks()
    {
        return $this->hasOne('App\Models\Supplier\SupplierMeta', 'supplier_id', 'id')
            ->where('type', SupplierMeta::TYPE_PRODUCT_TRADEMARK);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function supplierType()
    {
        return $this->hasOne('App\Models\Supplier\SupplierMeta', 'supplier_id', 'id')
            ->where('type', SupplierMeta::TYPE_SUPPLIER);
    }


    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function contacts()
    {
        return $this->hasMany('App\Models\Supplier\SupplierContact')->active()->sortDefault();
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function contactsAll()
    {
        return $this->hasMany('App\Models\Supplier\SupplierContact');
    }

    /**
     * Supplier Extension table.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function info()
    {
        return $this->hasOne('App\Models\Supplier\SupplierInfo', 'id', 'supplier_id');
    }

    /**
     * Supplier Extension table.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function orgSupplier()
    {
        return $this->hasOne(OrgSupplier::class, 'supp_supplier_id', 'id');
    }
}

<?php

namespace App\Lib;

use App\App\Constants;
use App\Models\User;
use GuzzleHttp\Client;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Middleware;
use GuzzleRetry\GuzzleRetryMiddleware;
use Illuminate\Database\Connection;
use Monolog\Logger;
use Slim\Container;


/**
 * Class Func: Utility functions here.
 *
 * @package App\Lib
 * <AUTHOR>
 */
final class Func
{
    static function refineEan($str, $pattern = ' 0'): string
    {
        if (!$str) return '';
        $str = ltrim($str, $pattern);

        $posOfFirstDot = strpos($str, ',');
        if ($posOfFirstDot !== false) {
            $str = substr($str, 0, $posOfFirstDot);
        }

        return $str;
    }

    static function refineEan2($str, $pattern = ' 0'): string
    {
        if (!$str) return '';
        $str = ltrim($str, $pattern);

        $posOfFirstDot = strpos($str, ',');
        if ($posOfFirstDot !== false) {
            $str = substr($str, 0, $posOfFirstDot);
        }
        return $str;
    }

    static function genApiKey($prefix, $length = 12)
    {
        $alphabet = 'abcdefghijklmnopqrstuvwxyz1234567890';
        $pass = array(); //remember to declare $pass as an array
        $alphaLength = strlen($alphabet) - 1; //put the length -1 in cache
        for ($i = 0; $i < $length; $i++) {
            $n = rand(0, $alphaLength);
            $pass[] = $alphabet[$n];
        }
        $body = implode($pass); //turn the array into a string
        return sprintf('%04d', (Func::safeInt($prefix) + 2100) % 10000) . $body;
    }

    static function randomPassword($length = 10)
    {
        $alphabet = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890--!=!@@#++%';
        $pass = array(); //remember to declare $pass as an array
        $alphaLength = strlen($alphabet) - 1; //put the length -1 in cache
        for ($i = 0; $i < $length; $i++) {
            $n = rand(0, $alphaLength);
            $pass[] = $alphabet[$n];
        }
        return implode($pass); //turn the array into a string
    }

    static function randomTs()
    {
        $ts = time() - 1668400000;
        return $ts;
        // return intval(sprintf("%d%02d", $ts, rand(1, 99)));
    }

    /**
     * Convert 2020-04-20           -> timestamp (seconds)
     *
     * @param $str
     * @param string $srcFormat
     * @return int|null
     */
    public static function dtStrToTimeYmd($str, $srcFormat = "Y-m-d"): ?int
    {
        if (!$str) return null;
        if ($str == DATE_EMPTY_STR) return null;

        $dt = date_create_from_format($srcFormat, $str);
        if ($dt == false) {
            return null;
        } else {
            return $dt->getTimestamp();
        }
    }

    public static function dtStrToValidDbStr($str, $format = null)
    {
        $dt = date_create_from_format($format ?? DATE_FORMAT_YMD_HIS, $str);
        if ($dt == false) {
            return null;
        } else {
            return $str;
        }
    }

    public static function dtToday($format = DATE_FORMAT_YMD)
    {
        return date($format);
    }

    public static function dtTzStrToDBFormat($str, $format = DATE_RFC3339_EXTENDED)
    {
        return self::dtDbDatetimeStr(self::dtStrToTimeYmd($str, $format));
    }

    /**
     * @param $var  string | number
     * @param $day_offset
     * @param bool $isTimestamp
     * @return false|int|string
     */
    public static function dtDateByOffset($var, $day_offset, $isTimestamp = FALSE)
    {
        if (is_string($var) && strlen($var) == 10) {
            $var = self::dtStrToTimeYmd($var);
        }
        if (!$var) return '';
        if (is_numeric($var)) {
            $ts = strtotime("$day_offset day", $var);
            if ($ts) {
                if ($isTimestamp) return $ts;
                return date(DATE_FORMAT_YMD, $ts);
            }
        }
        return '';
    }

    public static function dtDbDatetimeStr($ts = null, $format = DATE_FORMAT_YMD_HIS): string
    {
        if (!$ts) $ts = time();
        return date($format, $ts);
    }

    public static function isAddressEqual($address1, $address2): bool
    {
        return strtolower($address1) == strtolower($address2);
    }

    /**
     * Parse numeric string in German Format or English Format.
     *
     * @param $val
     * @return float
     */
    public static function floatValueG($val)
    {
        $val = str_replace(",", ".", $val);
        $val = preg_replace('/\.(?=.*\.)/', '', $val);
        return floatval($val);
    }

    public static function safeInt($val, $defaultValue = 0)
    {
        if (gettype($val) == 'boolean') return $val ? 1 : 0;
        if (gettype($val) == 'integer') return $val;
        if ($val === null) return $defaultValue;

        return intval($val);
    }

    public static function safeDouble($val, $defaultValue = 0)
    {
        if (gettype($val) == 'double' || gettype($val) == 'integer') return $val;

        if (is_numeric($val)) {
            return floatval($val);
        }

        return self::floatValueG($val);
    }

    public static function toDouble($val, $isGermanFormat = false)
    {
        if (gettype($val) === 'string') {
            if ($isGermanFormat) $val = str_replace(',', '.', str_replace('.', '', $val));
        }
        return self::safeDouble($val);
    }

    public static function safeHtml($string)
    {
        // Strip HTML Tags
        $clear = strip_tags($string);
        // Clean up things like &amp;
        $clear = html_entity_decode($clear);
        // Strip out any url-encoded stuff
        $clear = urldecode($clear);
        // Replace non-AlNum characters with space
        // $clear = preg_replace('/[^A-Za-z0-9]/', ' ', $clear);
        // Replace Multiple spaces with single space
        $clear = preg_replace('/ +/', ' ', $clear);
        // Trim the string of leading/trailing space
        $clear = trim($clear);
        return $clear;
    }

    public static function safeTimestamp($val)
    {
        return self::safeInt($val, 10, DT_MAX_TS);
    }

    /**
     * Parse jsonable parameter to array or object
     *
     * @param $value
     * @return array|mixed|null
     */
    public static function safeJson($value)
    {
        if (!is_null($value)) {
            if (is_array($value)) return $value;
            if (is_object($value)) return $value;
        }
        try {
            if ($value)
                return json_decode($value, true);
        } catch (\Exception $exception) {

        }
        return NULL;
    }

    public static function bn2Dec($value, $decimal = 18)
    {
        $dividend = (string)$value;
        $divisor = (string)'1' . str_repeat('0', $decimal);
        try {
            return bcdiv($dividend, $divisor, $decimal);
        } catch (\Exception $e) {

        }
        return 0;
    }

    public static function passwordHash($password): string
    {
        return password_hash($password, PASSWORD_DEFAULT);
    }

    public static function verifyPassword($password, $hash): bool
    {
        return password_verify($password, $hash);
    }

    public static function parseDetailedParam($str)
    {
        $arr = Func::csvToArr($str);
        foreach ($arr as $x) {

        }

        return $arr;
    }

    /**
     * @param $params   array
     * @param mixed      string
     * @param $fieldName    string
     * @return bool
     */
    public static function keyExistsInWithParam(array $params, mixed $key, string $fieldName = 'with'): bool
    {
        $with = $params[$fieldName] ?? '';
        if (!$with) return false;

        if (is_string($key)) {
            if (str_contains("," . $with . ",", "," . $key . ",")) {
                return true;
            }
        } else if (is_array($key)) {
            foreach ($key as $x) {
                if (str_contains("," . $with . ",", "," . $x . ",")) {
                    return true;
                }
            }
        }
        return false;
    }

    public static function getFileNames($filename, $returnSafe = true)
    {
        $ext = pathinfo($filename, PATHINFO_EXTENSION);
        if ($ext) {
            $nameOnly = substr($filename, 0, -strlen($ext) - 1);
        } else {
            $nameOnly = $filename;
        }

        return $returnSafe ? self::getSeoUniqueName($nameOnly) : $nameOnly;
    }

    public static function getSafeFilePath($filename, $divider = '-', $incTimestamp = false)
    {
        $ext = pathinfo($filename, PATHINFO_EXTENSION);
        if ($ext) {
            $nameOnly = substr($filename, 0, -strlen($ext) - 1);
        } else {
            $nameOnly = $filename;
        }
        $nameOnly = self::getSeoUniqueName($nameOnly, $divider, $incTimestamp);

        return $ext ? $nameOnly . '.' . $ext : $nameOnly;
    }

    public static function getPixelImageHtml($id, $sender, $receiver)
    {
        /** @var Container $container */
        $container = Func::getContainer();
        if ($container && ($mediaBaseUrl = $container->get('settings')['mediaApiUrl'] ?? '')) {
            $mediaBaseUrl .= '/pixel-image';

            $kv = 1;
            $keys = Constants::FILE_SEC_KEYS[$kv];
            $url = "?id=" . Func::mcrypt('encrypt', $id ?? '', ...$keys);
            $url .= "&e1=" . Func::mcrypt('encrypt', $sender ?? '', ...$keys);
            $url .= "&e2=" . Func::mcrypt('encrypt', $receiver ?? '', ...$keys);
            $url .= "&e3=supplier";
            $url .= "&kv=$kv";

            return '<img src="' . $mediaBaseUrl . $url . '" />';
        }
        return '';
    }

    public static function getSeoUniqueName($name, string $divider = '-', $incTimestamp = false)
    {
        // replace non letter or digits by divider
        $text = preg_replace('~[^\pL\d]+~u', $divider, $name);

        // transliterate
        $text = iconv('utf-8', 'us-ascii//TRANSLIT', $text);

        // remove unwanted characters
        $text = preg_replace('~[^-\w]+~', '', $text);

        // trim
        $text = trim($text, $divider);

        // remove duplicate divider
        $text = preg_replace('~-+~', $divider, $text);

        // lowercase
        $text = strtolower($text);

        if (empty($text)) {
            $text = $name;
        }
        if ($incTimestamp) {
            $text .= $divider . time();
        }

        return $text;
    }

    public static function toUtf8($text)
    {
        return $text;
//        return mb_convert_encoding($text, 'UTF-16LE', 'UTF-8');
    }

    /**
     * return the safe array for any input as string | null | array
     *
     * @param $strSet
     * @return array
     */
    public static function csvToArr($strSet, $delimiter = ','): array
    {
        if (!$strSet) return [];
        if (is_numeric($strSet)) return [$strSet];
        return is_string($strSet) ? explode($delimiter, $strSet) : $strSet;
    }


    public static function arrToCsv($value, int $flags = SORT_ASC)
    {
        if ($value) {
            if (is_array($value)) {
                $values = array_values($value);
                if ($values) {
                    sort($values, $flags);
                }
                return implode(',', array_values($values));
            } else
                return '';
        } else {
            return '';
        }
    }

    /**
     * Remove a $val from a string value in CSV.
     *
     * @param $strSet
     * @param $val
     * @return string
     */
    public static function removeItemInCsvSet($strSet, $val)
    {
        if (!$strSet) return '';
        $arr = is_string($strSet) ? explode(',', $strSet) : $strSet;
        $index = array_search($val, $arr);
        if ($index !== false)
            unset($arr[$index]);
        return implode(',', $arr);
    }

    public static function addItemInCsvSet($strSet, $val)
    {
        if (!$strSet) return $val;
        $arr = is_string($strSet) ? explode(',', $strSet) : $strSet;
        if (!in_array($val, $arr)) {
            $arr[] = $val;
        }
        asort($arr);
        return implode(',', array_values($arr));
    }

    /**
     * @param ItemEan $ean We assume that $ean has all relation data.
     * @return float|int
     */
    public static function getEANGrossPrice(ItemEan $ean, $priceTypeId = 1, $multiplyByCaseQty = false): float|int
    {
        $grossEanPrice = 0;
        $prices = $ean->eanPrices?->toArray();
        if ($prices) {
            $standardPriceRow = current(array_filter($prices, function ($x) use ($priceTypeId) {
                return $x['price_type_id'] == $priceTypeId;
            }));
            $vat = $ean->item?->vat?->value ?? 0;
            $grossEanPrice = ($standardPriceRow['price'] ?? 0) * (1 + $vat / 100);
            // if (!$isSingleEan && $multiplyByCaseQty) $grossEanPrice * $ean->attr_case_qty;
            // if (!$isSingleEan && $multiplyByCaseQty) $grossEanPrice * $ean->attr_case_qty;
            $grossEanPrice = round($grossEanPrice, 2);
        }

        return $grossEanPrice;
    }

    public static function getEANGrossPrices(ItemEan $ean): array
    {
        return [self::getEANGrossPrice($ean, 1), self::getEANGrossPrice($ean, 2)];
    }

    public static function nf($number, $is_decimal = true, $decimal = 2, $empty_for_zero = false)
    {
        if ($empty_for_zero && !$number) {
            return '';
        }
        if (is_string($number)) {
            $number = floatval($number);
        }
        if (!is_numeric($number)) {
            $number = 0;
        }
        if (floor(pow(10, $decimal) * $number) == 0) return $empty_for_zero ? '' : 0;

        if ($is_decimal) {
            return number_format($number, $decimal, ',', '.');
        } else {
            return number_format($number, 0, ',', '.');
        }
    }

    public static function nf2($number, $is_decimal = true, $decimal = 2)
    {
        if ($number === null) {
            return '';
        }

        return self::nf($number, $is_decimal, $decimal);
    }

    public static function nfInt($number, $empty_for_zero = false)
    {
        return self::nf($number, false, 0, $empty_for_zero);
    }

    /**
     * Convert German number format while keeping decimals.
     *
     * @param $number
     * @return mixed|void
     */
    public static function nfAlt($number)
    {
        if (!$number || !is_numeric($number)) return $number;

        $decimalsPos = strpos($number, '.');
        $decimals = $decimalsPos === false ? 0 : strlen($number) - strpos($number, '.') - 1;

        return self::nf($number, true, $decimals);
    }

    /**
     * Parse Email string to get Name and Email in an array
     * e.g. 'Aslam Doctor <<EMAIL>>' => ['<EMAIL>', 'Aslam Doctor']
     * @param $emailString
     * @return array
     */
    public static function parseEmailStr($emailString)
    {
        $split = explode(' <', $emailString);
        if (count($split) > 1) {
            $name = trim($split[0]);
            $email = trim(rtrim($split[1] ?? '', '>'));
        } else {
            $email = trim($split[0]);
            $name = '';
        }
        if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return [$email, $name];
        } else return [];
    }

    public static function parseEmailStrList($emailString)
    {
        $toArr = Func::csvToArr($emailString);
        $validEmails = [];
        if ($toArr) {
            foreach ($toArr as $to) {
                $to = trim($to);
                $emailArr = self::parseEmailStr($to);
                if ($emailArr) {
                    $validEmails[] = $emailArr;
                }
            }
        }
        return $validEmails;
    }

    public static function buildEmailStr($email, $name)
    {
        return $name ? $name . " <" . $email . ">" : $email;
    }

    public static function removeNonAsciiCode($value)
    {
        // enabled Tab, newLine
        return preg_replace('/[\x00-\x09\x0B-\x1F\x7E]/', '', $value);
    }


    /**
     *-----------------------------------------------------
     * global DB & Logger
     *-----------------------------------------------------
     */
    public static function getContainer(): Container
    {
        global $container;
        return $container;
    }

    public static function getDb($name = null): Connection|null
    {
        global $container;
        return $container->has('dbORM') ? $container->get('dbORM')?->getConnection($name) : null;
    }

    public static function dbQuote($value, int $type = \PDO::PARAM_STR): string|false
    {
        return self::getDb()->getPdo()->quote($value, $type);
    }

    public static function getLogger(): Logger|null
    {
        global $container;
        return $container->has('logger') ? $container->get('logger') : null;
    }

    public static function getBaseUrl()
    {
        $baseUrl = str_replace('/uploads', '', self::getContainer()->get('settings')['mediaBaseUrl']);
        return $baseUrl;
    }

    public static function getBaseUrlStable()
    {
        $host = $_SERVER['SERVER_NAME']; //$_SERVER['SERVER_ADDR'];
        $port = $_SERVER['SERVER_PORT'];
        $scheme = strtolower($_SERVER['REQUEST_SCHEME']);

        $relPath = dirname($_SERVER['PHP_SELF']);

        $baseUrl = $scheme . '://' . $host;
        if (($scheme == 'http' && $port == 80) || ($scheme == 'https' && $port == 443)) {

        } else {
            $baseUrl .= ':' . $port;
        }
        $baseUrl .= $relPath;
        $baseUrl = rtrim($baseUrl, "/") . "/";

        return $baseUrl;
    }



    /**
     *-----------------------------------------------------
     * global session user info
     *-----------------------------------------------------
     */
    public static function getSesUser(): array|null
    {
        global $container;
        return $container?->has('sesUser') ? $container->get('sesUser') : null;
    }

    public static function getSesUserId(): int|null
    {
        $user = self::getSesUser();
        return $user['user_id'] ?? null;
    }

    public static function isSesUserAdmin(): bool
    {
        $user = self::getSesUser();

        return $user && $user['role'] == User::ROLE_ADMIN;
    }

    public static function setSesUser(): array|null
    {
        global $container;
        return $container?->has('sesUser') ? $container->get('sesUser') : null;
    }

    /**
     * MCrypt by OpenSSL
     *
     * @param $action string 'encrypt' | 'decrypt'
     * @param $string
     * @param $secKey
     * @return false|string
     */
    public static function mcrypt(string $action, $string, $secKey, $secret_iv = null)
    {
        $output = false;
        $encrypt_method = "AES-256-CBC";
        $secret_key = $secKey;


        // hash
        $key = hash('sha256', $secret_key);

        // iv - encrypt method AES-256-CBC expects 16 bytes - else you will get a warning
        $iv = substr(hash('sha256', $secret_iv), 0, 16);
        if ($action == 'encrypt') {
            $output = openssl_encrypt($string, $encrypt_method, $key, 0, $iv);
            $output = base64_encode($output);
        } else if ($action == 'decrypt') {
            $output = openssl_decrypt(base64_decode($string), $encrypt_method, $key, 0, $iv);
        }
        return $output;
    }

    /**
     * Get a Guzzle HTTP client
     *
     * @param $extraSettings
     * @return Client
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public static function getHttpClient($extraSettings = []): Client
    {
        global $container;

        $stack = HandlerStack::create();
        $stack->push(GuzzleRetryMiddleware::factory());

        $stack->push(
            Middleware::log(
                $container->get('logger'),
                new LoggerMessageFormatter($container->get('settings')['app']['logLevel'])
            )
        );
        $settings = [
            'handler' => $stack,
            'max_retry_attempts' => 3,
            'default_retry_multiplier' => 1.5,
        ];

        return new Client(array_merge_recursive($settings, $extraSettings));
    }

    /**
     *-----------------------------------------------------
     * Path related
     *-----------------------------------------------------
     */
    public static function pathJoin(...$parts): string
    {
        $ds = DIRECTORY_SEPARATOR;
        if (sizeof($parts) === 0) return '';
        $prefix = ($parts[0] === $ds) ? $ds : '';
        $prefix2 = ($parts[0] && $parts[0][0] === $ds) ? $ds : '';
        $processed = array_filter(array_map(function ($part) use ($ds) {
            return trim(str_replace('/', $ds, $part), $ds);
        }, $parts), function ($part) {
            return !empty($part);
        });
        return $prefix . $prefix2 . implode($ds, $processed);
    }

    public static function validPath($fileName)
    {
        return $fileName !== null && $fileName !== '';
    }

    /**
     * Use slash "/" as a separator
     *
     * @param ...$parts
     * @return string
     */
    public static function pathJoinUrl(...$parts): string
    {
        $ds = '/';
        if (sizeof($parts) === 0) return '';
        $prefix = ($parts[0] === $ds) ? $ds : '';
        $prefix2 = ($parts[0][0] === $ds) ? $ds : '';
        $processed = array_filter(array_map(function ($part) use ($ds) {
            return trim($part, $ds);
        }, $parts), function ($part) {
            return Func::validPath($part);
        });
        return $prefix . $prefix2 . implode($ds, $processed);
    }

    public static function lastDirInPath($path): string
    {
        return substr($path, strrpos($path, '/'));
    }

    /**
     * Replace "\" or "/" with "/".
     */
    public static function pathToUrl($path): array|string
    {
        return str_replace(DIRECTORY_SEPARATOR, '/', $path);
    }

    public static $latestDtFormat = null;

    /**
     * Parse data for DB Date field
     * DD.MM.YYYY
     *
     * @param $val
     * @return string|null
     */
    public static function parseDtStrToDbDtDeep($val): string|null
    {
        if (!$val) return null;
        if (!(is_string($val) || is_numeric($val))) return null;

        try {
            $ts = null;
            if (is_numeric($val)) {
                $ts = \PhpOffice\PhpSpreadsheet\Shared\Date::excelToTimestamp($val);
            } else {
                $ts = self::dtStrToTimeYmd($val, self::$latestDtFormat ?? 'Y-m-d');
                if ($ts) {
                    self::$latestDtFormat = 'Y-m-d';
                } else {
                    // string case.
                    $formats = [
                        'd.m.Y'
                    ];
                    foreach ($formats as $format) {
                        $ts = self::dtStrToTimeYmd($val, $format);
                        if ($ts) {
                            self::$latestDtFormat = $format;
                            break;
                        }
                    }
                }
            }
            if ($ts) return date(DATE_FORMAT_YMD, $ts);
        } catch (Exception $exception) {

        }

        return null;
    }

    public static function refineHtml($html)
    {
        if (!$html) return $html;
        $patterns = ['/( data-bind=".*")*/'];
        return preg_replace($patterns, '', $html);
    }

    /**
     * Character Limiter
     *
     * Limits the string based on the character count.  Preserves complete words
     * so the character count may not be exactly as specified.
     *
     * @param string
     * @param int
     * @param string    the end character. Usually an ellipsis
     * @return    string
     */
    public static function characterLimiter($str, $n = 20, $end_char = '...')
    {
        if (!$str) return $str;

        if (mb_strlen($str) < $n) {
            return $str;
        }

        // a bit complicated, but faster than preg_replace with \s+
        $str = preg_replace('/ {2,}/', ' ', str_replace(array("\r", "\n", "\t", "\v", "\f"), ' ', $str));

        if (mb_strlen($str) <= $n) {
            return $str;
        }

        $out = '';
        $arr = explode(' ', trim($str));
        $endCharLength = mb_strlen($end_char);
        foreach ($arr as $val) {
            $outBk = $out;
            $out .= $val . ' ';

            if (mb_strlen($out) >= $n - $endCharLength + 1) {
                $out = trim($out);
                if (mb_strlen($out . $end_char) > $n) return trim(trim($outBk) . $end_char);
                return (mb_strlen($out) === mb_strlen($str)) ? $out : $out . $end_char;
            }
        }
    }

    /**
     * Split weight into array for shipping.
     * e.g. 57.23kg --> [30, 27.23] (Should be split into 2 parcels!)
     *
     * @param $weight
     * @param int|float $weightPerPack
     * @return array
     */
    public static function splitWeight($weight, float|int $weightPerPack = 30): array
    {
        $arr = [];
        while ($weight > 0) {
            $curWeight = min($weight, $weightPerPack);
            $arr[] = $curWeight;
            $weight -= $curWeight;
        }
        return $arr;
    }


    public static function refineAddress($address, $type = 1)
    {
        $address = trim($address);

        // Address?
        if ($type == 1) {
            if (($len = strlen($address)) >= 3) {
                $last3Chars = mb_substr($address, -3);

                if (is_numeric($last3Chars[0]) && $last3Chars[1] == ' ' && preg_match("/[a-z]/i", $last3Chars[2])) {
                    $address = mb_substr($address, 0, -3) . $last3Chars[0] . $last3Chars[2];
                }
            }
        }

        return $address;
    }

    /**
     * Check pids in lock file.
     *
     * Used to implement cronjob singleton feature.
     *
     * @param string $lockFilePath
     * @return false|string[]
     */
    public static function isProcessLocked(string $lockFilePath): array|bool
    {

        # If lock file exists, check if stale.  If exists and is not stale, return TRUE
        # Else, create lock file and return FALSE.

        if (file_exists($lockFilePath)) {
            # check if it's stale
            $lockingPID = trim(file_get_contents($lockFilePath));

            # Get all active PIDs.
            $pids = explode("\n", trim(`ps -e | awk '{print $1}'`));

            # If PID is still active, return true
            if (in_array($lockingPID, $pids)) return $pids;

            # Lock-file is stale, so kill it.  Then move on to re-creating it.
            // echo "Removing stale lock file.\n";
            unlink($lockFilePath);
        }

        file_put_contents($lockFilePath, getmypid() . "\n");
        return false;

    }

    /**
     * Check if the php execution environment is on CLI.
     *
     * @return bool
     */
    public static function isCLI(): bool
    {
        if (defined('STDIN')) {
            return true;
        }

        if (empty($_SERVER['REMOTE_ADDR']) and !isset($_SERVER['HTTP_USER_AGENT']) and count($_SERVER['argv']) > 0) {
            return true;
        }

        return false;
    }

    public static function isDev(): bool
    {
        return self::getContainer()->get('settings')['mode'] == 'development';
    }

    public static function compareIsEqualArray(array $array1, array $array2): bool
    {
        return (array_diff($array1, $array2) == [] && array_diff($array2, $array1) == []);
    }

    public static function getBoxNoTitle($boxNo)
    {
        return 100 + $boxNo;
    }

    /**
     * String from column index.
     *
     * @param int $columnIndex Column index (A = 1)
     *
     * @return string
     */
    public static function stringFromColumnIndex($columnIndex)
    {
        static $indexCache = [];
        static $lookupCache = ' ABCDEFGHIJKLMNOPQRSTUVWXYZ';

        if (!isset($indexCache[$columnIndex])) {
            $indexValue = $columnIndex;
            $base26 = '';
            do {
                $characterValue = ($indexValue % 26) ?: 26;
                $indexValue = ($indexValue - $characterValue) / 26;
                $base26 = $lookupCache[$characterValue] . $base26;
            } while ($indexValue > 0);
            $indexCache[$columnIndex] = $base26;
        }

        return $indexCache[$columnIndex];
    }

    public static function isZeroOrOne($value): bool
    {
        $value = ($value ?? '') . '';
        return $value === '0' || $value === '1';
    }

    public static function isZeroOrOneInParam($params, $key): bool
    {
        $value = $params[$key] ?? null;
        return self::isZeroOrOne($value);
    }

    /**
     * Check if html has any inline image.
     *
     * @param $html
     * @return bool
     */
    public static function hasInlineImage($html)
    {
        $PATTERN = '/\<img [^\>]*src=[\"\']?(data:(image\/[a-z]+);base64,([^\"\' \>]+))[\"\' \>]/';
        $matches = array();
        // preg_match_all($PATTERN, $html, $matches, PREG_SET_ORDER);
        preg_match($PATTERN, $html, $matches, PREG_OFFSET_CAPTURE);
        return !empty($matches);
    }

    /*public static function mb_replace($search, $replace, $subject, &$count=0) {
        if (!is_array($search) && is_array($replace)) {
            return false;
        }
        if (is_array($subject)) {
            // call mb_replace for each single string in $subject
            foreach ($subject as &$string) {
                $string = &mb_replace($search, $replace, $string, $c);
                $count += $c;
            }
        } elseif (is_array($search)) {
            if (!is_array($replace)) {
                foreach ($search as &$string) {
                    $subject = mb_replace($string, $replace, $subject, $c);
                    $count += $c;
                }
            } else {
                $n = max(count($search), count($replace));
                while ($n--) {
                    $subject = mb_replace(current($search), current($replace), $subject, $c);
                    $count += $c;
                    next($search);
                    next($replace);
                }
            }
        } else {
            $parts = mb_split(preg_quote($search), $subject);
            $count = count($parts)-1;
            $subject = implode($replace, $parts);
        }
        return $subject;
    }*/
}

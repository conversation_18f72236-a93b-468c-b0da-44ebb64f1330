<?php

namespace App\Models\Casts;

use App\Lib\Func;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;

class Csv implements CastsAttributes
{

    public function get($model, string $key, $value, array $attributes)
    {
        return Func::csvToArr($value);
    }

    public function set($model, string $key, $value, array $attributes)
    {
        if ($value) {
            if (is_array($value)) {
                return implode(',', array_values($value));
            } else
                return NULL;
        } else {
            return NULL;
        }
    }
}
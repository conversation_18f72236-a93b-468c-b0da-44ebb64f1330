<?php

declare(strict_types=1);

namespace App\Service\Sys\Product;

use App\Models\Sys\Product;
use Illuminate\Database\Eloquent\Model;

/**
 * @deprecated
 */
final class ProductService extends Base
{
    public function create(array $input): Product
    {
        return Product::create($input);
    }

    public function update($id, $input): Product
    {
        $row = Product::findOrFail($id);
        $row->update($input);
        return $row;
    }

    /**
     * @param int $id
     * @param array $params
     * @return \Illuminate\Database\Eloquent\Builder|Model|object|null|Product
     */
    public function getOne(int $id, array $params=[])
    {
        return $this->productRepository->getQueryBuilder()->where('id', $id)->first();
    }

    public function getProductsByPage(
        int $page,
        int $perPage,
        ?array $params
    ): array
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($perPage < 1) {
            $perPage = self::DEFAULT_PER_PAGE_PAGINATION;
        }

        return $this->productRepository->getProductsByPage(
            $page,
            $perPage,
            $params
        );
    }

    public function delete($ids): void
    {
        $arr = is_array($ids) ? $ids : explode(',', $ids);
        $this->productRepository->getQueryBuilder()->whereIn('id', $arr)->delete();
    }
}

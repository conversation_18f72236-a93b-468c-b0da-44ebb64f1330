<?php

declare(strict_types=1);

namespace App\Service\Sys\SysLog;

use App\Service\BaseService;
use App\Repository\Sys\SysLogRepository;
use Slim\Container;

abstract class Base extends BaseService
{
    private const REDIS_KEY = 'Sys:%s';
    protected SysLogRepository $sysLogRepository;

    public function __construct(Container $container)
    {
        $this->sysLogRepository = $container->get(SysLogRepository::class);
    }

    public function getSysLogRepository()
    {
        return $this->sysLogRepository;
    }
}


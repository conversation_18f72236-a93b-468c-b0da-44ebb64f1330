<?php

declare(strict_types=1);

namespace App\Controller\Supplier\SupplierCall;

use App\Lib\Func;
use Slim\Http\Request;
use Slim\Http\Response;

final class GetAll extends Base
{
    public function __invoke(Request $request, Response $response): Response
    {
        $page = $request->getQueryParam('page', null);
        $perPage = $request->getQueryParam('perPage', null);
        $params = $request->getParams();

        if (Func::keyExistsInWithParam($params, 'mergeEmail')) {
            $data = $this->supplierCallService->getSupplierCallsByPageMerged((int)$page, (int)$perPage, $params);
        } else if (Func::keyExistsInWithParam($params, 'mergeSupplierCustomer')) {
            // Supplier & customer Conversations
            $data = $this->supplierCallService->getSupplierAndCustomerCallsByPage((int)$page, (int)$perPage, $params);
        } else if (Func::keyExistsInWithParam($params, 'mergeOrgOffer')) {
            $data = $this->supplierCallService->getMergedSupplierOffersByPage((int)$page, (int)$perPage, $params);
        } else {
            $data = $this->supplierCallService->getSupplierCallsByPage((int)$page, (int)$perPage, $params);
        }

        return $this->jsonResponse($response, 'success', $data, 200);
    }
}

<?php

namespace App\Models;


/**
 * @property integer $user_id
 * @property string $username
 * @property string $email
 * @property string $name
 * @property string $initials
 * @property string $password
 * @property boolean $status
 * @property string $nonce
 * @property string $client_detail
 * @property string $settings
 * @property integer $role
 * @property string $created_on
 * @property string $updated_on
 * @property string $last_login_on
 *
 */
class User extends BaseModel
{
    const STATUS_ACTIVE = 1;
    const STATUS_INACTIVE = 0;

    const ROLE_USER_LIGHT = 8;
    const ROLE_USER_NORMAL = 4;
    const ROLE_USER_FULL = 2;
    const ROLE_ADMIN = 1;

    public $timestamps = [self::CREATED_AT, self::UPDATED_AT];

    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'user';

    protected $hidden = ['password', 'pivot'];

    protected $casts = ['settings' => 'json'];

    /**
     * The primary key for the model.
     * 
     * @var string
     */
    protected $primaryKey = 'user_id';

    /**
     * The "type" of the auto-incrementing ID.
     * 
     * @var string
     */
    protected $keyType = 'integer';

    /**
     * @var array
     */
    protected $fillable = ['username', 'email', 'name', 'initials', 'password', 'status', 'nonce', 'client_detail', 'role', 'created_on', 'updated_on', 'last_login_on', 'settings'];

}

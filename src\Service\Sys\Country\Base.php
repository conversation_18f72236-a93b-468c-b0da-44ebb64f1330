<?php

declare(strict_types=1);

namespace App\Service\Sys\Country;

use App\Service\BaseService;
use App\Repository\Sys\CountryRepository;
use Slim\Container;

abstract class Base extends BaseService
{
    private const REDIS_KEY = 'Sys:%s';
    protected CountryRepository $countryRepository;

    public function __construct(Container $container)
    {
        $this->countryRepository = $container->get(CountryRepository::class);
    }

    public function getCountryRepository()
    {
        return $this->countryRepository;
    }
}


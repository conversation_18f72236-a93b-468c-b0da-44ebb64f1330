<?php

declare(strict_types=1);

namespace App\Service\Sys\Dict;

use App\Lib\Func;
use App\Models\Sys\Dict;

final class DictService extends Base
{
    public function create(array $input): Dict
    {
        if (!($input['code'] ?? null)) {
            $input['code'] = $input['user']['user_id'] . '_' . Func::randomTs();
        }

        if (!($input['label'] ?? null)) {
            $input['label'] = $input['value'] ?? '';
        }

        return Dict::query()->create($input);
    }

    public function safeValidation($code, $input)
    {

    }

    public function update($code, $input): Dict
    {
        $row = Dict::findOrFail($code);
        $this->safeValidation($code, $input);
        $row->update($input);

        return $row;
    }

    public function getOne(int $id, array $params = []): Dict
    {
        return $this->dictRepository->getQueryBuilder()->where('id', $id)->first();
    }

    public function getDictsByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($perPage < 1) {
            $perPage = self::DEFAULT_PER_PAGE_PAGINATION;
        }

        return $this->dictRepository->getDictsByPage(
            $page,
            $perPage,
            $params
        );
    }

    public function delete($ids): void
    {
        $arr = is_array($ids) ? $ids : explode(',', $ids);
        $this->dictRepository->getQueryBuilder()->whereIn('id', $arr)->delete();
    }
}

<?php

declare(strict_types=1);

namespace App\Controller\Email\EmailTemplateCategory;

use Slim\Http\Request;
use Slim\Http\Response;

final class Create extends Base
{
    public function __invoke(Request $request, Response $response): Response
    {
        $input = (array)$request->getParsedBody();

        $this->validate($input);

        $row = $this->emailTemplateCategoryService->create($input);

        return $this->jsonResponse($response, 'success', $row, 201);
    }
}

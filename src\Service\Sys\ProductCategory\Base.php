<?php

declare(strict_types=1);

namespace App\Service\Sys\ProductCategory;

use App\Service\BaseService;
use App\Repository\Sys\ProductCategoryRepository;
use Slim\Container;

abstract class Base extends BaseService
{
    protected ProductCategoryRepository $productCategoryRepository;

    public function __construct(Container $container)
    {
        $this->productCategoryRepository = $container->get(ProductCategoryRepository::class);
    }

    public function getProductCategoryRepository()
    {
        return $this->productCategoryRepository;
    }
}


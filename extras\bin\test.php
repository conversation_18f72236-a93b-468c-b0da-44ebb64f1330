<?php


use App\Lib\FuncDate;

error_reporting(E_ALL);

require __DIR__ . '/../../src/App/App.php';

print_r(\App\Lib\FuncDate::getYmList('2026-06-30', 3));

$prevFromYm = '2022-07-01';
$prevToYm = '2023-07-30';
echo PHP_EOL;
print_r(FuncDate::getMonthsDiff($prevFromYm, $prevToYm));

echo PHP_EOL;
echo strtotime('2022-02' . '-01');
echo PHP_EOL;
print_r(date(DATE_FORMAT_YMD, strtotime('last day of 0 months', strtotime('2024-02' . '-01'))));
echo PHP_EOL;
print_r(min('2025-08', date('Y-m')));
echo PHP_EOL;
print_r(date('Y-m', strtotime('last day of -1 months')));

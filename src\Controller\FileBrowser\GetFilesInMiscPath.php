<?php

namespace App\Controller\FileBrowser;

use App\Controller\BaseController;
use App\Service\FileBrowser\MiscFileAssignService;
use Slim\Container;
use Slim\Http\Request;
use Slim\Http\Response;

class GetFilesInMiscPath extends BaseController
{
    protected MiscFileAssignService $miscFileAssignService;

    public function __construct(Container $container)
    {
        parent::__construct($container);

        $this->miscFileAssignService = $container->get(MiscFileAssignService::class);
    }

    public function __invoke(Request $request, Response $response, array $args): Response
    {
        $params = $request->getParams();

        $data = $this->miscFileAssignService->getAllFiles($params);

        return $this->jsonResponse($response, 'success', $data, 200);
    }
}
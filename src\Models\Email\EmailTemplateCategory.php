<?php

namespace App\Models\Email;

use App\Models\BaseModel;

/**
 * @property integer $id
 * @property string $cat1
 * @property integer $sort
 *
 * @property EmailTemplate[] $emailTemplates
 * @property integer $cat       // calculated value
 */
class EmailTemplateCategory extends BaseModel
{
    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'email_template_category';

    /**
     * @var array
     */
    protected $fillable = ['cat1', 'sort'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function emailTemplates()
    {
        return $this->hasMany('App\Models\Email\EmailTemplate', 'category_id');
    }
}

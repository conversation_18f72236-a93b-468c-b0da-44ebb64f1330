<?php

declare(strict_types=1);

namespace App\Handler;

use Psr\Http\Message\ResponseInterface as Response;
use Psr\Http\Message\ServerRequestInterface as Request;
use Psr\Container\ContainerInterface;

final class PHPRuntimeError
{
    protected ContainerInterface $container;

    public function __construct($container)
    {
        $this->container = $container;
    }

    public function __invoke(
        Request    $request,
        Response   $response,
        \Throwable $error
    ): Response
    {
        $isDetail = $_SERVER['DISPLAY_ERROR_DETAILS'] == '1';
        $statusCode = $error->getCode();
        $errorStr = $error->getMessage();


        if ($isDetail) $errorStr .= PHP_EOL . $error->getTraceAsString();
        $data = [
            'code' => $statusCode,
            'status' => 'error',
            'message' => $errorStr,
            'class' => $error->getMessage(),
            'trace' => $error->getTraceAsString(),
        ];
        $body = json_encode($data, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT);
        $response->getBody()->write((string)$body);


        $this->container->get('logger')->error('[Runtime] ' . $errorStr);
        // $this->container->get('logger')->error($error->getTraceAsString());
        $traces = $error->getTrace();
        if ($traces) {
            $errorDetails =  [] ;
            foreach ($traces as $ind => &$trace) {
                $errorDetails[] = sprintf("Func: %s, #%s in %s"
                    , $trace['function']
                    , $trace['line'] ?? ''
                    , $trace['file'] ?? ''
                );
                if ($ind > 3) {
                    break;
                }
            }

            $this->container->get('logger')->error(implode(PHP_EOL, $errorDetails));
        }

        $newResp = $response
            ->withStatus(500)
            ->withHeader('Access-Control-Allow-Headers', 'X-Requested-With, Content-Type, Accept, Origin, Authorization, AccessToken')
            ->withHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS');

        $newResp = setCorsOrigin($request, $newResp);
        return $newResp;
    }
}

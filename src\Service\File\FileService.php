<?php

declare(strict_types=1);

namespace App\Service\File;

use App\Lib\FileLib;
use App\Lib\Func;
use App\Models\File;
use Intervention\Image\ImageManager;
use Respect\Validation\Exceptions\FileException;

final class FileService extends Base
{
    public function getBase64EncodedImage(File $file, $type = 'image')
    {
        $orgPath = $file->org_path;
        if (!$orgPath) return null;
        if ($type === 'small_image') {
            $orgPath = str_replace(FileLib::ORIGINAL_PATH, FileLib::SMALL_PATH, $orgPath);
        } else if ($type === 'thumbnail') {
            $orgPath = str_replace(FileLib::ORIGINAL_PATH, FileLib::THUMB_PATH, $orgPath);
        }
        return $this->fileLib->getBase64EncodedImage($this->fileLib->getFullPath($orgPath));
    }

    /**
     * Return UNSAVED File object
     *
     * @param string $fileCategory
     * @param \Psr\Http\Message\UploadedFileInterface $file
     * @param string $fileName
     * @return File
     * @throws \League\Flysystem\FilesystemException
     */
    public static function uploadFile($fileCategory, \Psr\Http\Message\UploadedFileInterface $file, string $pathRel = ''): File
    {
        $pathAbs = Func::pathJoin(File::getBasePath($fileCategory), $pathRel);
        if (!file_exists($pathAbs)) {
            mkdir($pathAbs, 0755, true);
        }

        try {
            if ($file->getError() !== UPLOAD_ERR_OK) {
                throw new \App\Exception\File('Upload Error! Code: ' . $file->getError(), 500);
            }

            // Uploading file
            $fileType = $file->getClientMediaType();
            $fileRow = [
                'category' => $fileCategory,
                'type' => $fileType,
                'file_name' => Func::getSafeFilePath($file->getClientFilename(), '-', true),
                'clean_file_name' => $file->getClientFilename(),
                'size' => $file->getSize()
            ];
            $fileRow['org_path'] = $fileRow['path'] = Func::pathJoinUrl(Func::pathToUrl($pathRel), Func::getSafeFilePath($fileRow['file_name']));
            $file->moveTo($pathAbs . DS . $fileRow['file_name']);

            if (str_starts_with($fileType, 'image')) {
                if ($fileCategory == File::CAT_OFFER_BLOG_FILE) {
                    $orgFileFullPath = $pathAbs . DS . $fileRow['file_name'];
                    $im = new ImageManager(['driver' => 'gd',]);
                    $orgImage = $im->make($orgFileFullPath);
                    $orgImage->resize(400, 400, function ($constraint) {
                        $constraint->aspectRatio();
                        $constraint->upsize();
                    });
                    $orgImage->resizeCanvas(400, 400, 'center', false, '#ffffff');
                    $orgImage->save($orgFileFullPath);
                    clearstatcache();
                    $fileRow['size'] = @filesize($orgFileFullPath);
                } else if ($fileCategory == File::CAT_OFFER_NEWSLETTER_FILE) {
                    $orgFileFullPath = $pathAbs . DS . $fileRow['file_name'];
                    $im = new ImageManager(['driver' => 'gd',]);
                    $orgImage = $im->make($orgFileFullPath);
                    $orgImage->resize(80, 80, function ($constraint) {
                        $constraint->aspectRatio();
                        $constraint->upsize();
                    });
                    $orgImage->resizeCanvas(80, 80, 'center', false, '#ffffff');
                    $orgImage->save($orgFileFullPath);
                    clearstatcache();
                    $fileRow['size'] = @filesize($orgFileFullPath);
                }
            }

            // Save file data
            /** @var File $fileObj */
            $fileObj = File::create($fileRow);

            return $fileObj;
        } catch (\Exception $e) {
            throw new FileException($e->getMessage(), 500);
        }
    }

    public function removeFile(File $fileObj): bool
    {
        try {
            $path = $fileObj->abs_path;

            if (file_exists($path)) {
                unlink($path);
                $this->logger->debug("File deleted. Path: $path");
            } else {
                $this->logger->debug("File not exist. Path: $path");
            }

            $fileObj->delete();
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
            throw new \App\Exception\File($e->getMessage());
        }
        return true;
    }

    public function removeFileAll(File $fileObj): bool
    {
        try {
            if ($path = $fileObj->org_path) {
                if ($this->fs->fileExists($path)) {
                    $this->fs->delete($path);
                    $this->logger->debug("File deleted. Path: $path");
                } else {
                    $this->logger->debug("File not exist. Path: $path");
                }
            }

            // small image
            if ($path = $fileObj->path) {
                if ($this->fs->fileExists($path)) {
                    $this->fs->delete($path);
                    $this->logger->debug("File deleted. Path: $path");
                } else {
                    $this->logger->debug("File not exist. Path: $path");
                }
            }

            $fileObj->delete();
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
            throw new \App\Exception\File($e->getMessage());
        }
        return true;
    }
}

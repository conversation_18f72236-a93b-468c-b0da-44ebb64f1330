<?php

declare(strict_types=1);

namespace App\Repository\Customer;

use App\Lib\Func;
use App\Models\Customer\CustomerAddress;
use App\Repository\BaseRepositoryORM;
use Illuminate\Database\Eloquent\Builder;

final class CustomerAddressRepository extends BaseRepositoryORM
{
    private function getQueryCustomerAddressesByPage($params = []): Builder
    {
        $qb = $this->getQueryBuilder();

        if (!empty($params)) {
            $qb = $this->applyOrderBy($qb, $params);

            if (Func::keyExistsInWithParam($params, 'customer')) {
                $qb->with('customer');
            }

            if (Func::keyExistsInWithParam($params, 'country')) {
                $qb->with('country');
            }

            if (Func::keyExistsInWithParam($params, 'countryRegion')) {
                $qb->with('countryRegion');
            }
        }

        return $qb;
    }

    /**
     * Get lists by pagination.
     *
     * @param int $page
     * @param int $perPage
     * @param array|null $params
     * @return array
     */
    public function getCustomerAddressesByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        $query = $this->getQueryCustomerAddressesByPage($params);
        $total = $this->getCountByQuery($query);

        $result = $this->getResultsWithPagination(
            $query,
            $page,
            $perPage,
            $total
        );

        return $result;
    }

    public function getQueryBuilder(): Builder
    {
        return CustomerAddress::query();
    }
}
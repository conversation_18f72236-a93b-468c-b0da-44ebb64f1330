<?php

declare(strict_types=1);

namespace App\Repository\Customer;

use App\Lib\Func;
use App\Lib\FuncModel;
use App\Models\Customer\CustomerContact;
use App\Repository\BaseRepositoryORM;
use Illuminate\Database\Eloquent\Builder;

final class CustomerContactRepository extends BaseRepositoryORM
{
    public function getQueryCustomerContactsByPage($params = []): Builder
    {
        $qb = $this->getQueryBuilder();

        if (!empty($params)) {
            if ($keyWords = $params['keyWords'] ?? null) {
                $qb->where(function (Builder $builder) use (&$keyWords) {
                    $builder
                        ->where('firstname', 'like', FuncModel::likeValue($keyWords))
                        ->orWhere('lastname', 'like', FuncModel::likeValue($keyWords))
                        ->orWhere('email', 'like', FuncModel::likeValue($keyWords))
                        ->orWhereHas('customer', function (Builder $builder) use (&$keyWords) {
                            $builder
                                ->where('name', 'like', FuncModel::likeValue($keyWords))
                                ->orWhere('org_a', 'like', FuncModel::likeValue($keyWords));
                        });
                });
            }

            if (Func::keyExistsInWithParam($params, 'customer')) {
                $qb->with('customer', function ($builder) use (&$params) {
                    if (Func::keyExistsInWithParam($params, 'customer.contactsCnt')) {
                        $builder->withCount('contacts');
                    }
                });
            }

            $qb = $this->applyOrderBy($qb, $params);
        }

        return $qb;
    }

    /**
     * Get lists by pagination.
     *
     * @param int $page
     * @param int $perPage
     * @param array|null $params
     * @return array
     */
    public function getCustomerContactsByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        $query = $this->getQueryCustomerContactsByPage($params);
        $total = $this->getCountByQuery($query);

        $result = $this->getResultsWithPagination(
            $query,
            $page,
            $perPage,
            $total
        );

        return $result;
    }

    public function getQueryBuilder(): Builder
    {
        return CustomerContact::query();
    }
}
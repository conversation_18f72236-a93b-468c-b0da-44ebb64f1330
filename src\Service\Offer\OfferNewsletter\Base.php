<?php

declare(strict_types=1);

namespace App\Service\Offer\OfferNewsletter;

use App\Service\BaseService;
use App\Repository\Offer\OfferNewsletterRepository;
use Slim\Container;

abstract class Base extends BaseService
{
    private const REDIS_KEY = 'Offer:%s';
    protected OfferNewsletterRepository $offerNewsletterRepository;

    public function __construct(Container $container)
    {
        $this->offerNewsletterRepository = $container->get(OfferNewsletterRepository::class);
    }

    public function getOfferNewsletterRepository()
    {
        return $this->offerNewsletterRepository;
    }
}


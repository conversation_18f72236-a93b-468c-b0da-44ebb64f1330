<?php

namespace App\Controller\FileBrowser;

use App\Lib\FileBrowserLib;
use Slim\Http\Request;
use Slim\Http\Response;

class GetChildren extends Base
{
    public function __invoke(Request $request, Response $response, array $args): Response
    {
        $params = $request->getParams();
        $id = $params['id'] ?? '';

        $basePath = FileBrowserLib::id2path($id);
        if ($basePath == '!') $basePath = '';

        $data = $this->fileBrowserService->getChildren($basePath, $params);
        $folderChain = $this->fileBrowserService->getFolderChain($basePath);

        return $this->jsonResponse($response, 'success', ['folderChain' => $folderChain,  'data' =>$data], 200);
    }
}
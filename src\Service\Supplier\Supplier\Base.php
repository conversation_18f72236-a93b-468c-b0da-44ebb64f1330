<?php

declare(strict_types=1);

namespace App\Service\Supplier\Supplier;

use App\Repository\Supplier\SupplierRepository;
use App\Service\BaseService;
use Slim\Container;

abstract class Base extends BaseService
{
    protected SupplierRepository $supplierRepository;

    public function __construct(Container $container)
    {
        $this->supplierRepository = $container->get(SupplierRepository::class);
    }

    public function getSupplierRepository()
    {
        return $this->supplierRepository;
    }
}


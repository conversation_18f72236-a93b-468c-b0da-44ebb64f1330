<?php

namespace App\Models;

/**
 * @property integer $id
 * @property integer $sync_type
 * @property string $category
 * @property string $name
 * @property string $note
 * @property string $status
 * @property string $req_method
 * @property string $detail
 * @property integer $batch_code
 * @property integer $action_type
 * @property integer $created_by
 * @property string $created_on
 * @property string $updated_on
 *
 * @property User $user
 */
class SyncLog extends BaseModel
{
    const SYNC_TYPE_DOWN = 0;
    const SYNC_TYPE_UP = 1;
    const SYNC_TYPE_SYS_OWN = 2;

    const CATEGORY_DEFAULT = 'default';

    const NAME_DS_SUPPLIER_FULL = 'Full Supplier Sync';
    const NAME_DS_SUPPLIER_LATEST = 'Latest Supplier Sync';

    const NAME_DB_BACKUP = 'DB Backup';

    // Whc Media Service down syncing
    // -----------------------------------------------------------------------------------------------------------------
    const NAME_WHC_EMAIL_TRACKING_LOG_SYNC = 'WHC Email Tracking Log Sync';
    // -----------------------------------------------------------------------------------------------------------------

    const ACTION_TYPE_SYSTEM_UI = 0;
    const ACTION_TYPE_CRON = 1;

    const STATUS_STARTED = 'started';
    const STATUS_PROCESSING = 'processing';
    const STATUS_SUCCESS = 'success';
    const STATUS_ERROR = 'error';

    public $timestamps = true;

    protected $casts = ['detail' => 'array'];

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'sync_log';

    /**
     * The "type" of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'integer';

    /**
     * @var array
     */
    protected $fillable = ['sync_type', 'category', 'name', 'note', 'status', 'req_method', 'detail', 'batch_code', 'action_type', 'created_by', 'created_on', 'updated_on'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by', 'user_id');
    }
}

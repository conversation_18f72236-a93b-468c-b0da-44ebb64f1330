import { useState } from 'react';

export default () => {
    const [formChanges, setFormChanges] = useState<Record<string, any>>({});
    const [formChangesInEditing, setFormChangesInEditing] = useState<Record<string, any>>({});

    console.log('formChanges', formChanges);
    console.log('formChangesInEditing', formChangesInEditing);
    return { formChanges, setFormChanges, formChangesInEditing, setFormChangesInEditing };
};
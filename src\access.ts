import { UserRole } from "./constants";
import { sn } from "./util";

export const publicRoutes: string[] = ['/user/login'];

export const editorRoutes: string[] = [...publicRoutes, '/item/ean-detail'];
export const warehouseRoutes: string[] = [
  ...publicRoutes,
  '/orders/order-detail',
  '/orders/order-detail-packing',
  '/orders/shipping-report',
  '/orders/picklist-summary',
  '/ibo/ibo-register',
  '/ibo/ibo-register-mobile',
  '/ibo/pre-ibo-register',
];
export const stocklotUserRoutes: string[] = [...publicRoutes, '/item/stocklot-products'];

/**
 * access control.
 */
export default function access(initialState: { currentUser?: API.CurrentUser } | undefined) {
  const { currentUser } = initialState ?? {};
  const roleId = currentUser ? currentUser?.role : null;
  return {
    canAdmin: currentUser && roleId === UserRole.ADMIN,
    canUserFull: currentUser && (roleId === UserRole.USER_FULL || roleId === UserRole.ADMIN),
    user: currentUser && (roleId === UserRole.ADMIN || sn(roleId) > 0),
    roleRouteFilter: (route: any) => {
      if (!currentUser?.user_id) return false;
      let hasPermission = !!currentUser?.user_id;
      // console.log('[role check]', route.path, 'Role/Route name', roleId, route.name);

      /* if (roleId == UserRole.EDITOR) {
        hasPermission = editorRoutes.includes(route.path);
      } else if (roleId == UserRole.WAREHOUSE) {
        hasPermission = warehouseRoutes.includes(route.path);
      } else if (roleId == UserRole.STOCK_LOT) {
        hasPermission = stocklotUserRoutes.includes(route.path);
      } */

      return hasPermission;
    },
    userSettingsRouteFilter: (route: any) => {
      if (!currentUser?.user_id) return false;
      let hasPermission = false;

      if (roleId === UserRole.ADMIN || roleId === UserRole.USER_FULL) return true;

      /* if (route.path == '/suppliers/statistics') {
        hasPermission = sn(currentUser?.settings?.org_stats_item) > 50
      } */

      return hasPermission;
    },
  };
}

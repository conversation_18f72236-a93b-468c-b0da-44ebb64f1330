<?php

declare(strict_types=1);

namespace App\Controller\User;

use App\Exception\User;
use App\Lib\Func;
use Slim\Http\Request;
use Slim\Http\Response;

final class UpdatePassword extends Base
{
    public function __invoke(Request $request, Response $response, array $args): Response
    {
        $user_id = $args['id'] ?? null;
        if (!$user_id) {
            throw new User('User not found', 404);
        }

        $user_id = intval($user_id);
        $sesUserId = intval(Func::getSesUserId());
        if (!$user_id || $sesUserId !== $user_id) {
            User::raiseInvalidRequest('Invalid request. User does not match.');
        }

        $input = (array)$request->getParams();
        if (!($currentPassword = $input['currentPassword'] ?? null)) {
            User::raiseInvalidRequest('Current password is required.');
        }
        if (!($password = $input['password'] ?? null)) {
            User::raiseInvalidRequest('New password is required.');
        }

        // Check if username or email exists?
        $repo = $this->getCreateUserService()->getUserRepository();
        $builder = $repo->getQueryBuilder();
        $builder->where('user_id', '=', $user_id);
        /** @var \App\Models\User $user */
        $user = $builder->firstOrFail();

        if (!Func::verifyPassword($currentPassword, $user->password)) {
            User::raiseInvalidRequest('Current password does not match.');
        }

        $newData['password'] = Func::passwordHash($password);

        $user = $this->getUpdateUserService()->update($newData, $user_id);

        return $this->jsonResponse($response, 'success', $user, 200);
    }
}

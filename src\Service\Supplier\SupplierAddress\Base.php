<?php

declare(strict_types=1);

namespace App\Service\Supplier\SupplierAddress;

use App\Repository\Supplier\SupplierAddressRepository;
use App\Service\BaseService;
use Slim\Container;

abstract class Base extends BaseService
{
    protected SupplierAddressRepository $supplierAddressRepository;

    public function __construct(Container $container)
    {
        $this->supplierAddressRepository = $container->get(SupplierAddressRepository::class);
    }

    public function getSupplierAddressRepository()
    {
        return $this->supplierAddressRepository;
    }
}


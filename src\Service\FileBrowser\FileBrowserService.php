<?php

namespace App\Service\FileBrowser;

use App\Exception\BaseException;
use App\Lib\FileBrowserLib;
use App\Lib\Func;
use App\Service\BaseService;
use Slim\Container;

class FileBrowserService extends BaseService
{
    protected Container $container;

    protected ?string $rootPath = null;

    public function __construct(Container $container)
    {
        $this->container = $container;
    }

    public function setRootPath($path)
    {
        $this->rootPath = $path;
    }

    public function getRootPath()
    {
        return $this->rootPath;
    }

    public function getChildren($relBasePath, $options = [])
    {
        $basePath = Func::pathJoin($this->rootPath, $relBasePath);

        if (!file_exists($basePath)) {
            BaseException::raiseInvalidRequest('Base path ' . $basePath . ' not found!');
        }

        $list = array_diff(scandir($basePath), array('..', '.'));
        $ret = [];
        if ($list) {
            foreach ($list as $fileName) {
                $ret[] = $this->getFileDetail($relBasePath, $fileName);
            }
        }
        return $ret;
    }

    public function getFileDetail($parentPath, $fileName)
    {
        $parentId = FileBrowserLib::encode($parentPath);

        $absPath = $fileName ? Func::pathJoin($this->rootPath, $parentPath, $fileName) : Func::pathJoin($this->rootPath, $parentPath);
        $stat = stat($absPath);
        $isDir = is_dir($absPath);
        $fileArr = [
            'parentId' => $parentId,
            'id' => FileBrowserLib::encode(Func::pathJoin($parentPath, $fileName ? $fileName : '!')),
            'name' => $fileName ?? 'Root',
            'createdTime' => date(DATE_RFC3339_EXTENDED, $stat['ctime']),
            'modDate' => date( DATE_RFC3339_EXTENDED, $stat['mtime']),
            'size' => $stat['size'],
            'type' => $isDir ? 'dir' : 'file',
            'isDir' => $isDir,
            /*'capabilities' => [
            ],*/
        ];
        
        return $fileArr;
    }

    public function getFolderChain($relBasePath, $includeRoot=true)
    {
        $chains = [
            $this->getFileDetail('', null)
        ];
        if ($relBasePath) {
            $arr = explode(DS, $relBasePath);
            $p = '';
            foreach ($arr as $x) {
                $chains[] = $this->getFileDetail($p, $x);
                $p = Func::pathJoin($p, $x);
            }
        }
        return $chains;
    }
}
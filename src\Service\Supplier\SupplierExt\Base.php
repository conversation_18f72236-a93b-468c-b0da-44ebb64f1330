<?php

declare(strict_types=1);

namespace App\Service\Supplier\SupplierExt;

use App\Service\BaseService;
use App\Repository\Supplier\SupplierExtRepository;
use Slim\Container;

abstract class Base extends BaseService
{
    protected SupplierExtRepository $supplierExtRepository;

    public function __construct(Container $container)
    {
        $this->supplierExtRepository = $container->get(SupplierExtRepository::class);
    }

    public function getSupplierExtRepository()
    {
        return $this->supplierExtRepository;
    }
}


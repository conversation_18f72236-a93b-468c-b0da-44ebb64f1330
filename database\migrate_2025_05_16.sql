INSERT INTO `sys_dict` (`code`, `type`, `label`, `value`)
VALUES ('WHC_GROUPS', 'sys config', 'WHC Group Options', 'A,B,C,D,M,MT');

ALTER TABLE `supplier_contact`
    ADD COLUMN `is_asp`     BOOL DEFAULT 0 NULL AFTER `note`,
    ADD COLUMN `is_asp_reg` BOOL DEFAULT 0 NULL AFTER `is_asp`,
    ADD COLUMN `is_blocked` BOOL DEFAULT 0 NULL AFTER `is_asp_reg`;


CREATE TABLE `sys_product_category`
(
    `id`        INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'PK',
    `name`      VARCHAR(255) NOT NULL COMMENT 'Name of category',
    `is_active` BOOLEAN DEFAULT 1 COMMENT 'Active / Inactive',
    PRIMARY KEY (`id`),
    INDEX `IDX_sys_product_category_name` (`name`)
);


delete
from supplier_meta
where `type` in ('sales_place', 'sales_area', 'supplier_lang');



CREATE TABLE `sys_trademark`
(
    `id`        INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'PK',
    `name`      VARCHAR(255) NOT NULL COMMENT 'Name of trademark',
    `is_active` BOOLEAN DEFAULT 1 COMMENT 'Active / Inactive',
    PRIMARY KEY (`id`),
    INDEX `IDX_sys_trademark_name` (`name`)
);

CREATE TABLE `sys_product`
(
    `id`        INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'PK',
    `name`      VARCHAR(255) NOT NULL COMMENT 'Name of product',
    `is_active` BOOLEAN DEFAULT 1 COMMENT 'Active / Inactive',
    PRIMARY KEY (`id`),
    INDEX `IDX_sys_product_name` (`name`)
);


CREATE TABLE `supplier_call_trademark`
(
    `supplier_call_id` INT UNSIGNED NOT NULL,
    `trademark_id`     INT UNSIGNED NOT NULL,
    PRIMARY KEY (`supplier_call_id`, `trademark_id`),
    CONSTRAINT `FK_supplier_call_trademark_call_id` FOREIGN KEY (`supplier_call_id`) REFERENCES `supplier_call` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
    CONSTRAINT `FK_supplier_call_trademark_trademark_id` FOREIGN KEY (`trademark_id`) REFERENCES `sys_trademark` (`id`) ON UPDATE CASCADE ON DELETE CASCADE
);


CREATE TABLE `supplier_call_product`
(
    `supplier_call_id` INT UNSIGNED NOT NULL,
    `product_id`       INT UNSIGNED NOT NULL,
    PRIMARY KEY (`supplier_call_id`, `product_id`),
    CONSTRAINT `FK_supplier_call_product_call_id` FOREIGN KEY (`supplier_call_id`) REFERENCES `supplier_call` (`id`) ON UPDATE CASCADE ON DELETE CASCADE,
    CONSTRAINT `FK_supplier_call_product_product_id` FOREIGN KEY (`product_id`) REFERENCES `sys_product` (`id`) ON UPDATE CASCADE ON DELETE CASCADE
);

ALTER TABLE `supplier_call`
    ADD COLUMN `comment` TEXT NULL COMMENT 'Call comment for Offer' AFTER `note`;





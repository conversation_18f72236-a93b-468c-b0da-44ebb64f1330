<?php

declare(strict_types=1);

namespace App\Repository\Supplier;

use App\Lib\Func;
use App\Models\Supplier\SupplierAddress;
use App\Repository\BaseRepositoryORM;
use Illuminate\Database\Eloquent\Builder;

final class SupplierAddressRepository extends BaseRepositoryORM
{
    private function getQuerySupplierAddressesByPage($params = []): Builder
    {
        $qb = $this->getQueryBuilder();

        if (!empty($params)) {
            $qb = $this->applyOrderBy($qb, $params);

            if (Func::keyExistsInWithParam($params, 'supplier')) {
                $qb->with('supplier');
            }

            if (Func::keyExistsInWithParam($params, 'country')) {
                $qb->with('country');
            }

            if (Func::keyExistsInWithParam($params, 'countryRegion')) {
                $qb->with('countryRegion');
            }
        }

        return $qb;
    }

    /**
     * Get lists by pagination.
     *
     * @param int $page
     * @param int $perPage
     * @param array|null $params
     * @return array
     */
    public function getSupplierAddressesByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        $query = $this->getQuerySupplierAddressesByPage($params);
        $total = $this->getCountByQuery($query);

        $result = $this->getResultsWithPagination(
            $query,
            $page,
            $perPage,
            $total
        );

        return $result;
    }

    public function getQueryBuilder(): Builder
    {
        return SupplierAddress::query();
    }
}
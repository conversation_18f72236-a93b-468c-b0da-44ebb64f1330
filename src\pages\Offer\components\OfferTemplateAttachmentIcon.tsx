import { FileOutlined, PaperClipOutlined } from "@ant-design/icons";
import { Avatar, Modal, Space, Image, Typography, Badge, Button } from "antd";
import { useTheme } from "antd-style";
import { RcFile } from "antd/es/upload";
import { UploadFile } from "antd/lib";
import { useState } from "react";

type OfferTemplateAttachmentIconProps = {
  attachments?: (API.File & UploadFile)[];
};

const OfferTemplateAttachmentIcon: React.FC<OfferTemplateAttachmentIconProps> = ({ attachments, ...rest }) => {
  const theme = useTheme();
  const [openAttachmentsModal, setOpenAttachmentsModal] = useState<boolean>(false);

  console.log(attachments);

  return attachments?.length ? (
    <>
      <Badge count={attachments?.length} overflowCount={10}>
        <Button
          title={`${attachments.length} email template attachments`}
          type="primary"
          variant="outlined"
          color="green"
          onClick={() => setOpenAttachmentsModal(true)}
          icon={<PaperClipOutlined style={{ color: theme.green8 }} />}
        />
        {/* <Avatar
          size="default"
          shape="square"
          style={{ backgroundColor: theme.green3 }}
          icon={
            <PaperClipOutlined
              className="c-grey cursor-pointer"
              title={`${attachments.length} email template attachments`}
              onClick={() => setOpenAttachmentsModal(true)}
              style={{ color: theme.green8 }}
            />
          }
        /> */}
      </Badge>
      <Modal
        open={openAttachmentsModal}
        onCancel={() => setOpenAttachmentsModal(false)}
        width={900}
        title={`${attachments.length} email template attachments`}
        footer={false}
      >
        <Space size={24} wrap={true}>
          {attachments.map((a) => {
            const fileUrl = a.id ? `${a.url}` : a.thumbUrl;
            return (
              <Space key={a.uid} direction="vertical">
                {a.type?.includes("image") ? (
                  <Avatar shape="square" size={256} src={<Image src={fileUrl} />} />
                ) : (
                  <FileOutlined style={{ fontSize: 256, color: "grey" }} />
                )}
                <Typography.Link href={fileUrl} ellipsis target="_blank" style={{ textAlign: "center", maxWidth: 256 }}>
                  {a.clean_file_name ?? a.name}
                </Typography.Link>
              </Space>
            );
          })}
        </Space>
      </Modal>
    </>
  ) : null;
};

export default OfferTemplateAttachmentIcon;

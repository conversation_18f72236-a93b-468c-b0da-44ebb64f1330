<?php

declare(strict_types=1);

namespace App\Controller\User;

use Slim\Http\Request;
use Slim\Http\Response;

final class Login extends Base
{
    public function __invoke(Request $request, Response $response): Response
    {
        $input = (array) $request->getParsedBody();
        $jwt = $this->getLoginUserService()->login($input);
        $message = [
            'Authorization' => $jwt,
            'type' => $input['type'] ?? 'account'
        ];

        return $this->jsonResponse($response, 'success', $message, 200);
    }
}

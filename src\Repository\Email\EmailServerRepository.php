<?php

declare(strict_types=1);

namespace App\Repository\Email;

use App\Models\Email\EmailServer;
use App\Repository\BaseRepositoryORM;
use Illuminate\Database\Eloquent\Builder;

final class EmailServerRepository extends BaseRepositoryORM
{
    private function getQueryEmailServersByPage($params = []): Builder
    {
        $qb = $this->getQueryBuilder();

        if (!empty($params)) {
            if ($keyword = $params['keyword'] ?? '') {
                $qb->where('domain', 'LIKE', "%$keyword%");
            }

            $qb = $this->applyOrderBy($qb, $params);
        }

        return $qb;
    }

    /**
     * Get lists by pagination.
     *
     * @param int $page
     * @param int $perPage
     * @param array|null $params
     * @return array
     */
    public function getEmailServersByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        $query = $this->getQueryEmailServersByPage($params);
        $total = $this->getCountByQuery($query);

        $result = $this->getResultsWithPagination(
            $query,
            $page,
            $perPage,
            $total
        );

        return $result;
    }

    public function getQueryBuilder(): Builder
    {
        return EmailServer::query();
    }
}
import type { Dispatch, SetStateAction } from "react";
import React, { useEffect, useRef } from "react";
import { Col, message } from "antd";
import type { ProFormInstance } from "@ant-design/pro-form";
import { ProFormText, ModalForm } from "@ant-design/pro-form";
import { updateCustomer } from "@/services/app/Customer/customer";
import Util from "@/util";
import { ProFormDigit, ProFormSelect } from "@ant-design/pro-components";
import { countryOptions } from "@/services/app/countries";

type FormValueType = Partial<API.Customer>;

const handleUpdate = async (fields: FormValueType) => {
  const hide = message.loading("Updating...", 0);

  try {
    await updateCustomer(fields.id, fields);
    hide();
    message.success("Update is successful");
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type UpdateFormProps = {
  initialValues?: Partial<API.Customer>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.Customer) => void;
};

const UpdateForm: React.FC<UpdateFormProps> = (props) => {
  const { initialValues, modalVisible, handleModalVisible, onSubmit } = props;

  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (modalVisible && formRef.current) {
      formRef.current.resetFields();
      const newValues = {
        ...(initialValues || {}),
      };

      formRef.current.setFieldsValue(newValues);
    }
  }, [modalVisible, initialValues]);

  return (
    <ModalForm
      title={"Update Customer"}
      width="1200px"
      open={modalVisible}
      onOpenChange={handleModalVisible}
      layout="horizontal"
      grid
      labelCol={{ flex: "100px" }}
      wrapperCol={{ flex: "auto" }}
      initialValues={initialValues || {}}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleUpdate({ ...value, id: initialValues?.id });

        if (success) {
          handleModalVisible(false);
          if (onSubmit) onSubmit(value);
        }
      }}
    >
      <Col span={19}>
        <ProFormText name="name" label="Company" required rules={[{ message: "Company is required!", required: true }]} />
      </Col>
      <Col span={5}>
        <ProFormText name="org_a" label="Org A" width="xs" disabled placeholder="Org A" />
      </Col>
      <Col span={14}>
        <ProFormText name={["address", "street1"]} label="Street 1" />
        <ProFormText name={["address", "street2"]} label="Street 2" />
        <ProFormText name={["address", "postcode"]} label="Postcode" width="sm" />
        <ProFormText
          name={["address", "city"]}
          label="City"
          width="md"
          required
          rules={[
            {
              required: true,
              message: "City is required",
            },
          ]}
        />

        <ProFormText name={["address", "region"]} label="State" width="md" />
        <ProFormSelect
          name={["address", "country_id"]}
          label="Country"
          options={countryOptions}
          showSearch
          allowClear
          width="md"
          required
          rules={[
            {
              required: true,
              message: "Country is required",
            },
          ]}
        />
      </Col>
      <Col span={10}>
        <ProFormText name={["address", "telephone"]} label="Telephone" />
        <ProFormText name={["address", "fax"]} label="Fax" />
      </Col>
    </ModalForm>
  );
};

export default UpdateForm;

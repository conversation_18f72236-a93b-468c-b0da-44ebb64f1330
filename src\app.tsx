import { AvatarDropdown, Footer } from "@/components";
import { currentUser as queryCurrentUser } from "@/services/app/api";
import type { Settings as LayoutSettings } from "@ant-design/pro-components";
import { SettingDrawer } from "@ant-design/pro-components";
import type { RequestConfig, RunTimeLayoutConfig } from "@umijs/max";
import { history } from "@umijs/max";
import React from "react";
import defaultSettings from "../config/defaultSettings";
import { errorConfig } from "./requestErrorConfig";
import SAvatar from "./components/SAvatar";
import dayjs from "dayjs";
import { Button, Result } from "antd";

// ================ Important ================/
// dayjs.extend(utc);
// dayjs.extend(timezone);
// dayjs.tz.setDefault('Europe/Berlin');

const isDev = process.env.NODE_ENV === "development";
const loginPath = "/user/login";

localStorage.setItem("umi_locale", "en-US"); //your locale

/**
 * @see  https://umijs.org/zh-CN/plugins/plugin-initial-state
 * */
export async function getInitialState(): Promise<{
  settings?: Partial<LayoutSettings>;
  currentUser?: API.CurrentUser;
  loading?: boolean;
  fetchUserInfo?: () => Promise<API.CurrentUser | undefined>;
}> {
  const fetchUserInfo = async () => {
    try {
      const msg = await queryCurrentUser({
        skipErrorHandler: true,
      });
      return msg;
    } catch (error) {
      history.push(`${loginPath}?redirect=${window.location.pathname || ""}`);
    }
    return undefined;
  };
  // If it is not a login page, execute
  const { location } = history;
  const currentUser = await fetchUserInfo();

  if (location.pathname.includes(loginPath) && currentUser) {
    const urlParams = new URL(window.location.href).searchParams;
    const redirect = urlParams.get("redirect");
    history.push(redirect || "/");
  }

  return {
    fetchUserInfo,
    currentUser,
    settings: defaultSettings as Partial<LayoutSettings>,
  };
}

// APIs supported by ProLayout https://procomponents.ant.design/components/layout
export const layout: RunTimeLayoutConfig = ({ initialState, setInitialState }) => {
  return {
    /* actionsRender: () => [
      <Question key="doc" />, <SelectLang key="SelectLang" />
    ], */
    // actionsRender: () => [<div key="1">TEST</div>],
    avatarProps: {
      src: initialState?.currentUser?.avatar,
      title: (
        <>
          <SAvatar
            size="small"
            // src={initialState?.currentUser?.avatar}
            text={initialState?.currentUser?.initials ?? ""}
            alt="avatar"
          />
          <span className={`anticon`} style={{ marginLeft: 4 }}>
            {initialState?.currentUser?.name}
          </span>
        </>
      ),
      render: (_, avatarChildren) => {
        return <AvatarDropdown menu={!!initialState?.currentUser}>{avatarChildren}</AvatarDropdown>;
      },
    },
    footerRender: () => <Footer />,
    onPageChange: () => {
      const { location } = history;
      // If not logged in, redirect to login
      if (!initialState?.currentUser && location.pathname !== loginPath) {
        history.push(loginPath);
      }

      if (initialState?.currentUser && location.pathname === loginPath) {
        const urlParams = new URL(window.location.href).searchParams;
        const redirect = urlParams.get("redirect");
        history.push(redirect || "/");
      }
    },
    menuHeaderRender: undefined,
    // Custom 403 page
    unAccessible: (
      <Result
        status="403"
        title="403"
        subTitle={"Sorry, you don't have permission to access this page"}
        extra={
          <Button type="primary" onClick={() => history.push("/")}>
            Back Home
          </Button>
        }
      />
    ),

    breadcrumbRender: () => undefined,

    // Add a loading state
    childrenRender: (children) => {
      // if (initialState?.loading) return <PageLoading />;
      return (
        <>
          {children}
          {isDev && (
            <SettingDrawer
              disableUrlParams
              enableDarkTheme
              settings={initialState?.settings}
              onSettingChange={(settings) => {
                setInitialState((preInitialState) => ({
                  ...preInitialState,
                  settings,
                }));
              }}
            />
          )}
        </>
      );
    },
    ...initialState?.settings,
    logo: PUBLIC_PATH + "logo.png",
  };
};

/**
 * @name request configuration, you can configure error handling
 * It provides a unified network request and error handling solution based on useRequest of axios and ahooks.
 * @doc https://umijs.org/docs/max/request#Configuration
 */
export const request: RequestConfig = {
  baseURL: API_URL,
  ...errorConfig,
};

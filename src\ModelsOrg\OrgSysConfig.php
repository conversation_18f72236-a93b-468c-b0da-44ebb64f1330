<?php

namespace App\ModelsOrg;

/**
 * @property string $id
 * @property string $code
 * @property string $name
 * @property string $value
 * @property string $type
 * @property string $type_sub
 * @property integer $order
 * @property string $parent_id
 * @property boolean $sc_customer_order
 * @property string $sc_default_position
 * @property boolean $sc_contact
 * @property boolean $sc_customer_order_required
 * @property string $option_values
 * @property string $option_texts
 * @property string $display_type
 * @property string $value_type
 * @property boolean $link_to_supplier
 * @property boolean $outside_eu
 * @property boolean $ui_border
 * @property string $off_status_type
 */
class OrgSysConfig extends BaseModel
{
    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'sys_config';

    /**
     * The "type" of the auto-incrementing ID.
     * 
     * @var string
     */
    protected $keyType = 'string';

    /**
     * Indicates if the IDs are auto-incrementing.
     * 
     * @var bool
     */
    public $incrementing = false;

    /**
     * @var array
     */
    protected $fillable = ['code', 'name', 'value', 'type', 'type_sub', 'order', 'parent_id', 'sc_customer_order', 'sc_default_position', 'sc_contact', 'sc_customer_order_required', 'option_values', 'option_texts', 'display_type', 'value_type', 'link_to_supplier', 'outside_eu', 'ui_border', 'off_status_type'];


}

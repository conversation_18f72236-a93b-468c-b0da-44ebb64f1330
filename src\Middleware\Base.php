<?php

declare(strict_types=1);

namespace App\Middleware;

use App\Exception\Auth;
use App\Exception\User;
use Firebase\JWT\JWT;
use Psr\Container\ContainerInterface;
use Slim\Container;
use Slim\Http\Request;

abstract class Base
{
    protected ContainerInterface $container;

    public function __construct(ContainerInterface $container)
    {
        $this->container = $container;
    }

    protected function getValidToken(Request $request): string
    {
        // 1. JWT checking
        $jwtHeader = $request->getHeaderLine('Authorization');
        if (!$jwtHeader) {
            \App\Exception\Base::raiseUnauthorized('You need to login!');
        }
        $jwt = explode('Bearer ', $jwtHeader);
        if (!isset($jwt[1])) {
            \App\Exception\Base::raiseUnauthorized('Authentication token invalid. Please login.');
        }
        return $jwt[1];
    }
    protected function getDataByToken(string $token): object
    {
        try {
            return JWT::decode($token, $_SERVER['SECRET_KEY'], ['HS256', 'HS512']);
        } catch (\UnexpectedValueException $exception) {
            \App\Exception\Base::raiseUnauthorized();
        }
    }
    protected function checkToken(string $token): object
    {
        try {
            return JWT::decode($token, $_SERVER['SECRET_KEY'], ['HS256', 'HS512']);
        } catch (\UnexpectedValueException $exception) {
            \App\Exception\Base::raiseUnauthorized();
        }
    }

    protected function getApiKey(Request $request): string
    {
        return $request->getHeaderLine('X-API-Key');
    }

    protected function getParamsWithApiKey(Request &$request): array
    {
        // API Key checking.
        $parsedParams = (array)$request->getParams();
        $jwtHeader = $request->getHeaderLine('X-API-Key');
        if ($jwtHeader) {
            $parsedParams['api_key'] = $jwtHeader;
        }
        return $parsedParams;
    }

    /**
     * Check user object by user data and return user data as an array.
     * throw 403 error
     * @param object $user
     * @return array
     * @throws Auth
     */
    protected function validateUserObject(object $user): array
    {
        /** @var \App\Service\User\Find $findUserService */
        $findUserService = $this->container->get('find_user_service');

        try {
            $dbUser = $findUserService->getOne((int)$user->user_id);
            if ($dbUser['status']) {
                return $dbUser;
                // to do Now disable nonce checking
                /*if ($user->nonce == $dbUser['nonce']) {
                    $this->container->offsetSet('currentUser', $dbUser);
                    return $dbUser;
                }
                else \App\Exception\Base::raiseUnauthorized();*/
            }
        } catch (User $exception) {
            \App\Exception\Base::raiseUnauthorized('You are not authorized. User not found.');
        }
        \App\Exception\Base::raiseUnauthorized('You are not authorized. User disabled.');
    }
}

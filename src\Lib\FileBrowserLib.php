<?php

namespace App\Lib;

class FileBrowserLib
{
    const ORDER_BY_NAME = 'name';
    const ORDER_BY_MODIFIED_TIME = 'modifiedTime';
    const ORDER_DIRECTION_ASC = 'ASC';
    const ORDER_DIRECTION_DESC = 'DESC';

    const DEFAULT_ORDER_BY = FileBrowserLib::ORDER_BY_NAME;
    const DEFAULT_ORDER_DIRECTION = FileBrowserLib::ORDER_DIRECTION_ASC;

    public static function id2path($id) {
        return self::decode($id);
    }

    public static function encode($path) {
        return urlencode(base64_encode($path));
    }

    public static function decode($id) {
        return base64_decode(urldecode($id));
    }

}
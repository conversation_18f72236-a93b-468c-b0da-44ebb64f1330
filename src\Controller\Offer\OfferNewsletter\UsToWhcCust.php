<?php

declare(strict_types=1);

namespace App\Controller\Offer\OfferNewsletter;

use App\Exception\BaseException;
use Slim\Http\Request;
use Slim\Http\Response;

final class UsToWhcCust extends Base
{
    public function __invoke(Request $request, Response $response): Response
    {
        $input = (array)$request->getParsedBody();

        $offer_no = $input['offer_no'] ?? null;
        if (!$offer_no) {
            BaseException::raiseInvalidRequest('Offer No is required!');
        }

        $result = $this->offerNewsletterService->usToWhcCust($offer_no);

        return $this->jsonResponse($response, 'success', $result, 201);
    }
}

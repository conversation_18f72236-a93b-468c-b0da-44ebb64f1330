<?php

declare(strict_types=1);

namespace App\Service\Email\EmailTemplateCategory;

use App\Service\BaseService;
use App\Repository\Email\EmailTemplateCategoryRepository;
use Slim\Container;

abstract class Base extends BaseService
{
    private const REDIS_KEY = 'Email:%s';
    protected EmailTemplateCategoryRepository $emailTemplateCategoryRepository;

    public function __construct(Container $container)
    {
        $this->emailTemplateCategoryRepository = $container->get(EmailTemplateCategoryRepository::class);
    }

    public function getEmailTemplateCategoryRepository()
    {
        return $this->emailTemplateCategoryRepository;
    }
}


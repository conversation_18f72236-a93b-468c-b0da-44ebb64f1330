<?php

declare(strict_types=1);

namespace App\Middleware;

use App\Exception\User;
use App\Repository\UserRepository;
use Psr\Http\Message\ResponseInterface;
use Slim\Http\Request;
use Slim\Http\Response;
use Slim\Route;

final class AdminAuth extends Base
{
    /**
     * @throws \App\Exception\Auth
     */
    public function __invoke(
        Request $request,
        Response $response,
        Route $next
    ): ResponseInterface {

        $user = $this->getDataByToken($this->getValidToken($request));

        $dbUser = $this->validateUserObject($user);

        if (!UserRepository::isAdmin($dbUser)) {
            User::raiseForbidden();
        }

        // Setting user info from JWT.
        $object = (array) $request->getParsedBody();
        $object['user'] = $dbUser;
        $this->container['sesUser'] = $dbUser;

        // api key binding in params
        $parsedParams = $this->getParamsWithApiKey($request);

        return $next($request->withParsedBody($object)->withQueryParams($parsedParams), $response);
    }
}

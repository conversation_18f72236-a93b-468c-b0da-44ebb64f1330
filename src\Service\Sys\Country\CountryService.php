<?php

declare(strict_types=1);

namespace App\Service\Sys\Country;

use App\Models\Sys\Country;

final class CountryService extends Base
{
    public function create(array $input): Country
    {
        return Country::query()->create($input);
    }

    public function update($input, $id): Country
    {
        $input['id'] = $id;
        return Country::query()->update($input);
    }

    public function getOne(int $id, array $params=[]): Country
    {
        return $this->countryRepository->getQueryBuilder()->where('id', $id)->first();
    }

    public function getCountrysByPage(
        int $page,
        int $perPage,
        ?array $params
    ): array
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($perPage < 1) {
            $perPage = self::DEFAULT_PER_PAGE_PAGINATION;
        }

        return $this->countryRepository->getCountrysByPage(
            $page,
            $perPage,
            $params
        );
    }

    public function delete($ids): void
    {
        $arr = is_array($ids) ? $ids : explode(',', $ids);
        $this->countryRepository->getQueryBuilder()->whereIn('id', $arr)->delete();
    }
}

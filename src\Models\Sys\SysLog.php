<?php

namespace App\Models\Sys;

use App\Models\BaseModel;
use App\Models\User;

/**
 * @property integer $id
 * @property string $category
 * @property string $name
 * @property string $note
 * @property string $status
 * @property integer $ean_id
 * @property string $ref1
 * @property string $ref2
 * @property string $request_uri
 * @property string $request_method
 * @property integer $created_by
 * @property string $created_on
 * @property integer $sync_origin_id
 *
 *
 * @property User $user
 */
class SysLog extends BaseModel
{
    const UPDATED_AT = null;
    public $timestamps = [
        self::CREATED_AT
    ];

    const CATEGORY_LOGIN = 'login';
    const CATEGORY_ITEM_STATS_CRON = 'Cron - Item Stats';
    const CATEGORY_EMAIL_TRACKING = 'Email Tracking supplier';
    const CATEGORY_EMAIL_TRACKING_OFFER_TPL = 'Email Tracking supplier_offer_tpl';
    const CATEGORY_EMAIL_TRACKING_CALL = 'Email Tracking supplier_call';

    const NAME_EMAIL_TRACKING_SEEN = 'Seen';

    const STATUS_STARTED = 'started';
    const STATUS_PROCESSING = 'processing';
    const STATUS_SUCCESS = 'success';
    const STATUS_ERROR = 'error';

    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'sys_log';

    /**
     * The "type" of the auto-incrementing ID.
     * 
     * @var string
     */
    protected $keyType = 'integer';

    /**
     * @var array
     */
    protected $fillable = ['category', 'name', 'note', 'status', 'ean_id', 'ref1', 'ref2', 'request_uri', 'request_method', 'created_by', 'created_on'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by', 'user_id');
    }
}

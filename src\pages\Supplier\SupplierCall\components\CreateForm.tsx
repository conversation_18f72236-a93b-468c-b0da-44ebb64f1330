import type { Dispatch, SetStateAction } from "react";
import React, { useEffect, useRef } from "react";
import type { ProFormInstance } from "@ant-design/pro-form";
import { ProFormDependency } from "@ant-design/pro-form";
import { ModalForm } from "@ant-design/pro-form";
import { addSupplierCall } from "@/services/app/Supplier/supplier-call";
import { CheckboxOptionType, message } from "antd";
import Util, { getFormData } from "@/util";
import { LS_TOKEN_NAME, SupplierCallDirection, SupplierCallDirectionKv, SupplierCallType, SupplierCallTypeKv } from "@/constants";
import { ProForm, ProFormRadio, ProFormTextArea, ProFormUploadDragger } from "@ant-design/pro-components";
import { useModel } from "@umijs/max";
import HtmlEditor from "@/components/HtmlEditor";
import { RcFile, UploadFile } from "antd/es/upload";

type FormValueType = Omit<Partial<API.SupplierCall>, "files"> & {
  copy_to_sk?: "" | "yes" | "yes2";
} & { files?: UploadFile[] };

const handleAdd = async (fields: FormValueType) => {
  const hide = message.loading("Adding...", 0);

  const formData = getFormData(fields);

  if (fields.files?.length) {
    fields.files.forEach((file, ind) => {
      formData.set(`files[${ind}]`, file?.originFileObj ?? "");
    });
  }

  try {
    await addSupplierCall(formData);
    message.success("Added successfully");
    return true;
  } catch (error: any) {
    Util.error(error);
    return false;
  } finally {
    hide();
  }
};

export type CreateFormProps = {
  initialValues?: Partial<API.SupplierCall>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: FormValueType) => void;
};

export const SupplierCallTypeOptions: CheckboxOptionType[] = [
  {
    value: SupplierCallType.Phone,
    label: SupplierCallTypeKv[SupplierCallType.Phone],
  },
  {
    value: SupplierCallType.Email,
    label: SupplierCallTypeKv[SupplierCallType.Email],
  },
  {
    value: SupplierCallType.Meeting,
    label: SupplierCallTypeKv[SupplierCallType.Meeting],
  },
  {
    value: SupplierCallType.Notes,
    label: SupplierCallTypeKv[SupplierCallType.Notes],
  },
  {
    value: SupplierCallType.Offer,
    label: SupplierCallTypeKv[SupplierCallType.Offer],
  },
];

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const { initialValues, modalVisible, handleModalVisible, onSubmit } = props;

  const { initialState } = useModel("@@initialState");

  const formRef = useRef<ProFormInstance>();

  useEffect(() => {
    if (modalVisible) {
      // const savedFormValues = Util.getSfValues('f_new_call', {});
      formRef.current?.resetFields();
      formRef.current?.setFieldsValue({
        ...(initialValues ?? {
          type: SupplierCallType.Phone,
          direction: SupplierCallDirection.Out,
        }),
        // copy_to_sk: initialState?.currentUser?.initials == 'SK' ? '' : 'yes',
        copy_to_sk: "",
      });
    }
  }, [initialState?.currentUser?.initials, initialValues, modalVisible]);

  return (
    <ModalForm<FormValueType>
      title={"New Supplier Conversion"}
      width="800px"
      open={modalVisible}
      onOpenChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ flex: "90px" }}
      wrapperCol={{ flex: "auto" }}
      formRef={formRef}
      onFinish={async (value) => {
        Util.setSfValues("f_new_call", { ...value, note: null });

        const newValue: any = {
          ...value,
          supplier_id: initialValues?.supplier_id,
        } as API.SupplierCall;
        if (newValue.type == SupplierCallType.Offer) {
          newValue.direction = null;
        }

        const success = await handleAdd(newValue);
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          if (onSubmit) onSubmit(value);
          handleModalVisible(false);
        }
      }}
    >
      <ProFormRadio.Group name="type" label="Type" options={SupplierCallTypeOptions} />

      <ProFormDependency name={["type"]}>
        {(deps) => {
          if (deps.type == SupplierCallType.Offer) return null;
          else return <ProFormRadio.Group name="direction" label="Direction" valueEnum={SupplierCallDirectionKv} />;
        }}
      </ProFormDependency>

      {/* <ProFormDependency name={['type']}>
        {(deps) => {
          if (deps.type == SupplierCallType.Offer)
            return <ProFormDigit name="offer_no" label="Offer No" width="sm" />;
          else return null;
        }}
      </ProFormDependency> */}

      <ProFormDependency name={["type"]}>
        {(deps) => {
          if (deps.type == SupplierCallType.Offer) return <ProFormTextArea name="comment" label="Comment" fieldProps={{ rows: 5 }} />;
          else return null;
        }}
      </ProFormDependency>

      <ProForm.Item name={"note"} label={"Notes"} style={{ width: "100%" }} labelCol={undefined} wrapperCol={{ span: 24 }}>
        <HtmlEditor id={`call_note_create`} initialFocus enableTextModule hideMenuBar toolbarMode={2} height={400} />
      </ProForm.Item>

      <ProFormUploadDragger
        name="files"
        label="Files"
        title={false}
        description="Please select files or drag & drop"
        // accept="image/*"
        fieldProps={{
          multiple: true,
          listType: "picture-card",
          name: "file",
          headers: {
            Authorization: `Bearer ${localStorage.getItem(LS_TOKEN_NAME)}`,
          },
          style: { marginBottom: 24 },
          beforeUpload: (file: RcFile, fileList: RcFile[]) => {
            return false;
          },
        }}
      />

      <ProFormRadio.Group
        name="copy_to_sk"
        label="Copy to SK"
        options={[
          { value: "", label: "No" },
          { value: "yes", label: "Yes" },
          { value: "yes2", label: "Yes with files" },
        ]}
      />
    </ModalForm>
  );
};

export default CreateForm;

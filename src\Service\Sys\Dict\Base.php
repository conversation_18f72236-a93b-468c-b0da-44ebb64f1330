<?php

declare(strict_types=1);

namespace App\Service\Sys\Dict;

use App\Service\BaseService;
use App\Repository\Sys\DictRepository;
use Slim\Container;

abstract class Base extends BaseService
{
    private const REDIS_KEY = 'SysDict:%s';
    protected DictRepository $dictRepository;

    public function __construct(Container $container)
    {
        $this->dictRepository = $container->get(DictRepository::class);
    }

    public function getDictRepository()
    {
        return $this->dictRepository;
    }
}


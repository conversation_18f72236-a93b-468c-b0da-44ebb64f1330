<?php

declare(strict_types=1);

namespace App\Repository\Customer;

use App\Lib\Func;
use App\Lib\FuncModel;
use App\Models\Customer\CustomerCall;
use App\Repository\BaseRepositoryORM;
use Illuminate\Database\Eloquent\Builder;

final class CustomerCallRepository extends BaseRepositoryORM
{
    public function getQueryCustomerCallsByPage($params = []): Builder
    {
        $qb = $this->getQueryBuilder();

        if (!empty($params)) {
            if ($params['customer_id'] ?? null) {
                $qb->where('customer_id', $params['customer_id']);
            }
            if ($params['like_customer_name'] ?? null) {
                $qb->whereHas('customer', function($builder) use (&$params) {
                    $builder->where('name', 'like', FuncModel::likeValue($params['like_customer_name'], '%'));
                });
            }

            if (Func::keyExistsInWithParam($params, 'customer')) {
                $qb->with('customer');
            }

            $qb = $this->applyOrderBy($qb, $params);
        }

        return $qb;
    }

    /**
     * Get lists by pagination.
     *
     * @param int $page
     * @param int $perPage
     * @param array|null $params
     * @return array
     */
    public function getCustomerCallsByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        $query = $this->getQueryCustomerCallsByPage($params);
        $total = $this->getCountByQuery($query);

        $result = $this->getResultsWithPagination(
            $query,
            $page,
            $perPage,
            $total
        );

        return $result;
    }

    public function getQueryBuilder(): Builder
    {
        return CustomerCall::query();
    }
}
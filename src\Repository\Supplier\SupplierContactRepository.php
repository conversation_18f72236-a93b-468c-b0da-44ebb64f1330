<?php

declare(strict_types=1);

namespace App\Repository\Supplier;

use App\Lib\Func;
use App\Lib\FuncModel;
use App\Models\Supplier\SupplierContact;
use App\Repository\BaseRepositoryORM;
use Illuminate\Database\Eloquent\Builder;

final class SupplierContactRepository extends BaseRepositoryORM
{
    public function getQuerySupplierContactsByPage($params = []): Builder
    {
        $qb = $this->getQueryBuilder();

        if (!empty($params)) {
            if ($keyWords = $params['keyWords'] ?? null) {
                $qb->where(function (Builder $builder) use (&$keyWords) {
                    $builder
                        ->where('firstname', 'like', FuncModel::likeValue($keyWords))
                        ->orWhere('lastname', 'like', FuncModel::likeValue($keyWords))
                        ->orWhere('email', 'like', FuncModel::likeValue($keyWords))
                        ->orWhereHas('supplier', function (Builder $builder) use (&$keyWords) {
                            $builder
                                ->where('name', 'like', FuncModel::likeValue($keyWords))
                                ->orWhere('org_a', 'like', FuncModel::likeValue($keyWords));
                        });
                });
            }

            if (Func::keyExistsInWithParam($params, 'supplier')) {
                $qb->with('supplier');
            }

            $qb = $this->applyOrderBy($qb, $params);
        }

        return $qb;
    }

    /**
     * Get lists by pagination.
     *
     * @param int $page
     * @param int $perPage
     * @param array|null $params
     * @return array
     */
    public function getSupplierContactsByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        $query = $this->getQuerySupplierContactsByPage($params);
        $total = $this->getCountByQuery($query);

        $result = $this->getResultsWithPagination(
            $query,
            $page,
            $perPage,
            $total
        );

        return $result;
    }

    public function getQueryBuilder(): Builder
    {
        return SupplierContact::query();
    }
}
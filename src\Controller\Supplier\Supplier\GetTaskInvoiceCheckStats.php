<?php

declare(strict_types=1);

namespace App\Controller\Supplier\Supplier;

use App\App\Constants;
use App\Lib\Func;
use App\Lib\FuncDate;
use App\Models\Supplier\Supplier;
use App\Models\User;
use App\ModelsOrg\OrgSupplier;
use App\ModelsOrg\OrgVSupplier;
use App\ModelsTask\LexOffice\LoInvoiceCheck;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Slim\Http\Request;
use Slim\Http\Response;

/**
 * Getting v_findetails_all stats from WHC_Task
 */
final class GetTaskInvoiceCheckStats extends Base
{
    public function __invoke(Request $request, Response $response): Response
    {
        $page = intval($request->getQueryParam('page', 1));
        $perPage = intval($request->getQueryParam('perPage', Constants::DEFAULT_PER_PAGE_PAGINATION));
        $params = $request->getParams();

        $qb = LoInvoiceCheck::query()->from('v_findetails_all', 'fd');
        $qb->select('supplier.supp_supplier_id');
        $qb->selectRaw('supplier.supp_supplier_id AS id');
        $qb->addSelect('supplier.name');
        $qb->addSelect('org_supplier.org_a');

        $qb->leftJoinSub('select distinct `name`, `org_a`, `supp_supplier_id` from `supplier`', 'supplier', 'supplier.name', '=', 'fd.supplier_name');
        $qb->leftJoinSub(OrgSupplier::query()
            ->select('suppliers.org_a')
            ->addSelect('suppliers.supp_supplier_id')
            ->groupBy('suppliers.supp_supplier_id')
            , 'org_supplier', 'org_supplier.supp_supplier_id', '=', 'supplier.supp_supplier_id');

        if ($params['supp_supplier_id'] ?? null) {
            $qb->where('supplier.supp_supplier_id', $params['supp_supplier_id']);
        }

        if (($in_org_a = ($params['in_org_a'] ?? null)) && !in_array('all', $in_org_a)) {
            // If includes empty orgA?
            if (in_array('-', $in_org_a)) {
                $qb->where(function (Builder $builder) use (&$in_org_a) {
                    $builder->whereExists(function (\Illuminate\Database\Query\Builder $builder) use (&$in_org_a) {
                        $builder
                            ->select('id')
                            ->fromSub(OrgSupplier::query()
                                ->whereIn('org_a', $in_org_a)
                                ->orWhereRaw("IFNULL(org_a, '')=''")
                                , 't')
                            ->whereColumn('supplier.supp_supplier_id', 't.supp_supplier_id');
                    })->orWhereNotExists(function (\Illuminate\Database\Query\Builder $builder) {
                        $builder
                            ->select('id')
                            ->fromSub(OrgSupplier::query(), 't')
                            ->whereColumn('supplier.supp_supplier_id', 't.supp_supplier_id');
                    });
                });
            } else {
                $qb->whereExists(function (\Illuminate\Database\Query\Builder $builder) use (&$in_org_a) {
                    $builder
                        ->select('id')
                        ->fromSub(OrgSupplier::query()->whereIn('org_a', $in_org_a), 't')
                        ->whereColumn('supplier.supp_supplier_id', 't.supp_supplier_id');
                });
            }
        }

        // user permission
        $user = Func::getSesUser();
        $userPermOption = $user['settings']["org_stats_item"] ?? 'option1';
        $permOption = $userPermOption;
        $requestedPermOption = $params['org_stats_user_level'] ?? 'option1';
        if ($requestedPermOption <= $userPermOption) {
            $permOption = $requestedPermOption;
        }
        if ($permOption == 'option1') {
            $qb->where('fd.Artikelnummer', '<', 50)
                ->where('fd.Artikelnummer', '!=', 21);
        } else if ($permOption == 'option2') {
            $qb->where('fd.Artikelnummer', '<', 50);
        }
        // Always view >= 10 since 2025-08-04
        $qb->where('fd.Artikelnummer', '>=', 10);


        // Months filter
        $viewMode = $params['view_mode'] ?? 'monthly';
        if ($viewMode == 'monthly') {
            $minY = '9999-32';
            $maxY = '0000-00';
            $months = $params['months'] ?? [];
            if ($months) {
                $cntMonths = count($months);
                foreach ($months as $ind => $month) {
                    $ym = $month['from'];
                    $where = "LEFT(fd.Datum, 7)='$ym'";
                    $qb->selectRaw("SUM(IF($where, fd.EK_Summe, 0)) AS EK_Summe_$ind");
                    $qb->selectRaw("SUM(IF($where, fd.Summe, 0)) AS Summe_$ind");
                    $qb->selectRaw("SUM(IF($where, fd.Ertrag, 0)) AS Ertrag_$ind");
                    if ($ym < $minY) $minY = $ym;
                    if ($ym > $maxY) $maxY = $ym;
                }

                // Summing up
                $whereTotal = "LEFT(fd.Datum, 7) >= '$minY' AND LEFT(fd.Datum, 7) <= '$maxY'";
                $qb->selectRaw("SUM(IF($whereTotal, fd.EK_Summe, 0)) AS EK_Summe_total");
                $qb->selectRaw("SUM(IF($whereTotal, fd.Summe, 0)) AS Summe_total");
                $qb->selectRaw("SUM(IF($whereTotal, fd.Ertrag, 0)) AS Ertrag_total");

                // AVG: Qty Months of previous fiscal year + qty.months of current year.
                $firstMonth = $months[0]['from'];
                $lastMonth = $months[$cntMonths - 1]['from'];

                $prevFromYm = sprintf('%s-%02d', intval(substr($firstMonth, 0, 4)) - 1, FY_START_MONTH);
                // need to check by last month.
                $prevToYm = min($lastMonth, date('Y-m', strtotime('last day of -1 months')));
                $prevToYmFull = date(DATE_FORMAT_YMD, strtotime('last day of 0 months', strtotime($prevToYm . '-01')));
                $qtyMonths = FuncDate::getMonthsDiff($prevFromYm . '-01', $prevToYmFull) + 1;

                $whereAvg = "LEFT(fd.Datum, 7) >= '$prevFromYm' AND LEFT(fd.Datum, 7) <= '$prevToYm'";
                $qb->selectRaw("SUM(IF($whereAvg, fd.EK_Summe, 0)) / $qtyMonths AS EK_Summe_prev");
                $qb->selectRaw("SUM(IF($whereAvg, fd.Summe, 0)) / $qtyMonths AS Summe_prev");
                $qb->selectRaw("SUM(IF($whereAvg, fd.Ertrag, 0)) / $qtyMonths AS Ertrag_prev");
                if ($prevFromYm < $minY) $minY = $prevFromYm;
                if ($prevToYm > $maxY) $maxY = $prevToYm;

                $qb->whereRaw('LEFT(fd.Datum, 7) >= ?', [$minY]);
                $qb->whereRaw('LEFT(fd.Datum, 7) <= ?', [$maxY]);
            }
        } else {
            $nLastYearsCount = intval($params['years_count'] ?? 6);
            $fy_this_year = FuncDate::getFiscalYear(FY_START_MONTH);

            $minY = '9999-12-31';
            $maxY = '1900-01-01';
            $y = 0;
            for ($ind = 0; $ind < $nLastYearsCount; $ind++) {
                $y = $fy_this_year - $nLastYearsCount + $ind;

                $from = sprintf('%s-%02d-01', $y, FY_START_MONTH);
                $to = sprintf('%s-%02d-01', $y + 1, FY_START_MONTH);

                $where = "fd.Datum>='$from' AND fd.Datum<'$to'";
                $qb->selectRaw("SUM(IF($where, fd.EK_Summe, 0)) AS EK_Summe_$ind");
                $qb->selectRaw("SUM(IF($where, fd.Summe, 0)) AS Summe_$ind");
                $qb->selectRaw("SUM(IF($where, fd.Ertrag, 0)) AS Ertrag_$ind");
                if ($from < $minY) $minY = $from;
                if ($to > $maxY) $maxY = $to;
            }

            // Summing up
            $whereTotal = "fd.Datum>='$minY' AND fd.Datum<'$maxY'";
            $qb->selectRaw("SUM(IF($whereTotal, fd.EK_Summe, 0)) AS EK_Summe_total");
            $qb->selectRaw("SUM(IF($whereTotal, fd.Summe, 0)) AS Summe_total");
            $qb->selectRaw("SUM(IF($whereTotal, fd.Ertrag, 0)) AS Ertrag_total");

            $qb->whereRaw('YEAR(fd.Datum) >= ?', [$minY]);
            $qb->whereRaw('YEAR(fd.Datum) <= ?', [$maxY]);
        }

        $qbSummary = $qb->clone();
        $qb->groupBy('supplier.supp_supplier_id');

        // $qb->orderBy(OrgSupplier::query()->select('org_a')->whereColumn('supplier.supp_supplier_id', 'suppliers.supp_supplier_id'));
        $qb = LoInvoiceCheck::query()->from($qb, 'a');

        $totalCount = $qb->count();

        $qb->with('suppSupplier');

        // $qb->orderBy('org_a');
        // $qb->orderBy('supplier.name');
        $qb->orderByDesc('EK_Summe_total');
        if ($viewMode == 'monthly') {
            $qb->orderByDesc('EK_Summe_prev');
        }

        $result = $this->supplierService->getSupplierRepository()->getResultsWithPagination($qb, $page, $perPage, $totalCount);

        if (Func::keyExistsInWithParam($params, 'org_supplier.details')) {
            if ($result['pagination']['totalRows'] > 0) {
                $sup_supplier_ids = [];
                foreach ($result['data'] as $x) {
                    if ($x['supp_supplier'] ?? null) $sup_supplier_ids[] = $x['supp_supplier']['id'];
                }

                if ($sup_supplier_ids) {
                    $qbOrgSupp = OrgVSupplier::query()->whereIn('supp_supplier_id', $sup_supplier_ids);
                    $qbOrgSupp->with('info');
                    $qbOrgSupp->with(['top3Comments' => function (HasMany $hasMany) {
                        $hasMany->latest('created_on')->limit(3);
                        $hasMany->with('conf:id,code,name');
                        $hasMany->with('creator:id,display_name,username');
                    }]);

                    $kv = $qbOrgSupp->get()->keyBy('supp_supplier_id')->toArray();
                    foreach ($result['data'] as &$x2) {
                        $x2['org_supplier'] = $kv[$x2['supp_supplier_id']] ?? null;
                    }

                    // Get merged array with latest 3 inactive offers and all active offers for OrgSupplier
                    $orgSupplierIds = [];
                    foreach ($result['data'] as $x) {
                        if ($x['org_supplier']['id'] ?? null) {
                            $orgSupplierIds[] = $x['org_supplier']['id'];
                        }
                    }

                    if ($orgSupplierIds) {
                        // Get all active offers (status = 1)
                        $activeOffers = \App\ModelsOrg\OrgOffer::query()
                            ->whereIn('supplier_id', $orgSupplierIds)
                            ->where('status', 1)
                            ->orderBy('created_on', 'desc')
                            ->with('comments')
                            ->get()
                            ->groupBy('supplier_id')
                            ->toArray();

                        // Get latest 3 inactive offers (status = 0)
                        $inactiveOffers = \App\ModelsOrg\OrgOffer::query()
                            ->whereIn('supplier_id', $orgSupplierIds)
                            ->where('status', 0)
                            ->orderBy('updated_on', 'desc')
                            ->with('comments')
                            ->limit(3)
                            ->get()
                            ->groupBy('supplier_id')
                            ->toArray();

                        // Merge offers for each supplier
                        foreach ($result['data'] as &$x3) {
                            if ($x3['org_supplier']['id'] ?? null) {
                                $supplierId = $x3['org_supplier']['id'];
                                $mergedOffers = [];

                                // Add all active offers
                                if (isset($activeOffers[$supplierId])) {
                                    $mergedOffers = array_merge($mergedOffers, $activeOffers[$supplierId]);
                                }

                                // Add latest 3 inactive offers
                                if (isset($inactiveOffers[$supplierId])) {
                                    $mergedOffers = array_merge($mergedOffers, $inactiveOffers[$supplierId]);
                                }

                                // Sort merged offers by created_on desc
                                usort($mergedOffers, function ($a, $b) {
                                    if (($b['updated_on'] || $b['created_on']) && ($a['updated_on'] || $a['created_on'])) {
                                        return strtotime($b['updated_on'] ?? $b['created_on']) - strtotime($a['updated_on'] ?? $a['created_on']);
                                    } else {
                                        return strcmp($b['id'], $a['id']);
                                    }
                                });

                                $x3['org_supplier']['offers'] = $mergedOffers;
                            }
                        }
                    }
                }
            }
        }


        $result['summary'] = $qbSummary->get()->toArray();

        return $this->jsonResponse($response, 'success', $result, 200);
    }
}

<?php

declare(strict_types=1);

namespace App\Repository\Supplier;

use App\Lib\Func;
use App\Lib\FuncModel;
use App\Models\Supplier\SupplierCall;
use App\Repository\BaseRepositoryORM;
use Illuminate\Database\Eloquent\Builder;

final class SupplierCallRepository extends BaseRepositoryORM
{
    public function getQuerySupplierCallsByPage($params = []): Builder
    {
        $qb = $this->getQueryBuilder();

        if (!empty($params)) {
            if ($params['supplier_id'] ?? null) {
                $qb->where('supplier_id', $params['supplier_id']);
            }
            if ($params['offer_no'] ?? null) {
                $qb->where('offer_no', $params['offer_no']);
            }
            if ($params['offer_id'] ?? null) {
                $qb->where('offer_id', $params['offer_id']);
            }
            if ($params['trademark_id'] ?? null) {
                $qb->whereHas('trademarks', function (Builder $belongsTo) use (&$params) {
                    $belongsTo->where('id', $params['trademark_id']);
                });
            }
            if ($params['org_a'] ?? null) {
                $qb->whereHas('supplier', function (Builder $belongsTo) use (&$params) {
                    $belongsTo->where('org_a', $params['org_a']);
                });
            }

            if ($params['in_type'] ?? null) {
                $qb->whereIn('type', $params['in_type']);
            }
            if ($params['comment'] ?? null) {
                $qb->where('comment', 'like', FuncModel::likeValue($params['comment'], '%'));
            }
            if ($keyWords = $params['keyWords'] ?? null) {
                $qb->where(function (Builder $builder) use (&$keyWords) {
                    $builder
                        ->where('comment', 'like', FuncModel::likeValue($keyWords, '%'))
                        ->orWhere('note', 'like', FuncModel::likeValue($keyWords, '%'))
                    ;
                });
            }

            // Relations
            if (Func::keyExistsInWithParam($params, 'supplier')) {
                $qb->with('supplier');
            }

            // Order by
            $qb = $this->applyOrderBy($qb, $params);
        }

        return $qb;
    }

    /**
     * Get lists by pagination.
     *
     * @param int $page
     * @param int $perPage
     * @param array|null $params
     * @return array
     */
    public function getSupplierCallsByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        $query = $this->getQuerySupplierCallsByPage($params);
        $total = $this->getCountByQuery($query);

        $result = $this->getResultsWithPagination(
            $query,
            $page,
            $perPage,
            $total
        );

        return $result;
    }

    public function getQueryBuilder(): Builder
    {
        return SupplierCall::query();
    }
}
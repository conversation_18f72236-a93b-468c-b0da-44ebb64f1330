{"name": "supplier/backend", "description": "Backend API App for WHC_Supplier", "version": "1.0.0", "keywords": ["php", "slim-micro-framework", "rest-api", "mysql", "slim3", "slim", "rest", "api"], "license": "MIT", "authors": [{"name": "Max", "email": "<EMAIL>"}], "minimum-stability": "dev", "prefer-stable": true, "require": {"php": ">=8.0", "ext-pdo": "*", "ext-soap": "*", "carbon-cli/carbon-cli": "^1.2", "caseyamcl/guzzle_retry_middleware": "^2.6", "fabpot/goutte": "^4.0", "firebase/php-jwt": "^5.0", "google/analytics-data": "^0.9.5", "google/apiclient": "^2.13", "greew/oauth2-azure-provider": "^2.0", "guzzlehttp/guzzle": "^7.3", "ifsnop/mysqldump-php": "^2.10", "illuminate/database": "^8.83", "illuminate/events": "^8.83", "imangazaliev/didom": "^2.0", "intervention/image": "^2.7", "lazzard/php-ftp-client": "^1.7", "league/flysystem": "^2.4", "league/oauth2-client": "^2.8", "monolog/monolog": "^2.5", "mpdf/mpdf": "^8.1", "mpdf/qrcode": "^1.2", "palanik/corsslim": "dev-slim3", "php-imap/php-imap": "^5.0", "phpmailer/phpmailer": "^6.8", "phpoffice/phpspreadsheet": "^1.24", "phpoffice/phpword": "^1.3", "predis/predis": "^1.1", "respect/validation": "^1.1", "slim/php-view": "^3.2", "slim/slim": "^3.12.3", "spomky-labs/otphp": "^10.0", "staudenmeir/eloquent-eager-limit": "^1.6", "vlucas/phpdotenv": "^5.1", "webklex/php-imap": "^4.1"}, "require-dev": {"phpunit/phpunit": "^9.0"}, "config": {"sort-packages": true, "process-timeout": 0, "platform": {"php": "8.0"}, "allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": false, "composer/package-versions-deprecated": false}}, "autoload": {"psr-4": {"App\\": "src/", "Tests\\": "tests/"}}, "scripts": {"post-create-project-cmd": ["php -r \"copy('.env.example', '.env');\"", "php extras/bin/post-create-project-command.php"], "coverage": "phpunit --coverage-html=coverage --coverage-text", "database": "php extras/bin/restart-db.php", "phpstan": "./vendor/bin/phpstan analyse src tests --level=max", "restart": "php extras/bin/restart-db.php", "restart-db": "php extras/bin/restart-db.php", "gen-gwt": "php extras/bin/gen-jwt.php", "start": "php -S localhost:8080 -t public public/index.php", "test": "phpunit"}}
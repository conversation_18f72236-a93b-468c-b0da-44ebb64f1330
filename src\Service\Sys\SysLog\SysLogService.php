<?php

declare(strict_types=1);

namespace App\Service\Sys\SysLog;

use App\Models\Sys\SysLog;

final class SysLogService extends Base
{
    public function create($params=[]): SysLog
    {
        if (!isset($params['status'])) {
            $params['status'] = SysLog::STATUS_SUCCESS;
        }

        return SysLog::create($params);
    }

    public function getOne(int $id, array $params=[]): SysLog
    {
        return $this->sysLogRepository->getQueryBuilder()->where('id', $id)->first();
    }

    public function getSysLogsByPage(
        int $page,
        int $perPage,
        ?array $params
    ): array
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($perPage < 1) {
            $perPage = self::DEFAULT_PER_PAGE_PAGINATION;
        }

        return $this->sysLogRepository->getSysLogsByPage(
            $page,
            $perPage,
            $params
        );
    }

    public function delete($ids): void
    {
        $arr = is_array($ids) ? $ids : explode(',', $ids);
        $this->sysLogRepository->getQueryBuilder()->whereIn('id', $arr)->delete();
    }

    public static function saveLog($category, $name, $status, $note=null, $extra=[])
    {
        /*$log = new SysLog();
        $log->fill(compact('category', 'name', 'status', 'note'));
        if ($extra) {
            $log->fill($extra);
        }*/

        return SysLog::create(array_merge_recursive(compact('category', 'name', 'status', 'note'), $extra ?? []));
    }

    public static function saveLogSuccess($category, $name, $note=null, $extra=[])
    {
        $status = SysLog::STATUS_SUCCESS;

        self::saveLog($category, $name, $status, $note, $extra);
    }


}

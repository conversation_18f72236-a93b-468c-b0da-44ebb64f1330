<?php

declare(strict_types=1);

namespace App\Controller\Sys\SysLog;

use App\Service\MediaApi\MediaApiBaseService;
use Slim\Http\Request;
use Slim\Http\Response;

final class DsEmailTrackingData extends Base
{
    public function __invoke(Request $request, Response $response): Response
    {
        /** @var MediaApiBaseService $mediaApiService */
        $mediaApiService = $this->container->get(MediaApiBaseService::class);

        $mediaApiService->dsEmailTrackingHistory();

        return $this->jsonResponse($response, 'success', true, 200);
    }
}

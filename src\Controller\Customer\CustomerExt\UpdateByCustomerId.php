<?php

declare(strict_types=1);

namespace App\Controller\Customer\CustomerExt;

use Slim\Http\Request;
use Slim\Http\Response;

final class UpdateByCustomerId extends Base
{
    public function __invoke(Request $request, Response $response, array $args): Response
    {
        $id = $args['id'] ?? null;
        if (!$id) {
            \App\Exception\Base::raiseInvalidRequest('ID is required.');
        }

		$input = (array) $request->getParsedBody();
        $row = $this->customerExtService->updateByCustomerId($id, $input);

        return $this->jsonResponse($response, 'success', $row, 200);
    }
}

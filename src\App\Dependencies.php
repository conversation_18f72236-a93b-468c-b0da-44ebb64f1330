<?php

declare(strict_types=1);

use App\Handler\ApiError;
use App\Handler\PHPRuntimeError;
use App\Handler\NotFoundError;
use App\Service\RedisService;
use Monolog\Handler\RotatingFileHandler;
use Psr\Container\ContainerInterface;

if (!defined('DATE_FORMAT_YMD')) define('DATE_FORMAT_YMD', 'Y-m-d');
if (!defined('DATE_FORMAT_YMD_HIS')) define('DATE_FORMAT_YMD_HIS', 'Y-m-d H:i:s');
if (!defined('DATE_FORMAT_YMDHI')) define('DATE_FORMAT_YMDHI', 'YmdHi');
if (!defined('DATE_FORMAT_DMY_HI')) define('DATE_FORMAT_DMY_HI', 'd.m.y H:i');
if (!defined('DATE_FORMAT_DMY_HIS')) define('DATE_FORMAT_DMY_HIS', 'd.m.y H:i:s');
if (!defined('DATE_FORMAT_DMY')) define('DATE_FORMAT_DMY', 'd.m `y');
if (!defined('DATE_FORMAT_DMY2')) define('DATE_FORMAT_DMY2', 'd.m.y');
if (!defined('DATE_FORMAT_DMY_F')) define('DATE_FORMAT_DMY_F', 'd.m.Y');    // Full dd.mm.yyyy
if (!defined('DATE_EMPTY_STR')) define('DATE_EMPTY_STR', '0000-00-00');
if (!defined('DT_MAX_TS')) define('DT_MAX_TS', 999999999999);        // Max time stamp in epoch format.
if (!defined('DT_MAX_TIME')) define('DT_MAX_TIME', ' 23:59:59');        // Max time in " H:i:s".

// Date Range definition
if (!defined('DR_TODAY')) {
    define('DR_TODAY', 'DR_T');
    define('DR_YESTERDAY', 'DR_YESTERDAY');
    define('DR_2_DAYS_AGO', 'DR_2_DAYS_AGO');
    define('DR_THIS_WEEK', 'DR_TW');
    define('DR_LAST_WEEK', 'DR_LW');
    define('DR_LAST_30_DAYS', 'DR_LAST_30_DAYS');
    define('DR_THIS_MONTH', 'DR_TM');
    define('DR_LAST_MONTH', 'DR_LM');
    define('DR_THIS_YEAR', 'DR_TY');
    define('DR_LAST_YEAR', 'DR_LY');
    define('DR_SINCE_BEGIN', 'DR_SB');
    define('DR_CUSTOM', 'DR_CUSTOM');
    define('DR_FY_THIS', 'DR_FY_THIS'); // Current fiscal year
    define('DR_FY_LAST', 'DR_FY_LAST'); // last fiscal year

    // Start month in Fiscal. July
    define('FY_START_MONTH', 7);
    define('DAY_SECONDS', 24 * 3600);
}

if (!defined('ORG_DB')) define('ORG_DB', 'db_org');
if (!defined('TASK_DB')) define('TASK_DB', 'db_task');

require_once __DIR__ . '/Constants.php';

// monolog
$container['logger'] = static function(ContainerInterface $container) {
    $settings = $container->get('settings')['logger'];
    $logger = new Monolog\Logger($settings['name']);
    $formatter = new Monolog\Formatter\LineFormatter(
        null, // Format of message in log, default [%datetime%] %channel%.%level_name%: %message% %context% %extra%\n
        null, // Datetime format
        true, // allowInlineLineBreaks option, default false
        true  // ignoreEmptyContextAndExtra option, default false
    );
    // $logger->pushProcessor(new Monolog\Processor\UidProcessor());
    $logRotate = new RotatingFileHandler($settings['path'] . "/".  $settings['name'] . ".log", 15, $settings['level']);
    $logRotate->setFormatter($formatter);
    $logger->pushHandler($logRotate);
    return $logger;
};


// db instance
/** var $container['dbORM'] Manager */
$container['dbORM'] = static function ($c) {
    $capsule = new \Illuminate\Database\Capsule\Manager;
    $settings = $c['settings']['db'];
    $capsule->addConnection([
        'driver' => 'mysql',
        'host' => $settings['host'],
        'database' => $settings['name'],
        'username' => $settings['user'],
        'password' => $settings['pass'],
        'charset'   => 'utf8mb4',
        'collation' => 'utf8mb4_general_ci',
        'prefix'    => '',
        /*'options'   => [
            // NOTE: This returns mysql query result set data as string.
            \PDO::ATTR_EMULATE_PREPARES => true
        ]*/
    ]);

    $capsule->setAsGlobal();

    // Logging for default collection.
    // ---------------------------------
    $capsule->getConnection()->enableQueryLog();
    $capsule->getConnection()->setEventDispatcher(new \Illuminate\Events\Dispatcher);
    $capsule->getConnection()->listen(function (\Illuminate\Database\Events\QueryExecuted $query) use ($c) {
        /** @var \Monolog\Logger $logger */
        $logger = $c->get('logger');

        $sql = $query->sql;
        $bindings = $query->bindings;
        $sql_with_bindings = preg_replace_callback('/\?/', function ($match) use ($sql, &$bindings) {
            return json_encode(array_shift($bindings));
        }, $sql);
        $logger->debug(PHP_EOL . " [SQL] " . $sql_with_bindings);
    });
    // ---------------------------------

    // WHC_Org database
    $settings = $c['settings'][ORG_DB];
    $capsule->addConnection([
        'driver' => 'mysql',
        'host' => $settings['host'],
        'database' => $settings['name'],
        'username' => $settings['user'],
        'password' => $settings['pass'],
        'charset'   => 'utf8mb4',
        'collation' => 'utf8mb4_general_ci',
        'prefix'    => '',
    ], ORG_DB);

    $capsule->getConnection(ORG_DB)->enableQueryLog();
    $capsule->getConnection(ORG_DB)->setEventDispatcher(new \Illuminate\Events\Dispatcher);
    $capsule->getConnection(ORG_DB)->listen(function (\Illuminate\Database\Events\QueryExecuted $query) use ($c) {
        /** @var \Monolog\Logger $logger */
        $logger = $c->get('logger');

        $sql = $query->sql;
        $bindings = $query->bindings;
        $sql_with_bindings = preg_replace_callback('/\?/', function ($match) use ($sql, &$bindings) {
            return json_encode(array_shift($bindings));
        }, $sql);
        $logger->debug(PHP_EOL . " [SQL][".ORG_DB."] " . $sql_with_bindings);
    });
    // ---------------------------------

    // WHC_Task database
    $settings = $c['settings'][TASK_DB];
    $capsule->addConnection([
        'driver' => 'mysql',
        'host' => $settings['host'],
        'database' => $settings['name'],
        'username' => $settings['user'],
        'password' => $settings['pass'],
        'charset'   => 'utf8mb4',
        'collation' => 'utf8mb4_general_ci',
        'prefix'    => '',
    ], TASK_DB);

    $capsule->getConnection(TASK_DB)->enableQueryLog();
    $capsule->getConnection(TASK_DB)->setEventDispatcher(new \Illuminate\Events\Dispatcher);
    $capsule->getConnection(TASK_DB)->listen(function (\Illuminate\Database\Events\QueryExecuted $query) use ($c) {
        /** @var \Monolog\Logger $logger */
        $logger = $c->get('logger');

        $sql = $query->sql;
        $bindings = $query->bindings;
        $sql_with_bindings = preg_replace_callback('/\?/', function ($match) use ($sql, &$bindings) {
            return json_encode(array_shift($bindings));
        }, $sql);
        $logger->debug(PHP_EOL . " [SQL][".TASK_DB."] " . $sql_with_bindings);
    });
    // ---------------------------------

    $capsule->bootEloquent();

    return $capsule;
};

$container['errorHandler'] = static function (ContainerInterface $container): ApiError {
    return new ApiError($container);
};

$container['notFoundHandler'] = static function (ContainerInterface $container): NotFoundError {
    return new NotFoundError();
};

$container['phpErrorHandler'] = static function (ContainerInterface $container): PHPRuntimeError {
    return new PHPRuntimeError($container);
};

register_shutdown_function('fatal_handler');

function fatal_handler()
{
    /** @var \Slim\Container $container */
    global $container;

    $last_error = error_get_last();
    if (!is_null($last_error)) {
        // if not Deprecated error?
        if ($last_error['type'] != 8192) {
            $container->get('logger')->error(sprintf("ERR Type: %s, %s, #%s in %s"
                , $last_error['type']
                , $last_error['message']
                , $last_error['line']
                , $last_error['file']
            ));
        }
    }
}


$container['redis_service'] = static function ($container): RedisService {
    $redis = $container->get('settings')['redis'];

    return new RedisService(new \Predis\Client($redis['url']));
};

if (!function_exists('setCorsOrigin')) {
/**
 * This is patch of the Slim framework bug in case of Exception.
 *
 * <AUTHOR>
 *
 * @param $req
 * @param $rsp
 * @return mixed
 */
    function setCorsOrigin($req, $rsp)
    {
        $origin = explode(',', $_SERVER['CORS_HOSTS']);

        // handle multiple allowed origins
        if (is_array($origin)) {

            $allowedOrigins = $origin;

            $origin = null;

            // but use a specific origin if there is a match
            foreach ($allowedOrigins as $allowedOrigin) {
                foreach ($req->getHeader("Origin") as $reqOrig) {
                    if ($allowedOrigin === $reqOrig) {
                        $origin = $allowedOrigin;
                        break;
                    }
                }
                if (!is_null($origin)) {
                    break;
                }
            }

            if (is_null($origin)) {
                // default to the first allowed origin
                $origin = reset($allowedOrigins);
            }
        }

        return $rsp->withHeader('Access-Control-Allow-Origin', $origin);
    }
}

declare namespace APIOrg {
  type Supplier = {
    id?: string;
    name?: string;
    org_a?: string;
    supp_supplier_id?: number;
  } & API.TimestampData & {
    supp_supplier?: API.Supplier;
    distinct_offers_count?: number;
    top3_comments?: APIOrg.SupplierComment[]
  };

  type VSupplier = {
    id?: string;
    name?: string;
    org_a?: string;
    supp_supplier_id?: number;
    comment?: string;
    status?: string;
    status_name?: string;
    created_by_name?: string;
    created_by_disp_name?: string;
  } & API.TimestampData & {
    info?: OrgSupplierInfo;
    comments?: SupplierComment[];
    top3_comments?: SupplierComment[];
    offers?: Offer[];
  };

  type SupplierComment = {
    id?: string;
    supplier_id?: string;
    comment?: string;
    code?: string;
    offer_id?: string;
    customer_order?: string;
    order_id?: string;
  } & API.CreatorData & API.UpdaterData & {
    creator?: APIOrg.User;
  }

  type Offer = {
    id?: string;
    offer_sid?: string;
    offer?: string;
    status?: ZeroOrOne;
    warehouse?: string;
    supplier_id?: string;
    we?: string;
    brand?: string;
    spread_out?: string;
  } & {
    offer_template?: API.OfferTemplate;
    supp_supplier?: API.Supplier;
    ext?: API.OfferExt;
    blog_cnt?: number;
    newsletter_cnt?: number;
  } & API.TimestampData;


  type OfferComment = {
    id?: string;
    offer_id?: string;
    comment?: string;
    sc_id?: string;
    customer_id?: string;
    customer_order?: string;
  } & {
    sys_config?: SysConfig;
  } & API.CreatorData & API.UpdaterData;

  type SysConfig = {
    code?: string;
    name?: string;
    value?: string;
    type?: string;
    type_sub?: string;
    order?: string;
    parent_id?: string;
    sc_customer_order?: string;
    sc_default_position?: string;
    sc_contact?: string;
    sc_customer_order_required?: string;
    option_values?: string;
    option_texts?: string;
    display_type?: string;
    value_type?: string;
    link_to_supplier?: string;
    outside_eu?: string;
    ui_border?: string;

    off_status_type?: string;
  }

  type User = {
    id?: string;
    username?: string;
    display_name?: string;
    password?: string;
    is_superuser?: boolean;
  } & API.TimestampData;
}

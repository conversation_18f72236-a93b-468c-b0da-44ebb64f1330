CREATE TABLE `offer_template`
(
    `id`         int(11)     NOT NULL AUTO_INCREMENT,
    `offer_no`   varchar(15) NOT NULL,
    `sort`       int(11)      DEFAULT NULL COMMENT 'SequenceNo',
    `title`      varchar(255) DEFAULT NULL COMMENT 'Template Title',
    `created_on` datetime     DEFAULT NULL,
    `updated_on` datetime     DEFAULT NULL,
    `created_by` int(11)      DEFAULT NULL,
    `updated_by` int(11)      DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `IDX_offer_template_sort` (`sort`),
    KEY `IDX_offer_template_title` (`title`),
    KEY `IDX_offer_template_offer_no` (`offer_no`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;

CREATE TABLE `offer_template_lang`
(
    `id`                int(11)                   NOT NULL AUTO_INCREMENT,
    `offer_template_id` int(11)                   NOT NULL COMMENT 'FK: template ID',
    `lang`              varchar(3)   DEFAULT 'DE' NOT NULL COMMENT 'FK: Lang',
    `subject`           varchar(255) DEFAULT NULL COMMENT 'Subject',
    `header`            TEXT         DEFAULT NULL COMMENT 'Header',
    `body`              longtext     DEFAULT NULL COMMENT 'Subject',
    `footer`            TEXT         DEFAULT NULL COMMENT 'Footer',
    `attachments`       longtext     DEFAULT NULL COMMENT 'Attachments in JSON',
    `created_on`        datetime     DEFAULT NULL,
    `updated_on`        datetime     DEFAULT NULL,
    `created_by`        int(11)      DEFAULT NULL,
    `updated_by`        int(11)      DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `UQ_offer_template_lang_mix` (`offer_template_id`, `lang`),
    KEY `IDX_offer_template_subject` (`subject`),
    CONSTRAINT `FK_offer_template_offer_template_id` FOREIGN KEY (`offer_template_id`) REFERENCES `offer_template` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;

CREATE TABLE `offer_template_lang_file`
(
    `offer_template_lang_id` int(11)             NOT NULL,
    `file_id`                bigint(20) unsigned NOT NULL,
    PRIMARY KEY (`offer_template_lang_id`, `file_id`),
    KEY `FK_offer_template_lang_file_file_id` (`file_id`),
    CONSTRAINT `FK_offer_template_lang_file_file_id` FOREIGN KEY (`file_id`) REFERENCES `file` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `FK_offer_template_lang_file_self_id` FOREIGN KEY (`offer_template_lang_id`) REFERENCES `offer_template_lang` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;

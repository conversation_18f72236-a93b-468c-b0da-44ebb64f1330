<?php

declare(strict_types=1);

ini_set("precision", '14');
ini_set("serialize_precision", '-1');

if (!defined('APP_PATH'))
    define('APP_PATH', __DIR__ . '/../../public');

if (!defined('APP_SRC_PATH'))
    define('APP_SRC_PATH', __DIR__ . '/..');

if (!defined('PRIVATE_DATA_PATH'))
    define('PRIVATE_DATA_PATH', __DIR__ . '/../../public/data');

return [
    'settings' => [
        'mode' => $_SERVER['MODE'],
        'displayErrorDetails' => intval($_SERVER['DISPLAY_ERROR_DETAILS'] ?? 0) == 1,
        'db' => [
            'host' => $_SERVER['DB_HOST'],
            'name' => $_SERVER['DB_NAME'],
            'user' => $_SERVER['DB_USER'],
            'pass' => $_SERVER['DB_PASS'],
            'port' => $_SERVER['DB_PORT'],
        ],
        'db_org' => [
            'host' => $_SERVER['DB_HOST_ORG'],
            'name' => $_SERVER['DB_NAME_ORG'],
            'user' => $_SERVER['DB_USER_ORG'],
            'pass' => $_SERVER['DB_PASS_ORG'],
            'port' => $_SERVER['DB_PORT_ORG'],
        ],
        'db_task' => [
            'host' => $_SERVER['DB_HOST_TASK'],
            'name' => $_SERVER['DB_NAME_TASK'],
            'user' => $_SERVER['DB_USER_TASK'],
            'pass' => $_SERVER['DB_PASS_TASK'],
            'port' => $_SERVER['DB_PORT_TASK'],
        ],

        'redis' => [
            'enabled' => $_SERVER['REDIS_ENABLED'],
            'url' => $_SERVER['REDIS_URL'],
        ],
        'app' => [
            'logLevel' => intval($_SERVER['LOG_LEVEL'] ?? 400),
            'domain' => $_SERVER['APP_DOMAIN'],
            'secret' => $_SERVER['SECRET_KEY'],
        ],
        'upload_dir' => APP_PATH . "/uploads",
        'mediaBaseUrl' => $_SERVER['MEDIA_BASE_URL'],
        'templates.path' => 'uploads/',

        // WHC media server setting
        'mediaApiUrl' => $_SERVER['MEDIA_API_URL'],
        'mediaApiToken' => $_SERVER['MEDIA_API_TOKEN'],

        // WHC_Cust server setting
        'custApiUrl' => $_SERVER['CUST_API_URL'],
        'custApiToken' => $_SERVER['CUST_API_TOKEN'],


        // M365
        'm365' => [
            'clientId' => $_SERVER['M365_CLIENT_ID'] ?? null,
            'clientSecret' => $_SERVER['M365_CLIENT_SECRET'] ?? null,
            'tenant' => $_SERVER['M365_TENANT'] ?? null,
        ],

        // Monolog settings
        'logger' => [
            'name' => 'whc_supplier',
            'path' => APP_PATH . '/logs/',
            'level' => intval($_SERVER['LOG_LEVEL'] ?? \Monolog\Logger::ERROR),
            'skipRequest' => intval($_SERVER['LOG_SKIP_REQUEST'] ?? 1),
            'skipResponse' => intval($_SERVER['LOG_SKIP_RESPONSE'] ?? 1),
        ],
    ],
];

<?php

declare(strict_types=1);

namespace App\Repository\Supplier;

use App\Lib\Func;
use App\Lib\FuncDate;
use App\Lib\FuncModel;
use App\Models\Supplier\Supplier;
use App\Models\Supplier\SupplierCall;
use App\Models\Supplier\SupplierMeta;
use App\ModelsOrg\OrgSupplier;
use App\ModelsTask\LexOffice\LoInvoiceCheck;
use App\Repository\BaseRepositoryORM;
use App\Service\BaseService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Query\JoinClause;


final class SupplierRepository extends BaseRepositoryORM
{
    public function getQuerySuppliersByPage($params = []): Builder
    {
        $qb = $this->getQueryBuilder();

        if (!empty($params)) {
            BaseService::trimInputData($params);

            if ($params['id'] ?? null) {
                $qb->where('id', $params['id']);
            }

            if ($params['firstname'] ?? null) {
                $qb->where('firstname', 'like', FuncModel::likeValue($params['firstname']));
            }
            if ($params['org_a'] ?? null) {
                $qb->where('org_a', $params['org_a']);
            }

            if ($params['user_id'] ?? null) {
                $qb->whereHas('ext', function ($qb) use (&$params) {
                    $qb->where('user_id', $params['user_id']);
                });
            }


            if ($keyWords = $params['keyWords'] ?? null) {
                $qb->where(function (Builder $builder) use (&$keyWords) {
                    $builder
                        ->where('name', 'like', FuncModel::likeValue($keyWords, '%'))
                        ->orWhere('internal_name', 'like', FuncModel::likeValue($keyWords, '%'))
                        ->orWhereHas('contacts', function (Builder $builder) use (&$keyWords) {
                            $builder
                                ->where('firstname', 'like', FuncModel::likeValue($keyWords, '%'))
                                ->orWhere('email', 'like', FuncModel::likeValue($keyWords, '%'))
                                ->orWhere('lastname', 'like', FuncModel::likeValue($keyWords, '%'));
                        });;
                });
            }

            if ($params['product_trademark'] ?? null) {
                if ($params['product_trademark'] === '-') {
                    $qb->whereDoesntHave('productTrademarks');
                } else {
                    $qb->whereHas('productTrademarks', function ($builder) use (&$params) {
                        $builder->where('value', $params['product_trademark']);
                    });
                }
            }

            if ($params['supplier_type'] ?? null) {
                if ($params['supplier_type'] === '-') {
                    $qb->whereDoesntHave('supplierType');
                } else {
                    $qb->whereHas('supplierType', function ($builder) use (&$params) {
                        $builder->where('value', $params['supplier_type']);
                    });
                }
            }

            if ($product_category = ($params['product_category'] ?? null)) {
                if ($product_category == '-') {
                    $qb->whereDoesntHave('productCategories');
                } else {
                    $qb->whereHas('productCategories', function ($builder) use (&$product_category) {
                        $builder->where('value', $product_category);
                    });
                }
            }

            // Aggregations
            if (Func::keyExistsInWithParam($params, 'calls_phone_count')) {
                $qb->withCount(['calls AS calls_phone_count' => function (Builder $builder) {
                    $builder->where('type', SupplierCall::TYPE_PHONE);
                }]);
            }

            // Relations
            if (Func::keyExistsInWithParam($params, 'address')) {
                $qb->with('address', function ($builder) use (&$params) {
                    if (Func::keyExistsInWithParam($params, 'address.country')) {
                        $builder->with('country');
                    }
                    if (Func::keyExistsInWithParam($params, 'address.countryRegion')) {
                        $builder->with('countryRegion');
                    }
                });
            }

            if (Func::keyExistsInWithParam($params, 'calls')) {
                $qb->with('calls');
            }

            if (Func::keyExistsInWithParam($params, 'info')) {
                $qb->with('info');
            }

            if (Func::keyExistsInWithParam($params, 'contacts')) {
                $qb->with('contacts', function (HasMany $builder) {
                    $builder->sortDefault();
                });
            }

            if (Func::keyExistsInWithParam($params, 'contactsAll')) {
                $qb->with('contactsAll', function (HasMany $builder) {
                    $builder->sortDefault();
                });
            }

            if (Func::keyExistsInWithParam($params, 'ext') || Func::keyExistsInWithParam($params, 'ext.user')) {
                $qb->with('ext', function ($qb) use (&$params) {
                    if (Func::keyExistsInWithParam($params, 'ext.user')) {
                        $qb->with('user');
                    }
                });
            }

            if (Func::keyExistsInWithParam($params, 'meta')) {
                $qb->with('meta');
            }

            // load WHC_Org supplier
            if (Func::keyExistsInWithParam($params, 'orgSupplier') || Func::keyExistsInWithParam($params, 'org_supplier')) {
                $qb->with('orgSupplier', function (HasOne $builder) {
                    $builder->withCount([
                        'offers as distinct_offers_count' => function (Builder $query) {
                            $query->select(Func::getDb()->raw('count(distinct offer_sid)'));
                        },
                    ]);
                });
            }
            if ($in_org_a = ($params['in_org_a'] ?? null)) {
                if (!in_array('all', $in_org_a)) {
                    if (in_array('-', $in_org_a)) {
                        $qb->where(function (Builder $builder) use (&$in_org_a) {
                            $builder->whereExists(function (\Illuminate\Database\Query\Builder $builder) use (&$in_org_a) {
                                $builder
                                    ->select('id')
                                    ->fromSub(OrgSupplier::query()
                                        ->whereIn('org_a', $in_org_a)
                                        ->orWhereRaw("IFNULL(org_a, '')=''")
                                        , 't')
                                    ->whereColumn('supplier.id', 't.supp_supplier_id');
                            })->orWhereNotExists(function (\Illuminate\Database\Query\Builder $builder) {
                                $builder
                                    ->select('id')
                                    ->fromSub(OrgSupplier::query(), 't')
                                    ->whereColumn('supplier.id', 't.supp_supplier_id');
                            });
                        });
                    } else {
                        $qb->whereExists(function (\Illuminate\Database\Query\Builder $builder) use (&$in_org_a) {
                            $builder
                                ->select('id')
                                ->fromSub(OrgSupplier::query()->whereIn('org_a', $in_org_a), 't')
                                ->whereColumn('supplier.id', 't.supp_supplier_id');
                        });
                    }
                }
            }

            if (($params['relevance'] ?? '') !== '') {
                $qb->whereHas('ext', function ($qb) use (&$params) {
                    $qb->where('relevance', sprintf("%03d", $params['relevance']));
                });
            }
            
            // Add relevance range filter
            if (isset($params['relevance_from']) || isset($params['relevance_to'])) {
                $qb->whereHas('ext', function ($qb) use (&$params) {
                    if (isset($params['relevance_from']) && $params['relevance_from'] !== '') {
                        $qb->where('relevance', '>=', sprintf("%03d", $params['relevance_from']));
                    }
                    if (isset($params['relevance_to']) && $params['relevance_to'] !== '') {
                        $qb->where('relevance', '<=', sprintf("%03d", $params['relevance_to']));
                    }
                });
            }

            $qb = $this->applyOrderBy($qb, $params);
        }

        return $qb;
    }

    /**
     * Get lists by pagination.
     *
     * @param int $page
     * @param int $perPage
     * @param array|null $params
     * @return array
     */
    public function getSuppliersByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        $query = $this->getQuerySuppliersByPage($params);
        $total = $this->getCountByQuery($query);

        $result = $this->getResultsWithPagination(
            $query,
            $page,
            $perPage,
            $total
        );

        if ($total) {
            if (Func::keyExistsInWithParam($params, 'metaLabel')) {
                $ids = [];
                foreach ($result['data'] as $row) {
                    $ids[] = $row['id'];
                }

                $productCategoryKv = SupplierMeta::query()
                    ->select('sys_product_category.id')
                    ->addSelect('sys_product_category.name')
                    ->where('type', SupplierMeta::TYPE_PRODUCT_CATEGORY)
                    ->leftJoin('sys_product_category', 'sys_product_category.id', '=', 'supplier_meta.value')
                    ->distinct()
                    ->whereIn('supplier_id', $ids)
                    ->pluck('name', 'id')->toArray();

                foreach ($result['data'] as &$row) {
                    if ($row['meta'] ?? null) {
                        foreach ($row['meta'] as &$meta) {
                            if ($meta['type'] == SupplierMeta::TYPE_PRODUCT_CATEGORY) {
                                $meta['label'] = $productCategoryKv[$meta['value']] ?? $meta['value'];
                            }
                        }
                    }
                }
            }

            if (Func::keyExistsInWithParam($params, 'taskInvoiceStats')) {
                // Load invoice stats detail from WHC_Task
                $ids = [];
                foreach ($result['data'] as $row) {
                    $ids[] = $row['id'];
                }

                $qb = LoInvoiceCheck::query()->from('v_findetails_all', 'fd');
                $qb->select('supplier.supp_supplier_id');
                $qb->selectRaw('supplier.supp_supplier_id AS id');
                $qb->selectRaw("YEAR(fd.Datum) AS y");
                $qb->selectRaw("SUM(fd.EK_Summe) AS EK_Summe");

                $qb->leftJoinSub('select distinct `name`, `org_a`, `supp_supplier_id` from `supplier`', 'supplier', 'supplier.name', '=', 'fd.supplier_name');

                /*$years = [];
                $YEAR_CNT = 4;
                $thisYear = intval(date('Y'));
                for ($i = $thisYear - $YEAR_CNT; $i <= $thisYear; $i++) {
                    $years[] = $i;
                    $qb->selectRaw("SUM(IF(YEAR(fd.Datum)=$i, fd.EK_Summe, 0)) AS EK_Summe_$i");
                    $qb->selectRaw("SUM(IF(YEAR(fd.Datum)=$i, fd.Summe, 0)) AS Summe_$i");
                    $qb->selectRaw("SUM(IF(YEAR(fd.Datum)=$i, fd.Ertrag, 0)) AS Ertrag_$i");
                }

                $qb->whereIn('supplier.supp_supplier_id', $ids);
                $qb->where('fd.Datum', '>=', $years[0] . '-01-01');
                $qb->where('fd.Datum', '<', ($years[$YEAR_CNT] + 1) . '-01-01');
                */

                $nLastYearsCount = intval($params['years_count'] ?? 4);
                $fy_this_year = FuncDate::getFiscalYear(FY_START_MONTH);
                $minY = '9999-12-31';
                $maxY = '1900-01-01';
                $y = 0;
                for ($ind = 0; $ind < $nLastYearsCount; $ind++) {
                    $y = $fy_this_year - $nLastYearsCount + $ind;
                    $from = sprintf('%s-%02d-01', $y, FY_START_MONTH);
                    $to = sprintf('%s-%02d-01', $y + 1, FY_START_MONTH);
                    $where = "fd.Datum>='$from' AND fd.Datum<'$to'";
                    $qb->selectRaw("SUM(IF($where, fd.EK_Summe, 0)) AS EK_Summe_$ind");
                    $qb->selectRaw("SUM(IF($where, fd.Summe, 0)) AS Summe_$ind");
                    $qb->selectRaw("SUM(IF($where, fd.Ertrag, 0)) AS Ertrag_$ind");
                    if ($from < $minY) $minY = $from;
                    if ($to > $maxY) $maxY = $to;
                }

                // Summing up
                $whereTotal = "fd.Datum>='$minY' AND fd.Datum<'$maxY'";
                $qb->selectRaw("SUM(IF($whereTotal, fd.EK_Summe, 0)) AS EK_Summe_total");
                $qb->selectRaw("SUM(IF($whereTotal, fd.Summe, 0)) AS Summe_total");
                $qb->selectRaw("SUM(IF($whereTotal, fd.Ertrag, 0)) AS Ertrag_total");

                $qb->whereRaw('YEAR(fd.Datum) >= ?', [$minY]);
                $qb->whereRaw('YEAR(fd.Datum) <= ?', [$maxY]);

                // user permission
                $user = Func::getSesUser();
                $userPermOption = $user['settings']["org_stats_item"] ?? 'option1';
                $permOption = $userPermOption;
                $requestedPermOption = $params['org_stats_user_level'] ?? 'option1';
                if ($requestedPermOption <= $userPermOption) {
                    $permOption = $requestedPermOption;
                }
                if ($permOption == 'option1') {
                    $qb->where('fd.Artikelnummer', '<', 50)
                        ->where('fd.Artikelnummer', '!=', 21);
                } else if ($permOption == 'option2') {
                    $qb->where('fd.Artikelnummer', '<', 50);
                }
                // Always view >= 10 since 2025-08-04
                $qb->where('fd.Artikelnummer', '>=', 10);

                $qb->groupBy('supplier.supp_supplier_id');

                $kvTmp = $qb->get()->keyBy('supp_supplier_id')?->toArray();

                foreach ($result['data'] as $key => $row) {
                    $result['data'][$key]['task_invoice_stats'] = $kvTmp[$row['id']] ?? null;
                }
            }
        }

        return $result;
    }

    public function getQueryBuilder(): Builder
    {
        return Supplier::query();
    }
}

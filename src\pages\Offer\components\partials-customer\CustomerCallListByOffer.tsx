import { DT_FORMAT_DM, SupplierCallDirection, SupplierCallDirectionKv, SupplierCallType, SupplierCallTypeKv } from "@/constants";
import Util, { nl2br, sEllipsed, sn } from "@/util";
import { DeleteOutlined, EditOutlined, EyeOutlined, SearchOutlined } from "@ant-design/icons";
import { ActionType, ProColumns, ProForm, ProFormInstance, ProFormSelect, ProFormText, ProTable } from "@ant-design/pro-components";
import { Button, Checkbox, Col, message, Popconfirm, Popover, Row, Space, Tag, Typography } from "antd";
import { SetStateAction, useEffect, useRef, useState } from "react";

import SNotesViewerModal from "@/components/SNotesViewerModal";
import { SupplierCallTypeComp } from "@/pages/Supplier/Supplier/components/SupplierCallList";
import CustomerCallCreateForm from "./CustomerCallCreateForm";
import CustomerCallUpdateForm from "./CustomerCallUpdateForm";
import { deleteCustomerCall, getCustomerCallListByPage } from "@/services/app/Customer/customer-call";
import { getCustomerListByPage } from "@/services/app/Customer/customer";
import CustomerOfferEmailViewModal from "./CustomerOfferEmailViewModal";

export type SearchFormValueType = Partial<API.SupplierCall>;

type RowType = API.CustomerCall;

type CustomerCallListByOfferProps = {
  offer_no?: string | number;
};

const CustomerCallListByOffer: React.FC<CustomerCallListByOfferProps> = (props) => {
  const { offer_no } = props;

  const actionRef = useRef<ActionType>();

  const [currentRow, setCurrentRow] = useState<RowType>();
  const [loading, setLoading] = useState(false);

  const [createModalVisible, handleCreateModalVisible] = useState<boolean>(false);
  const [updateModalVisible, handleUpdateModalVisible] = useState<boolean>(false);

  const [inc_internal_email, set_inc_internal_email] = useState<boolean>(false);
  const [notesModalVisible, setNotesModalVisible] = useState<boolean>(false);

  const [totalRecordsCount, setTotalRecordsCount] = useState<number>(0);

  // Search form
  const searchFormRef = useRef<ProFormInstance>();

  // View Email
  const [emailInfo, setEmailInfo] = useState<{ emailId?: number; emailAddr?: string }>();
  const [showDetail, setShowDetail] = useState<boolean>(false);

  const columns: ProColumns<RowType>[] = [
    {
      title: "Created on",
      dataIndex: ["date"],
      defaultSortOrder: "descend",
      sorter: true,
      width: 35,
      align: "center",
      ellipsis: true,
      render: (__, record) => Util.dtToDMY(record.date, DT_FORMAT_DM),
    },
    {
      title: "Type",
      dataIndex: ["type"],
      sorter: false,
      width: 20,
      ellipsis: true,
      align: "center",
      render: (__, record) => {
        return <SupplierCallTypeComp type={record.type} internal={record.uid?.startsWith("call_")} />;
      },
    },
    /*{
      title: "",
      dataIndex: ["direction"],
      sorter: false,
      width: 30,
      ellipsis: true,
      align: "center",
      className: "c-grey text-sm",
      render: (__, record) => {
        return record.direction ? (SupplierCallDirectionKv[record.direction] ?? record.direction) : null;
      },
    }, */
    {
      title: "Notes",
      dataIndex: ["note"],
      width: 280,
      render: (__, record) => {
        if (record.uid?.includes("call_")) {
          return record?.note ? (
            <Row wrap={false}>
              <Col flex={"auto"}>
                <Popover
                  title={`${SupplierCallTypeKv[record.type || 0]}${record.direction ? ` / ${SupplierCallDirectionKv[record.direction]}` : ""}`}
                  content={<div dangerouslySetInnerHTML={{ __html: nl2br(record.note) }} style={{ maxHeight: 600, overflowY: "auto" }}></div>}
                  trigger={["hover"]}
                  styles={{ root: { maxWidth: 1200 } }}
                >
                  <Typography.Paragraph
                    onClick={() => {
                      setCurrentRow(record);
                      setNotesModalVisible(true);
                    }}
                    className="cursor-pointer"
                    ellipsis={{
                      rows: 2,
                      expanded: false,
                    }}
                    style={{ marginBottom: 0 }}
                  >
                    {sEllipsed(Util.stripTags(record.note), 200)}
                  </Typography.Paragraph>
                </Popover>
              </Col>
            </Row>
          ) : null;
        } else {
          if (record.customers?.length) {
            return (
              <div>
                {record.customers?.map((x) => (
                  <Tag
                    key={x.id}
                    title={
                      x.email_kv
                        ? Object.keys(x.email_kv)
                            .map((e) => `${e}: Email #${x.email_kv?.[`${e}`]}`)
                            .join(",")
                        : ""
                    }
                    className="cursor-pointer"
                    onClick={() => {
                      if (x.email_kv) {
                        const firstEmailAddr = Object.keys(x.email_kv)?.[0];
                        setEmailInfo({ emailId: x.email_kv[firstEmailAddr], emailAddr: firstEmailAddr });
                        setShowDetail(true);
                      }
                    }}
                  >
                    {sEllipsed(x.name, 20)}
                  </Tag>
                ))}
              </div>
            );
          } else {
            return null;
          }
        }
      },
    },
    /* {
      title: "Subject / Comment",
      dataIndex: ["email_subject"],
      width: 300,
      ellipsis: true,
      render(__, entity) {
        return entity.uid?.includes("call_") ? null : entity.email_subject;
      },
    }, */
    {
      valueType: "option",
      fixed: "right",
      width: 40,
      className: "p-0",
      render(__, entity) {
        return (
          <Space.Compact>
            {entity.uid?.startsWith("call_") && (
              <Button
                type="link"
                color="primary"
                icon={<EditOutlined color="primary" />}
                style={{ width: 20 }}
                onClick={() => {
                  setCurrentRow(entity);
                  handleUpdateModalVisible(true);
                }}
              />
            )}
            {entity.uid?.startsWith("call_") && (
              <Popconfirm
                className="cursor-pointer c-red"
                title={<div>Are you sure you want to delete?</div>}
                okText="Yes"
                cancelText="No"
                styles={{ root: { width: 300 } }}
                onConfirm={async () => {
                  const hide = message.loading("Deleting a selected log...", 0);
                  deleteCustomerCall(entity.id)
                    .then(() => {
                      message.success("Deleted successfully.");
                      actionRef.current?.reload();
                    })
                    .catch(Util.error)
                    .finally(() => hide());
                }}
              >
                <div>
                  <Button type="link" danger icon={<DeleteOutlined color="danger" />} style={{ width: 20 }} />
                </div>
              </Popconfirm>
            )}
          </Space.Compact>
        );
      },
    },
  ];

  /* const { userOptions } = useUserOptions();
  const userId = supplierExt?.user?.user_id;
  const initials = supplierExt?.user?.initials ?? supplierExt?.user?.username ?? "Select"; */

  useEffect(() => {
    actionRef.current?.reload();
  }, [inc_internal_email]);

  useEffect(() => {
    if (offer_no) {
      actionRef.current?.reload();
    }
  }, [offer_no]);

  return (
    <>
      <ProTable<RowType, API.PageParams>
        headerTitle={
          <Space size={8} direction="vertical" style={{ width: 350 }}>
            <div>Customer:</div>
            <ProForm<SearchFormValueType>
              layout="inline"
              formRef={searchFormRef}
              isKeyPressSubmit
              submitter={{
                render(props, dom) {
                  return <div style={{ marginRight: "auto" }}>{dom}</div>;
                },
                searchConfig: { submitText: <SearchOutlined /> },
                submitButtonProps: { htmlType: "submit", type: "default" },
                resetButtonProps: { style: { display: "none" } },
                onSubmit: () => actionRef.current?.reload(),
              }}
            >
              {/* <ProFormSelect
                name="customer_id"
                width="xs"
                showSearch
                request={async (params) => {
                  return getCustomerListByPage({ ...params, pageSize: 200 }).then((res) => {
                    return res.data.map((x) => ({ ...x, value: x.id, label: x.name }));
                  });
                }}
                fieldProps={{ popupMatchSelectWidth: false }}
              /> */}
              <ProFormText name="like_customer_name" placeholder="Search customer..." width={140} />

              <ProFormText name="ft_note" placeholder="Search notes..." width={140} />
            </ProForm>
            {/* <div style={{ fontWeight: "normal" }}>
              <Checkbox
                id="inc_internal_email"
                title="Include Internal Email?"
                defaultChecked={false}
                checked={inc_internal_email}
                onChange={(e) => set_inc_internal_email(e.target.checked)}
              />
            </div> */}
            {/* <div style={{ fontWeight: "normal" }}>
              <label htmlFor="offer_mode" style={{ marginRight: 8 }}>
                Offer
              </label>
              <Radio.Group
                id="offer_mode"
                onChange={(e) => set_offer_mode(e.target.value)}
                value={offer_mode}
                options={[
                  { value: "inc", label: "Inc" },
                  { value: "exc", label: "Exc" },
                  { value: "only", label: "Only" },
                ]}
              />
            </div> */}
          </Space>
        }
        toolBarRender={() => [
          <Space key="action-buttons" size={48} style={{ marginRight: 8 }}>
            <Button
              key="new"
              type={"primary"}
              onClick={() => {
                handleCreateModalVisible(true);
              }}
            >
              New
            </Button>
          </Space>,
        ]}
        actionRef={actionRef}
        rowKey="uid"
        size="small"
        revalidateOnFocus={false}
        showHeader={false}
        options={{ fullScreen: false, density: false, setting: false, reload: false }}
        search={false}
        sticky
        scroll={{ x: 420 }}
        pagination={{
          defaultPageSize: sn(Util.getSfValues("cu_sf_customer_call_list_p")?.pageSize ?? 20),
        }}
        request={async (params, sort, filter) => {
          if (!offer_no) {
            return [];
          }

          const searchFormValues = {};
          Util.setSfValues("cu_sf_customer_call_list", searchFormValues);
          Util.setSfValues("cu_sf_customer_call_list_p", params);

          setLoading(true);
          return getCustomerCallListByPage(
            {
              ...params,
              with: "mergeEmail",
              inc_internal_email,
              offer_no: offer_no,
              ...searchFormRef.current?.getFieldsValue(),
            },
            sort,
            filter,
          )
            .then((res) => {
              if (currentRow?.id) {
                setCurrentRow(res.data.find((x) => x.id == currentRow.id));
              }
              setTotalRecordsCount(res.total || 0);
              return res;
            })
            .finally(() => setLoading(false));
        }}
        onRequestError={Util.error}
        columns={columns}
        columnEmptyText=""
        locale={{ emptyText: <></> }}
        cardProps={{ bodyStyle: { padding: 0 }, headStyle: { padding: 0 } }}
        tableStyle={{ display: !totalRecordsCount ? "none" : "" }}
      />

      <CustomerCallCreateForm
        initialValues={{
          type: SupplierCallType.Phone,
          direction: SupplierCallDirection.Out,
          offer_no: offer_no,
        }}
        modalVisible={createModalVisible}
        handleModalVisible={handleCreateModalVisible}
        onSubmit={(value) => {
          actionRef.current?.reload();
        }}
      />

      <CustomerCallUpdateForm
        initialValues={{ ...currentRow, offer_no: offer_no }}
        modalVisible={updateModalVisible}
        handleModalVisible={handleUpdateModalVisible}
        onSubmit={(value) => {
          actionRef.current?.reload();
        }}
      />

      <SNotesViewerModal
        id="customer-call-notes-viewer"
        title={`${SupplierCallTypeKv[currentRow?.type || 0]}${currentRow?.direction ? ` / ${SupplierCallDirectionKv[currentRow?.direction]}` : ""} Notes`}
        content={currentRow?.note}
        modalVisible={notesModalVisible}
        handleModalVisible={setNotesModalVisible}
      />

      {emailInfo?.emailId ? (
        <CustomerOfferEmailViewModal
          emailId={emailInfo?.emailId}
          emailAddr={emailInfo.emailAddr}
          modalVisible={showDetail}
          handleModalVisible={setShowDetail}
        />
      ) : null}
    </>
  );
};

export default CustomerCallListByOffer;

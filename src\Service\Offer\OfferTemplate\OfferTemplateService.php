<?php

declare(strict_types=1);

namespace App\Service\Offer\OfferTemplate;

use App\Models\Offer\OfferTemplate;

final class OfferTemplateService extends Base
{
    public function create(array $input): OfferTemplate
    {
        return OfferTemplate::create($input);
    }

    public function update($id, $input): OfferTemplate
    {
        $row = OfferTemplate::findOrFail($id);
        $row->update($input);
        return $row;
    }

    public function getOne(int $id, array $params = []): OfferTemplate
    {
        return $this->offerTemplateRepository->getQueryBuilder()->where('id', $id)->first();
    }

    public function getOfferTemplatesByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($perPage < 1) {
            $perPage = self::DEFAULT_PER_PAGE_PAGINATION;
        }

        return $this->offerTemplateRepository->getOfferTemplatesByPage(
            $page,
            $perPage,
            $params
        );
    }

    public function delete($ids): void
    {
        $arr = is_array($ids) ? $ids : explode(',', $ids);
        $this->offerTemplateRepository->getQueryBuilder()->whereIn('id', $arr)->delete();
    }
}

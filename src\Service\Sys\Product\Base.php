<?php

declare(strict_types=1);

namespace App\Service\Sys\Product;

use App\Service\BaseService;
use App\Repository\Sys\ProductRepository;
use Slim\Container;

abstract class Base extends BaseService
{
    protected ProductRepository $productRepository;

    public function __construct(Container $container)
    {
        $this->productRepository = $container->get(ProductRepository::class);
    }

    public function getProductRepository()
    {
        return $this->productRepository;
    }
}


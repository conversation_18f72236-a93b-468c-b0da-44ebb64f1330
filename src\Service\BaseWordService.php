<?php

namespace App\Service;

use App\Lib\ExcelExportLib;
use App\Lib\FileLib;
use App\Lib\Func;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Query\Builder;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Exception;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Worksheet\PageSetup;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Writer\Xls;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class BaseWordService
{
    const CREATOR = 'FsOne';
    const KEYWORD = 'Foodstore One office 2007 openxml php';

    // XLS Data types
    const DATA_TYPE_STRING = ExcelExportLib::DATA_TYPE_STRING;
    const DATA_TYPE_NUM = ExcelExportLib::DATA_TYPE_NUM;
    const DATA_TYPE_INT = ExcelExportLib::DATA_TYPE_INT;
    const DATA_TYPE_DATE = ExcelExportLib::DATA_TYPE_DATE;
    const DATA_TYPE_DATETIME = ExcelExportLib::DATA_TYPE_DATETIME;
    const DATA_TYPE_YN = 'yn';
    const DATA_TYPE_STRING_DIGIT = 'string_digit';
    const DATA_TYPE_DIGIT = '0';

    protected Spreadsheet $spreadsheet;
    protected $isLandscape = false;

    protected $basePath = '/tmp';   // /{basePath}/{relPath}/{file_name}.{extension}
    protected $relPath = '/tmp';    //
    protected $extension = 'docx';

    // In case export, we should set linear array
    // In case import, we should set key-value array
    protected array $dbFieldToXlsCol = [];

    protected ?string $docTitle = null;
    protected ?string $fileName = null;
    protected bool $isFileNameTimestamp = true;

    public function setDocTitle($title): void
    {
        $this->docTitle = $title;
    }

    public function setFileName($title): void
    {
        $this->fileName = $title;
    }

    public function setIsFileNameTimestamp($isFileNameTimestamp): void
    {
        $this->isFileNameTimestamp = $isFileNameTimestamp;
    }


    /**
     * @param array $dbFieldToXlsCol
     */
    public function setDbFieldToXlsCol(array $dbFieldToXlsCol): void
    {
        $this->dbFieldToXlsCol = $dbFieldToXlsCol;
    }

    /**
     * For export.
     * Public set settings to export excel.
     *
     * @param $docTitle
     * @param array $options
     * @return void
     */
    public function setOptions($docTitle, array $options = []): void
    {
        if ($docTitle) {
            $this->docTitle = $docTitle;
        }

        $this->relPath = $options['relPath'] ?? '/tmp';
        $this->basePath = $options['basePath'] ?? PRIVATE_DATA_PATH;
    }

    /**
     * Create a spreadsheet object with this title.
     * Either use an existing Excel template file or create a new one.
     *
     * @param $docTitle
     * @param array $options [ArrayShape(['creator' => "", 'keyword' => ""])]
     * @return Spreadsheet
     *
     * @throws Exception
     */
    public function createWordDocument($docTitle, array $options = []): Spreadsheet
    {
        $mergedOptions = array_merge($options, [
            'creator' => self::CREATOR,
            'keyword' => self::KEYWORD,
        ]);
        $this->spreadsheet = ExcelExportLib::createSpreadsheet($docTitle, $mergedOptions);

        return $this->spreadsheet;
    }
}
<?php

namespace App\Models\Offer;

use App\Models\BaseModel;
use App\Models\File;

/**
 * @property integer $offer_template_id
 * @property integer $file_id
 * @property File $file
 * @property OfferTemplateLang $offerTemplate
 */
class OfferTemplateFile extends BaseModel
{
    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'offer_template_file';

    /**
     * The primary key for the model.
     * 
     * @var string
     */
    protected $primaryKey = 'offer_template_id';

    protected function setKeysForSaveQuery($query): \Illuminate\Database\Eloquent\Builder
    {
        $query
            ->where('offer_template_id', '=', $this->getAttribute('offer_template_id'))
            ->where('file_id', '=', $this->getAttribute('file_id'));

        return $query;
    }


    /**
     * Indicates if the IDs are auto-incrementing.
     * 
     * @var bool
     */
    public $incrementing = false;

    /**
     * @var array
     */
    protected $fillable = ['offer_template_id', 'file_id'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function file()
    {
        return $this->belongsTo('App\Models\Offer\File');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function offerTemplate()
    {
        return $this->belongsTo('App\Models\Offer\OfferTemplate');
    }
}

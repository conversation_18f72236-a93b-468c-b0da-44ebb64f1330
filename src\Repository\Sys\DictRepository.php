<?php

declare(strict_types=1);

namespace App\Repository\Sys;

use App\Models\Sys\Dict;
use App\Repository\BaseRepositoryORM;
use Illuminate\Database\Eloquent\Builder;


final class DictRepository extends BaseRepositoryORM
{
    private function getQueryDictsByPage($params = []): Builder
    {
        $qb = $this->getQueryBuilder();

        if (!empty($params)) {
            $qb = $this->applyOrderBy($qb, $params);

            if ($params['type'] ?? null)
                $qb->where('type', $params['type']);

            if ($params['label'] ?? null)
                $qb->where('label', 'LIKE', "%{$params['label']}%");

            if ($params['code'] ?? null)
                $qb->where('code', $params['code']);

        }

        $qb->orderBy('type')->orderBy('label');

        return $qb;
    }

    /**
     * Get lists by pagination.
     *
     * @param int $page
     * @param int $perPage
     * @param array|null $params
     * @return array
     */
    public function getDictsByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        $query = $this->getQueryDictsByPage($params);
        $total = $this->getCountByQuery($query);

        $result = $this->getResultsWithPagination(
            $query,
            $page,
            $perPage,
            $total
        );

        return $result;
    }

    public function getQueryBuilder(): Builder
    {
        return Dict::query();
    }
}
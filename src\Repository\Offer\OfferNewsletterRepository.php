<?php

declare(strict_types=1);

namespace App\Repository\Offer;

use App\Lib\Func;
use App\Models\Offer\OfferNewsletter;
use App\Repository\BaseRepositoryORM;
use Illuminate\Database\Eloquent\Builder;

final class OfferNewsletterRepository extends BaseRepositoryORM
{
    public function getQueryOfferNewslettersByPage($params = []): Builder
    {
        $qb = $this->getQueryBuilder();

        if (!empty($params)) {
            if ($params['offer_no'] ?? null) {
                $qb->where('offer_no', $params['offer_no']);
            }

            if (Func::keyExistsInWithParam($params, 'files')){
                $qb->with('files');
            }

            $qb = $this->applyOrderBy($qb, $params);
        }

        return $qb;
    }

    /**
     * Get lists by pagination.
     *
     * @param int $page
     * @param int $perPage
     * @param array|null $params
     * @return array
     */
    public function getOfferNewslettersByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        $query = $this->getQueryOfferNewslettersByPage($params);
        $total = $this->getCountByQuery($query);

        $result = $this->getResultsWithPagination(
            $query,
            $page,
            $perPage,
            $total
        );

        return $result;
    }

    public function getQueryBuilder(): Builder
    {
        return OfferNewsletter::query();
    }
}
<?php

declare(strict_types=1);

namespace App\Service\Offer\OfferTemplateLang;

use App\Service\BaseService;
use App\Repository\Offer\OfferTemplateLangRepository;
use Slim\Container;

abstract class Base extends BaseService
{
    private const REDIS_KEY = 'Offer:%s';
    protected OfferTemplateLangRepository $offerTemplateLangRepository;

    public function __construct(Container $container)
    {
        $this->offerTemplateLangRepository = $container->get(OfferTemplateLangRepository::class);
    }

    public function getOfferTemplateLangRepository()
    {
        return $this->offerTemplateLangRepository;
    }
}


<?php

declare(strict_types=1);

namespace App\Controller\Supplier\Supplier;

use App\Controller\BaseController;
use App\Service\Supplier\Supplier\SupplierService;
use Respect\Validation\Validator as v;
use Slim\Container;

abstract class Base extends BaseController
{
    public SupplierService $supplierService;

    public function __construct(Container $container)
    {
        parent::__construct($container);
        $this->supplierService = $container->get(SupplierService::class);
    }

	public function validate(array $input): bool {
        $validator = v::key('name', v::stringType()->length(1, 255));

        if (!$validator->validate($input)) {
            \App\Exception\Base::raiseInvalidRequest();
        }
        return true;
    }
}

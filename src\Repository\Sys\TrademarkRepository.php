<?php

declare(strict_types=1);

namespace App\Repository\Sys;

use App\Lib\FuncModel;
use App\Models\Sys\Trademark;
use App\Repository\BaseRepositoryORM;
use Illuminate\Database\Eloquent\Builder;

/**
 * @deprecated
 */
final class TrademarkRepository extends BaseRepositoryORM
{
    public function getQueryTrademarksByPage($params = []): Builder
    {
        $qb = $this->getQueryBuilder();

        if (!empty($params)) {
            if ($params['name'] ?? null){
                $qb->where('name', 'like', FuncModel::likeValue($params['name']));
            }
            if ($params['keyWords'] ?? null){
                $qb->where('name', 'like', FuncModel::likeValue($params['keyWords']));
            }

            $qb = $this->applyOrderBy($qb, $params);
        }

        return $qb;
    }

    /**
     * Get lists by pagination.
     *
     * @param int $page
     * @param int $perPage
     * @param array|null $params
     * @return array
     */
    public function getTrademarksByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        $query = $this->getQueryTrademarksByPage($params);
        $total = $this->getCountByQuery($query);

        $result = $this->getResultsWithPagination(
            $query,
            $page,
            $perPage,
            $total
        );

        return $result;
    }

    public function getQueryBuilder(): Builder
    {
        return Trademark::query();
    }
}
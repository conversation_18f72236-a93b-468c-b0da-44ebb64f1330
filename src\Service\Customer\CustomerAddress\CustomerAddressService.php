<?php

declare(strict_types=1);

namespace App\Service\Customer\CustomerAddress;

use App\Models\Customer\CustomerAddress;


final class CustomerAddressService extends Base
{
    public function create(array $input): CustomerAddress
    {
        return CustomerAddress::create($input);
    }

    public function update($id, $input): CustomerAddress
    {
        $row = CustomerAddress::findOrFail($id);
        $row->update($input);
        return $row;
    }

    public function getOne(int $id, array $params=[]): CustomerAddress
    {
        return $this->customerAddressRepository->getQueryBuilder()->where('id', $id)->first();
    }

    public function getCustomerAddressesByPage(
        int $page,
        int $perPage,
        ?array $params
    ): array
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($perPage < 1) {
            $perPage = self::DEFAULT_PER_PAGE_PAGINATION;
        }

        return $this->customerAddressRepository->getCustomerAddressesByPage(
            $page,
            $perPage,
            $params
        );
    }

    public function delete($ids): void
    {
        $arr = is_array($ids) ? $ids : explode(',', $ids);
        $this->customerAddressRepository->getQueryBuilder()->whereIn('id', $arr)->delete();
    }
}

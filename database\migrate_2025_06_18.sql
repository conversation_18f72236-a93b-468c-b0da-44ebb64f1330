drop table if exists customer_call;

CREATE TABLE `customer_call`
(
    `id`          int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'PK',
    `customer_id` int(10) unsigned   DEFAULT NULL COMMENT 'Customer''s ID.',
    `type`        smallint(6)        DEFAULT 1 COMMENT 'Call type: 1: Phone, 2: Notes, 3: Email, 4: OfferRequest, 5: Meeting',
    `direction`   enum ('I','O','W') DEFAULT 'O' COMMENT 'O: outgoing from WHC, I: incoming from customer, W: between staff',
    `offer_no`    varchar(15)        DEFAULT NULL COMMENT 'Offer No',
    `note`        longtext           DEFAULT NULL COMMENT 'Call note',
    `ref_type`    varchar(31)        DEFAULT NULL COMMENT 'Ref Type. E.g. Email',
    `ref_id`      varchar(31)        DEFAULT NULL COMMENT 'Ref ID in Ref Type. E.g. 255 in Email',
    `created_on`  datetime           DEFAULT NULL,
    `created_by`  int(11)            DEFAULT NULL COMMENT 'Creator',
    `updated_on`  datetime           DEFAULT NULL,
    `updated_by`  int(11)            DEFAULT NULL COMMENT 'Updater',
    PRIMARY KEY (`id`),
    KEY `FK_customer_call_customer_id` (`customer_id`),
    KEY `IDX_customer_call_updated_on` (`updated_on`),
    KEY `IDX_customer_call_offer_no` (`offer_no`),
    KEY `IDX_customer_call_ref_type` (`ref_type`),
    KEY `IDX_customer_call_ref_id` (`ref_id`),
    CONSTRAINT `FK_customer_call_customer_id` FOREIGN KEY (`customer_id`) REFERENCES `customer` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;


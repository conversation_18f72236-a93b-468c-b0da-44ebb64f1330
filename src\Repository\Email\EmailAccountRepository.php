<?php

declare(strict_types=1);

namespace App\Repository\Email;

use App\Lib\Func;
use App\Models\Email\EmailAccount;
use App\Repository\BaseRepositoryORM;
use Illuminate\Database\Eloquent\Builder;

final class EmailAccountRepository extends BaseRepositoryORM
{
    public function getQueryEmailAccountsByPage($params = []): Builder
    {
        $qb = $this->getQueryBuilder();

        if (!empty($params)) {
            if (key_exists('status', $params)) {
                $qb->where('status', (int)$params['status']);
            }

            if (key_exists('emailServer.is_oauth', $params)) {
                $qb->whereHas('emailServer', function ($builder) use (&$params) {
                    $builder->where('is_oauth', (int)$params['emailServer.is_oauth']);
                });
            }

            if (Func::keyExistsInWithParam($params, 'emailServer')) {
                $qb->with('emailServer');
            }

            $qb = $this->applyOrderBy($qb, $params);
        }

        return $qb;
    }

    public function getValidAccounts($params = []): array
    {
        $params['status'] = 1;
        $params['with'] = 'emailServer';
        $query = $this->getQueryEmailAccountsByPage($params);

        /** @var EmailAccount[] $accounts */
        $accounts = $query->select('email_account.*')
            ->addSelect('password')
            // bug
            //->withMax('emails', 'date_str')
            ->withMax('emails', 'date')
            ->withMax('emails', 'mail_id')
            ->get();

        /** @var EmailAccount[] $filtered */
        $filtered = [];
        foreach ($accounts as $account) {
            if (!$account->emailServer) {
                continue;
            }
            if (!$account->emailServer->imap_host) {
                continue;
            }

            if ($account->emailServer->imap_ssl && !$account->emailServer->imap_port_ssl) {
                continue;
            }
            if (!$account->emailServer->imap_ssl && !$account->emailServer->imap_port) {
                continue;
            }

            $filtered[] = $account;
        }

        return $filtered;
    }

    /**
     * Get lists by pagination.
     *
     * @param int $page
     * @param int $perPage
     * @param array|null $params
     * @return array
     */
    public function getEmailAccountsByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        $query = $this->getQueryEmailAccountsByPage($params);
        $total = $this->getCountByQuery($query);

        $result = $this->getResultsWithPagination(
            $query,
            $page,
            $perPage,
            $total
        );

        return $result;
    }

    public function getQueryBuilder(): Builder
    {
        return EmailAccount::query();
    }
}
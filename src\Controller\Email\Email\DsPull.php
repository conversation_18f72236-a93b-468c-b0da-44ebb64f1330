<?php

declare(strict_types=1);

namespace App\Controller\Email\Email;

use Slim\Http\Request;
use Slim\Http\Response;

final class DsPull extends Base
{
    public function __invoke(Request $request, Response $response): Response
    {
        $input = (array)$request->getParsedBody();

        $this->emailService->popEmails($input);

        return $this->jsonResponse($response, 'success', true, 201);
    }
}

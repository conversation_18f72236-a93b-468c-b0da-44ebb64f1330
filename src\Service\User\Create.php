<?php

declare(strict_types=1);

namespace App\Service\User;

use App\Entity\User;

final class Create extends Base
{
    public function create(array $input): array
    {
        /** @var User $user */
        $user = $this->userRepository->create($input);
        if (self::isRedisEnabled() === true) {
            $this->saveInCache((int) $user['user_id'], $user);
        }

        return $user;
    }
}

<?php

namespace App\Models\Offer;

use App\Models\BaseModel;

/**
 * @property integer $id
 * @property integer $offer_template_id
 * @property string $lang
 * @property string $subject
 * @property string $header
 * @property string $body
 * @property string $footer

 * @property string $created_on
 * @property string $updated_on
 * @property integer $created_by
 * @property integer $updated_by
 *
 * @property OfferTemplate $offerTemplate
 */
class OfferTemplateLang extends BaseModel
{
    public $timestamps = true;

    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'offer_template_lang';

    /**
     * @var array
     */
    protected $fillable = ['offer_template_id', 'lang', 'subject', 'header', 'body', 'footer', 'created_on', 'updated_on', 'created_by', 'updated_by'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function offerTemplate()
    {
        return $this->belongsTo('App\Models\Offer\OfferTemplate');
    }
}

<?php

declare(strict_types=1);

namespace App\Service\Offer\OfferNewsletter;

use App\Exception\BaseException;
use App\Lib\Func;
use App\Lib\FuncDate;
use App\Models\File;
use App\Models\Offer\OfferExt;
use App\Models\Offer\OfferNewsletter;
use App\Service\BaseService;
use App\Service\CustApi\CustApiService;
use App\Service\File\FileService;
use App\Service\Offer\OfferExt\OfferExtService;
use Intervention\Image\ImageManager;
use Psr\Http\Message\UploadedFileInterface;

final class OfferNewsletterService extends Base
{
    public function create(array $input): OfferNewsletter
    {
        return OfferNewsletter::create($input);
    }

    public function update($id, $input): OfferNewsletter
    {
        $row = OfferNewsletter::findOrFail($id);
        $row->update($input);
        return $row;
    }

    /**
     * @param array $input
     * @param UploadedFileInterface[] $files
     * @return OfferNewsletter
     */
    public function updateOrCreate(array $input, $files = []): OfferNewsletter
    {
        $id = $input['id'] ?? null;
        $offer_no = $input['offer_no'] ?? null;
        /** @var OfferNewsletter $newsletter */
        if ($id)
            $newsletter = OfferNewsletter::query()->find($id);
        else $newsletter = new OfferNewsletter();

        $newsletter->fill($input);

        if (!($input['case_qty'] ?? null)) $newsletter->case_qty = null;
        if (!($input['ve_pallet'] ?? null)) $newsletter->ve_pallet = null;
        if (!($input['price'] ?? null)) $newsletter->price = null;
        if (!($input['product_title'] ?? null)) $newsletter->product_title = null;

        $newsletter->save();

        // files
        if ($files) {
            $pathRel = DS . File::CAT_OFFER_NEWSLETTER_FILE . DS . $offer_no;

            $dbFiles = [];
            foreach ($files as &$file) {
                $dbFiles[] = FileService::uploadFile(File::CAT_OFFER_NEWSLETTER_FILE, $file, $pathRel);
            }
            $newsletter->files()->saveMany($dbFiles);
        }

        return $newsletter;
    }

    /**
     * Up Sync newsletters into WHC_Cust
     *
     * @param $offer_no
     * @return array|bool|mixed|void|null
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \Psr\Container\ContainerExceptionInterface
     */
    public function usToWhcCust($offer_no)
    {
        $qb = $this->offerNewsletterRepository->getQueryOfferNewslettersByPage(['offer_no' => $offer_no, 'with' => 'files']);
        /** @var OfferNewsletter[] $newsletters */
        $newsletters = $qb->get();

        if ($newsletters) {
            /** @var CustApiService $custApiService */
            $custApiService = Func::getContainer()->get(CustApiService::class);
            $client = $custApiService->getApiClient();

            $multipartList = [
                [
                    'name' => "offer_no",
                    'contents' => $offer_no,
                ]
            ];
            foreach ($newsletters as $ind => $newsletter) {
                $multipart = [
                    'name' => "rows[$ind][offer_no]",
                    'contents' => $newsletter->offer_no,
                ];
                $multipartList[] = $multipart;

                if ($newsletter->product_title) {
                    $multipart = [
                        'name' => "rows[$ind][product_title]",
                        'contents' => $newsletter->product_title,
                    ];
                    $multipartList[] = $multipart;
                }

                if ($newsletter->case_qty) {
                    $multipart = [
                        'name' => "rows[$ind][case_qty]",
                        'contents' => $newsletter->case_qty,
                    ];
                    $multipartList[] = $multipart;
                }
                if ($newsletter->ve_pallet) {
                    $multipart = [
                        'name' => "rows[$ind][ve_pallet]",
                        'contents' => $newsletter->ve_pallet,
                    ];
                    $multipartList[] = $multipart;
                }
                if ($newsletter->price) {
                    $multipart = [
                        'name' => "rows[$ind][price]",
                        'contents' => $newsletter->price,
                    ];
                    $multipartList[] = $multipart;
                }

                foreach ($newsletter->files as $fileInd => $file) {
                    $multipart = [
                        'name' => "rows[$ind][files][$fileInd]",
                        'contents' => fopen($file->abs_path, 'r'),
                        'filename' => $file->name,
                    ];

                    $multipartList[] = $multipart;
                }
            }

            $url = 'rest/offer/newsletter/updateOrCreate';
            $postData = [
                // 'body' => ['rows' => $rows],
            ];
            if ($multipartList) {
                $postData['multipart'] = $multipartList;
            }

            try {
                $res = $client->post($url, $postData);

                $isSuccess = $res->getStatusCode() >= 200 && $res->getStatusCode() <= 300;
                $resParsed = Func::safeJson($res->getBody()->__toString());
                if ($isSuccess) {
                    // Update timestamp
                    /** @var OfferExtService $extService */
                    $extService = Func::getContainer()->get(OfferExtService::class);
                    $extService->updateOrCreate([
                        'offer_no' => $offer_no,
                        'details' => [
                            'us_newsletter_dt' => FuncDate::dtNow()
                        ]
                    ]);
                }
                return $resParsed ? $resParsed['message'] ?? $isSuccess : $isSuccess;
            } catch (\Exception $exception) {
                Func::getLogger()->error($url);
                throw BaseException::raiseInternalServerError($exception->getMessage());
            }
        } else {
            BaseException::raiseInvalidRequest('Offer newsletter does not exist!');
        }
    }

    public function copyFileAndResize($offer_no, $file_id)
    {
        /** @var OfferNewsletter $newsletter */
        $newsletter = OfferNewsletter::query()->where('offer_no', $offer_no)->firstOrFail();
        /** @var File $orgFile */
        $orgFile = $newsletter->files()->where('id', $file_id)->firstOrFail();

        if (!str_starts_with($orgFile->type, 'image')) {
            BaseException::raiseInvalidRequest("File is not image!");
        }

        $pathInfo = pathinfo($orgFile->org_path);
        $newFileName = uniqid() . '.' . $pathInfo['extension'];
        $newAbsPath = File::getBasePath(File::CAT_OFFER_NEWSLETTER_FILE) . $pathInfo['dirname'] . DS . $newFileName;
        copy($orgFile->abs_path, $newAbsPath);

        // Resize image
        $orgFileFullPath = $newAbsPath;
        $im = new ImageManager(['driver' => 'gd',]);
        $orgImage = $im->make($orgFileFullPath);
        $orgImage->resize(80, 80, function ($constraint) {
            $constraint->aspectRatio();
            $constraint->upsize();
        });
        $orgImage->resizeCanvas(80, 80, 'center', false, '#ffffff');
        $orgImage->save($orgFileFullPath);
        $orgImage->destroy();

        $file = new File();
        $file->category = File::CAT_OFFER_NEWSLETTER_FILE;
        $file->file_name = $newFileName;
        $file->clean_file_name = $newFileName;
        $file->path = $file->org_path = $pathInfo['dirname'] . '/' . $newFileName;
        $file->type = $orgFile->type;
        clearstatcache();
        $file->size = @filesize($orgFileFullPath);

        $newsletter->files()->save($file);

        return $file;
    }

    public function getOne(int $id, array $params = []): OfferNewsletter
    {
        return $this->offerNewsletterRepository->getQueryBuilder()->where('id', $id)->first();
    }

    public function getOfferNewslettersByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($perPage < 1) {
            $perPage = self::DEFAULT_PER_PAGE_PAGINATION;
        }

        return $this->offerNewsletterRepository->getOfferNewslettersByPage(
            $page,
            $perPage,
            $params
        );
    }

    public function delete($ids, $excludedFileNames = []): void
    {
        $arr = is_array($ids) ? $ids : explode(',', $ids);

        /** @var OfferNewsletter[] $oldRows */
        $oldRows = OfferNewsletter::query()
            ->whereIn('id', $arr)
            ->with('files')
            ->get();

        /** @var FileService $fileService */
        $fileService = Func::getContainer()->get('file_service');
        foreach ($oldRows as &$x) {
            if ($x->files) {
                foreach ($x->files as $file) {
                    if (!in_array($file->file_name, $excludedFileNames)) {
                        $fileService->removeFile($file);
                    } else {
                        $file->delete();
                    }
                }
            }
        }

        OfferNewsletter::query()->whereIn('id', $arr)->delete();
    }
}

<?php

namespace App\Models;

use App\App\Constants;
use App\Lib\Func;
use App\Models\Offer\OfferBlog;
use App\Models\Offer\OfferNewsletter;

/**
 * @property integer $id
 * @property string $category
 * @property string $file_name
 * @property string $clean_file_name
 * @property string $path
 * @property string $org_path
 * @property integer $size
 * @property integer $hits
 * @property string $type
 * @property string $hash
 * @property string $created_at
 * @property string $updated_at
 *
 * @property mixed|string $url
 * @property int|mixed $uid
 * @property string $name
 * @property mixed|string $abs_path
 * @property mixed|string $thumb_url
 *
 *
 */
class File extends BaseModel
{
    public const CREATED_AT = 'created_at';
    public const UPDATED_AT = 'updated_at';

    public $timestamps = true;

    const CAT_PICKLIST_PDF = 'picklist pdf';
    const CAT_PICKLIST_CSV = 'picklist csv';
    const CAT_CALL = 'call_file';
    const CAT_OFFER_TEMPLATE_LANG_FILE = 'offer_template_lang_file';
    const CAT_OFFER_BLOG_FILE = 'offer_blog_file';
    const CAT_OFFER_NEWSLETTER_FILE = 'offer_newsletter_file';


    protected $hidden = ['hits', 'abs_path'];

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'file';

    /**
     * The "type" of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'integer';

    /**
     * @var array
     */
    protected $fillable = ['category', 'hits', 'file_name', 'clean_file_name', 'path', 'org_path', 'size', 'type', 'hash', 'created_at', 'updated_at'];

    public $appends = [
        'url', 'uid', 'thumb_url', 'name'
    ];

    public function getUidAttribute()
    {
        return $this->id;
    }

    public function getNameAttribute()
    {
        return $this->file_name;
    }

    public static function getBasePath($category)
    {
        $basePath = Func::getContainer()->get('settings')['upload_dir'];
        switch ($category) {
            case self::CAT_CALL:
            case self::CAT_OFFER_TEMPLATE_LANG_FILE:
            case self::CAT_OFFER_BLOG_FILE:
            case self::CAT_OFFER_NEWSLETTER_FILE:
                $basePath = PRIVATE_DATA_PATH;
                break;
        }

        return $basePath;
    }

    public function getAbsPathAttribute($fieldKey = null)
    {
        $container = Func::getContainer();

        $category = $this->getAttributeValue('category');
        $basePath = self::getBasePath($category);

        return Func::pathJoinUrl($basePath, $this->getAttributeValue($fieldKey ?? 'org_path'));
    }

    public function getUrlAttribute()
    {
        $container = Func::getContainer();
        $category = $this->getAttributeValue('category');

        switch ($category) {
            case self::CAT_CALL:
            case self::CAT_OFFER_TEMPLATE_LANG_FILE:
                $kv = 1;
                $url = 'file/download?id=' . Func::mcrypt('encrypt', $this->id, ...Constants::FILE_SEC_KEYS[$kv]) . "&kv=$kv";
                break;
            case self::CAT_OFFER_BLOG_FILE:
            case self::CAT_OFFER_NEWSLETTER_FILE:
                $kv = 1;
                $url = Func::getBaseUrlStable() . 'api/file/download?id=' . Func::mcrypt('encrypt', $this->id, ...Constants::FILE_SEC_KEYS[$kv]) . "&kv=$kv";
                break;
            default:
                $baseUrl = $container->get('settings')['mediaBaseUrl'];
                $url = $baseUrl . $this->org_path;
        }

        return $url;
    }

    public function getThumbUrlAttribute()
    {
        $container = Func::getContainer();
        $category = $this->getAttributeValue('category');
        switch ($category) {
            case self::CAT_CALL:
            case self::CAT_OFFER_TEMPLATE_LANG_FILE:
                $kv = 1;
                $url = 'file/download?id=' . Func::mcrypt('encrypt', $this->id, ...Constants::FILE_SEC_KEYS[$kv]) . "&kv=$kv&thumb=1";
                return $url;
            case self::CAT_OFFER_BLOG_FILE:
            case self::CAT_OFFER_NEWSLETTER_FILE:
                $kv = 1;
                $url = Func::getBaseUrlStable() .  'api/file/download?id=' . Func::mcrypt('encrypt', $this->id, ...Constants::FILE_SEC_KEYS[$kv]) . "&kv=$kv&thumb=1";
                return $url;
            default:
                return $this->org_path ? $container->get('settings')['mediaBaseUrl'] . ($this->path ?? $this->org_path) : null;
        }
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function offerNewsletters()
    {
        return $this->belongsToMany(OfferNewsletter::class, 'offer_newsletter_file');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function offerBlogs()
    {
        return $this->belongsToMany(OfferBlog::class, 'offer_blog_file');
    }
}

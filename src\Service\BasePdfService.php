<?php

namespace App\Service;

use App\Lib\Func;
use Mpdf\HTMLParserMode;
use Mpdf\Mpdf;
use Slim\Views\PhpRenderer;

class BasePdfService
{
    const CREATOR = 'FsOne';
    const KEYWORD = 'Foodstore One PDF';

    protected $isLandscape = false;

    protected $basePath = '/uploads';   // /{basePath}/{relPath}/{file_name}.{extension}
    protected $relPath = '/export';

    protected ?string $docTitle = null;
    protected ?string $fileName = null;

    protected bool $isFileNameTimestamp = true;

    protected Mpdf $mpdf;

    protected PhpRenderer $view;
    protected $templatePath;

    public function init(&$params)
    {
        $this->basePath = Func::getContainer()->get('settings')['upload_dir'];
        $this->view = Func::getContainer()->get('view');
    }

    public function createMpdfInstance($pdfOptions = []): Mpdf
    {
        if($this->isLandscape) {
            $pdfOptions['orientation'] = 'L';
        }

        $this->mpdf = new Mpdf($pdfOptions);
        $this->mpdf->keep_table_proportions = true;
        $this->mpdf->useFixedNormalLineHeight = false;
        $this->mpdf->useFixedTextBaseline = false;

        return $this->mpdf;
    }

    public function writeHtml(&$html) {
        $html = iconv('UTF-8', 'UTF-8//IGNORE', $html);

        $this->mpdf->WriteHTML($html, HTMLParserMode::HTML_BODY);
    }

}
<?php

namespace App\Models\Customer;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Builder;

/**
 * @property integer $id
 * @property integer $customer_id
 * @property string $department
 * @property string $email
 * @property integer $is_active
 * @property integer $is_default
 * @property string $firstname
 * @property string $lastname
 * @property string $middlename
 * @property string $prefix
 * @property string $suffix
 * @property string $telephone
 * @property string $created_on
 * @property string $updated_on
 * @property string $note
 * @property string $lang
 * @property string $salutation
 * @property boolean $is_blocked
 * @property Customer $customer
 */
class CustomerContact extends BaseModel
{
    public $timestamps = true;

    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'customer_contact';

    /**
     * @var array
     */
    protected $fillable = ['customer_id', 'department', 'email', 'is_active', 'is_default', 'firstname', 'lastname', 'middlename', 'prefix', 'suffix', 'telephone', 'created_on', 'updated_on', 'note', 'lang', 'salutation', 'is_blocked'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function customer()
    {
        return $this->belongsTo('App\Models\Customer\Customer');
    }

    protected $appends = [
        'fullname',
    ];

    public function getFullnameAttribute()
    {
        $firstName = $this->getAttribute('firstname') ?? '';
        $lastname = $this->getAttribute('lastname') ?? '';

        return $firstName . ' ' . $lastname;
    }

    public function scopeSortDefault(Builder $query)
    {
        $query->orderByDesc('is_default')->orderByDesc('is_active');
    }

    public function scopeActive(Builder $query)
    {
        $query->where('is_active', 1);
    }
}

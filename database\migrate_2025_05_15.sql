# ----------------------------
# Converting supplier_id to integer type
# ----------------------------

DELIMITER $$
DROP FUNCTION IF EXISTS `get_pk`$$
DELIMITER ;


truncate table email;

ALTER TABLE `email`
    CHANGE `supplier_id` `supplier_id` INT UNSIGNED NULL COMMENT 'Supplier ID.';

truncate table supplier_call;

ALTER TABLE `supplier_call`
    CHANGE `supplier_id` `supplier_id` INT UNSIGNED NULL COMMENT 'Supplier ID.';

truncate table supplier_ext;

ALTER TABLE `supplier_ext`
    CHANGE `supplier_id` `supplier_id` INT UNSIGNED NULL COMMENT 'Supplier ID.';

truncate table supplier_meta;

ALTER TABLE `supplier_meta`
    CHANGE `supplier_id` `supplier_id` INT UNSIGNED NULL COMMENT 'Supplier ID.';


truncate table supplier_address;

ALTER TABLE `supplier_address`
    drop constraint SUPPLIER_ADDRESS_SUPPLIER_ID;

ALTER TABLE `supplier_address`
    CHANGE `supplier_id` `supplier_id` INT UNSIGNED NULL COMMENT 'Supplier ID.';


truncate table supplier_contact;

ALTER TABLE `supplier_contact`
    drop constraint FK_supplier_contact_supplier_id;

ALTER TABLE `supplier_contact`
    CHANGE `supplier_id` `supplier_id` INT UNSIGNED NULL COMMENT 'Supplier ID.';



truncate table `supplier`;

ALTER TABLE `supplier`
    CHANGE `id` `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'PK: Supplier ID';

ALTER TABLE `supplier_address`
    add constraint `SUPPLIER_ADDRESS_SUPPLIER_ID` FOREIGN KEY (`supplier_id`) REFERENCES `supplier` (`id`) ON DELETE CASCADE ON UPDATE SET NULL;

ALTER TABLE `supplier_contact`
    add constraint `FK_supplier_contact_supplier_id` FOREIGN KEY (`supplier_id`) REFERENCES `supplier` (`id`) ON DELETE CASCADE ON UPDATE SET NULL;



# ----------------------------
# DB design change by SK's request
# ----------------------------
alter table supplier_address
    drop column firstname,
    drop column lastname,
    drop column middlename,
    drop column prefix,
    drop column suffix;

ALTER TABLE `supplier_address`
    CHANGE `telephone` `telephone` VARCHAR(255) CHARSET utf8 COLLATE utf8_general_ci NULL COMMENT 'Phone Number';


ALTER TABLE `supplier_contact`
    ADD COLUMN `department` VARCHAR(255)       NULL COMMENT 'Job Title / Department' AFTER `supplier_id`,
    ADD COLUMN `is_default` SMALLINT DEFAULT 0 NULL COMMENT 'Is Default Contact?' AFTER `is_active`,
    CHANGE `telephone` `telephone` VARCHAR(255) CHARSET utf8 COLLATE utf8_general_ci NULL COMMENT 'Phone Number',
    ADD COLUMN `note`       TEXT               NULL COMMENT 'Note' AFTER `updated_on`;



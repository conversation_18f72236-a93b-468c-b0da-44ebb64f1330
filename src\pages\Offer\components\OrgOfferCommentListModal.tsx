import { getOrgOfferCommentAll } from "@/services/app/Supplier/supplier-call";
import Util from "@/util";
import { ActionType, ProColumns, ProTable } from "@ant-design/pro-components";
import { Modal } from "antd";
import { Dispatch, SetStateAction, useEffect, useRef } from "react";

type RowType = APIOrg.OfferComment;

export type OrgOfferCommentListModalProps = {
  offer_id: string;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
};

const OrgOfferCommentListModal: React.FC<OrgOfferCommentListModalProps> = (props) => {
  const { offer_id, modalVisible, handleModalVisible } = props;

  const actionRef = useRef<ActionType>();

  const columns: ProColumns<RowType>[] = [
    {
      title: "Date",
      dataIndex: "created_on",
      width: 120,
      sorter: true,
      defaultSortOrder: "descend",
      render(__, entity) {
        return Util.dtToDMYHHMM(entity.created_on);
      },
    },
    {
      title: "Status",
      dataIndex: "status",
      width: 120,
      render(__, entity) {
        return entity.sys_config?.name;
      },
    },
    {
      title: "Comments",
      dataIndex: "comment",
    },
  ];

  useEffect(() => {
    if (modalVisible && offer_id) {
      actionRef.current?.reload();
    }
  }, [modalVisible, offer_id]);

  return (
    <Modal title={`Offer Comments History`} width="1000px" open={modalVisible} onCancel={() => handleModalVisible(false)} footer={false}>
      <ProTable<RowType, API.PageParams>
        headerTitle={false}
        toolBarRender={() => []}
        actionRef={actionRef}
        rowKey="uid"
        size="small"
        cardProps={{ bodyStyle: { padding: 0 } }}
        revalidateOnFocus={false}
        options={false}
        search={false}
        sticky
        pagination={{
          defaultPageSize: 20,
          hideOnSinglePage: true,
        }}
        request={async (params, sort, filter) => {
          return getOrgOfferCommentAll({ ...params, offer_id: offer_id }, sort, filter);
        }}
        onRequestError={Util.error}
        columns={columns}
        columnEmptyText=""
      />
    </Modal>
  );
};
export default OrgOfferCommentListModal;

<?php

declare(strict_types=1);

namespace App\Repository\Sys;

use App\Models\Sys\CountryRegion;
use App\Repository\BaseRepositoryORM;
use Illuminate\Database\Eloquent\Builder;

final class CountryRegionRepository extends BaseRepositoryORM
{
    private function getQueryCountryRegionsByPage($params = []): Builder
    {
        $qb = $this->getQueryBuilder();

        if (!empty($params)) {
            if ($params['country_code'] ?? null) {
                $qb->where('country_code', $params['country_code']);
            }

            $qb = $this->applyOrderBy($qb, $params);
        }

        return $qb;
    }

    /**
     * Get lists by pagination.
     *
     * @param int $page
     * @param int $perPage
     * @param array|null $params
     * @return array
     */
    public function getCountryRegionsByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        $query = $this->getQueryCountryRegionsByPage($params);
        $total = $this->getCountByQuery($query);

        $result = $this->getResultsWithPagination(
            $query,
            $page,
            $perPage,
            $total
        );

        return $result;
    }

    public function getQueryBuilder(): Builder
    {
        return CountryRegion::query();
    }
}
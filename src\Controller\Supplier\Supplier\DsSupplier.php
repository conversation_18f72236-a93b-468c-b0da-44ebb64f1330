<?php

declare(strict_types=1);

namespace App\Controller\Supplier\Supplier;

use App\Exception\BaseException;
use Slim\Http\Request;
use Slim\Http\Response;

/**
 * @deprecated  Remove later
 */
final class DsSupplier extends Base
{
    public function __invoke(Request $request, Response $response, array $args): Response
    {
        BaseException::raiseInternalServerError("No function yet!");

        $this->supplierService->dsSuppliers();

        return $this->jsonResponse($response, 'success', true, 200);
    }
}

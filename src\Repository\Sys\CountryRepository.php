<?php

declare(strict_types=1);

namespace App\Repository\Sys;

use App\Models\Sys\Country;
use App\Repository\BaseRepositoryORM;
use Illuminate\Database\Eloquent\Builder;

final class CountryRepository extends BaseRepositoryORM
{
    private function getQueryCountrysByPage($params = []): Builder
    {
        $qb = $this->getQueryBuilder();

        if (!empty($params)) {
            $qb = $this->applyOrderBy($qb, $params);
        }

        return $qb;
    }

    /**
     * Get lists by pagination.
     *
     * @param int $page
     * @param int $perPage
     * @param array|null $params
     * @return array
     */
    public function getCountrysByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        $query = $this->getQueryCountrysByPage($params);
        $total = $this->getCountByQuery($query);

        $result = $this->getResultsWithPagination(
            $query,
            $page,
            $perPage,
            $total
        );

        return $result;
    }

    public function getQueryBuilder(): Builder
    {
        return Country::query();
    }
}
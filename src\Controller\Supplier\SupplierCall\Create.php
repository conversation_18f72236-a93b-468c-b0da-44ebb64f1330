<?php

declare(strict_types=1);

namespace App\Controller\Supplier\SupplierCall;

use Slim\Http\Request;
use Slim\Http\Response;

final class Create extends Base
{
    public function __invoke(Request $request, Response $response): Response
    {
        $input = (array)$request->getParsedBody();
        $input['uploaded_files'] = $this->validateUploadedFiles($request);

		$row = $this->supplierCallService->create($input);

        return $this->jsonResponse($response, 'success', $row, 201);
    }
}

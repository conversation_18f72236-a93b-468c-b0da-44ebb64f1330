<?php

declare(strict_types=1);

namespace App\Controller\Customer\CustomerCall;

use App\Controller\BaseController;
use App\Exception\File;
use App\Service\Customer\CustomerCall\CustomerCallService;
use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\UploadedFileInterface;
use Slim\Container;

abstract class Base extends BaseController
{
    public CustomerCallService $customerCallService;

    /**
     * @var UploadedFileInterface[]
     */
    public ?array $uploadedFiles = null;

    public function __construct(Container $container)
    {
        parent::__construct($container);
        $this->customerCallService = $container->get(CustomerCallService::class);
    }

    /**
     * @throws File
     */
    public function validateUploadedFiles(RequestInterface &$request, $key='files')
    {
        $tmp = $request->getUploadedFiles();
        $this->uploadedFiles = $tmp[$key] ?? null;
        if ($this->uploadedFiles) {
            foreach ($this->uploadedFiles as $x) {
                if ($x->getError() !== UPLOAD_ERR_OK) {
                    throw new \App\Exception\File('Upload Error! Code: ' . $x->getError(), 500);
                }
            }
        }

        return $this->uploadedFiles;
    }
}

<?php

declare(strict_types=1);

namespace App\Service;

use App\Exception\BaseException;
use App\Lib\Func;
use App\Service\MediaApi\MediaApiBaseService;

abstract class BaseService
{
    protected RedisService $redisService;
    public const DEFAULT_PER_PAGE_PAGINATION = 10;

    protected static function isRedisEnabled(): bool
    {
        return filter_var($_SERVER['REDIS_ENABLED'], FILTER_VALIDATE_BOOLEAN);
    }

    public static function extractInlineImage($htmlStr, $src='supplier')
    {
        if ($htmlStr && Func::hasInlineImage($htmlStr))
        {
            /** @var MediaApiBaseService $mediaApiService */
            $mediaApiService = Func::getContainer()->get(MediaApiBaseService::class);
            $output = $mediaApiService->extractInlineImage($htmlStr, $src);
            if (!$output) {
                BaseException::raiseInternalServerError("Html body processing was failed!");
            }

            return $output;
        }

        return $htmlStr;
    }

    public static function trimInputData(&$input, $keys=[])
    {
        foreach ($input as $key => &$data) {
            if (is_string($data)) {
                $data = trim($data);
            }
        }
    }
}

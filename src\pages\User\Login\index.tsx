import { LockOutlined, UserOutlined } from "@ant-design/icons";
import { Alert, message } from "antd";
import React, { useState } from "react";
import { ProFormText, LoginForm } from "@ant-design/pro-form";
import { useModel, IRoute } from "umi";
import Footer from "@/components/Footer";
import { login } from "@/services/app/login";
import styles from "./index.less";
import { LS_TOKEN_NAME } from "@/constants";
import { getQueryParamInUrl } from "@/util";

const LoginMessage: React.FC<{
  content: string;
}> = ({ content }) => (
  <Alert
    style={{
      marginBottom: 24,
    }}
    message={content}
    type="error"
    showIcon
  />
);

const Login: React.FC<IRoute> = () => {
  const [errorMsg, setErrorMsg] = useState("");
  const { initialState, setInitialState } = useModel("@@initialState");

  const fetchUserInfo = async () => {
    const userInfo = await initialState?.fetchUserInfo?.();

    if (userInfo) {
      await setInitialState((s: any) => ({ ...s, currentUser: userInfo }));
    }

    return userInfo;
  };

  const handleSubmit = async (values: API.LoginParams) => {
    try {
      const res = await login({ ...values, type: "account" });

      const defaultLoginSuccessMessage = "Login successful!";
      message.success(defaultLoginSuccessMessage);
      localStorage.setItem(LS_TOKEN_NAME, res.Authorization || "");

      if (!location) return;

      const redirect = getQueryParamInUrl(location.href, "redirect");

      /* if (userInfo?.role === UserRole.EDITOR) {
        // location.href = redirect || '/item/ean-detail';
      } else if (userInfo?.role === UserRole.WAREHOUSE) {
        // location.href = redirect || '/orders/order-detail';
      } else {
        location.href = redirect || '/';
    } */
      location.href = redirect || "/";

      return;
    } catch (error: any) {
      console.error(error);
      setErrorMsg("Incorrect username or password");
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <LoginForm
          logo={<img src={`${PUBLIC_PATH}logo.png`} />}
          title="WHC Supplier"
          subTitle={" "}
          initialValues={{
            autoLogin: true,
          }}
          onFinish={async (values) => {
            await handleSubmit(values as API.LoginParams);
          }}
          submitter={{ searchConfig: { submitText: "Login" } }}
        >
          {errorMsg && <LoginMessage content={errorMsg} />}

          <ProFormText
            name="username"
            fieldProps={{
              size: "large",
              prefix: <UserOutlined className={styles.prefixIcon} />,
            }}
            placeholder={"Username"}
            rules={[
              {
                required: true,
                message: "Please input your username!",
              },
            ]}
          />
          <ProFormText.Password
            name="password"
            fieldProps={{
              size: "large",
              prefix: <LockOutlined className={styles.prefixIcon} />,
            }}
            placeholder={"Password"}
            rules={[
              {
                required: true,
                message: "Please input your password!",
              },
            ]}
          />
        </LoginForm>
      </div>
      <Footer />
    </div>
  );
};

export default Login;

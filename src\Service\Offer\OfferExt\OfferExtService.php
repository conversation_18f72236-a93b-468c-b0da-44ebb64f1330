<?php

declare(strict_types=1);

namespace App\Service\Offer\OfferExt;

use App\Exception\BaseException;
use App\Models\Offer\OfferExt;
use App\ModelsOrg\OrgOffer;

final class OfferExtService extends Base
{
    public function create(array $input): OfferExt
    {
        return OfferExt::create($input);
    }

    public function usOfferToOrg(OfferExt $ext)
    {
        OrgOffer::query()
            ->where('offer_sid', $ext->offer_no)
            ->update(['status' => $ext->status]);
    }

    public function update($id, $input): OfferExt
    {
        $row = OfferExt::findOrFail($id);
        $row->update($input);

        if (key_exists('status', $input)) {
            $this->usOfferToOrg($row);
        }
        return $row;
    }

    public function updateOrCreate(array $input): OfferExt
    {
        $offerNo = $input['offer_no'] ?? null;
        if (!$offerNo) {
            BaseException::raiseInvalidRequest("Offer No is required!");
        }

        /** @var OfferExt $offerExt */
        $offerExt = OfferExt::query()->firstOrNew(['offer_no' => $offerNo]);
        if (key_exists('details', $input) && $input['details']) {
            $input['details'] = array_replace_recursive($offerExt->details ?? ['x' => 1], $input['details']);
        }

        $offerExt->fill($input);
        $offerExt->save();

        if (key_exists('status', $input)) {
            $this->usOfferToOrg($offerExt);
        }
        return $offerExt;
    }

    public function getOne(int $id, array $params=[]): OfferExt
    {
        return $this->offerExtRepository->getQueryBuilder()->where('id', $id)->first();
    }

    public function getOfferExtsByPage(
        int $page,
        int $perPage,
        ?array $params
    ): array
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($perPage < 1) {
            $perPage = self::DEFAULT_PER_PAGE_PAGINATION;
        }

        return $this->offerExtRepository->getOfferExtsByPage(
            $page,
            $perPage,
            $params
        );
    }

    public function delete($ids): void
    {
        $arr = is_array($ids) ? $ids : explode(',', $ids);
        $this->offerExtRepository->getQueryBuilder()->whereIn('id', $arr)->delete();
    }
}

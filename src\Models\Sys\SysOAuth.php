<?php

namespace App\Models\Sys;

use App\Models\BaseModel;
use App\Models\User;

/**
 * @property integer $id
 * @property string $type
 * @property string $token
 * @property string $refresh_token
 * @property string $token_expired_at
 * @property string $client_id_expired_at
 * @property string $client_secret_expired_at
 *
 * @property User $user
 */
class SysOAuth extends BaseModel
{
    public const TYPE_M365 = 'M365';

    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'sys_oauth';

    /**
     * The "type" of the auto-incrementing ID.
     * 
     * @var string
     */
    protected $keyType = 'integer';

    /**
     * @var array
     */
    protected $fillable = ['type', 'token', 'refresh_token', 'token_expired_at', 'client_id_expired_at', 'client_secret_expired_at'];

}

<?php

declare(strict_types=1);

namespace App\Service\Customer\CustomerAddress;

use App\Repository\Customer\CustomerAddressRepository;
use App\Service\BaseService;
use Slim\Container;

abstract class Base extends BaseService
{
    protected CustomerAddressRepository $customerAddressRepository;

    public function __construct(Container $container)
    {
        $this->customerAddressRepository = $container->get(CustomerAddressRepository::class);
    }

    public function getCustomerAddressRepository()
    {
        return $this->customerAddressRepository;
    }
}


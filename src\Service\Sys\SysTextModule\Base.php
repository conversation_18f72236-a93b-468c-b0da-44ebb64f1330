<?php

declare(strict_types=1);

namespace App\Service\Sys\SysTextModule;

use App\Service\BaseService;
use App\Repository\Sys\SysTextModuleRepository;
use Slim\Container;

abstract class Base extends BaseService
{
    private const REDIS_KEY = 'Sys:%s';
    protected SysTextModuleRepository $sysTextModuleRepository;

    public function __construct(Container $container)
    {
        $this->sysTextModuleRepository = $container->get(SysTextModuleRepository::class);
    }

    public function getSysTextModuleRepository()
    {
        return $this->sysTextModuleRepository;
    }
}


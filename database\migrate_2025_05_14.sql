DELIMITER $$

DROP FUNCTION IF EXISTS `get_pk`$$

CREATE FUNCTION `get_pk`(p_table_name VARCHAR(50)) RETURNS varchar(15) CHARSET latin1
BEGIN
    DECLARE location VARCHAR(1);
    SET location = 'Z';
    update tbl_keys SET max_no = max_no + 1 WHERE table_name = p_table_name;
    return CONCAT(location, (SELECT CONCAT(
                                            prefix,
                                            LPAD(CONVERT(max_no, CHAR), 10, '0')
                                        )
                             FROM tbl_keys
                             WHERE table_name = p_table_name));
END$$

DELIMITER ;


DROP TABLE IF EXISTS `tbl_keys`;

CREATE TABLE `tbl_keys`
(
    `table_name` varchar(50) NOT NULL,
    `prefix`     varchar(4)  NOT NULL,
    `max_no`     int(10)     NOT NULL DEFAULT 1,
    PRIMARY KEY (`table_name`) USING BTREE,
    UNIQUE KEY `ind_prefix` (`prefix`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = latin1
  ROW_FORMAT = COMPACT;

insert into `tbl_keys`(`table_name`, `prefix`, `max_no`)
values ('supplier', 'SUPP', 0);

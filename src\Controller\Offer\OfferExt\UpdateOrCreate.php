<?php

declare(strict_types=1);

namespace App\Controller\Offer\OfferExt;

use App\Lib\Func;
use Slim\Http\Request;
use Slim\Http\Response;

final class UpdateOrCreate extends Base
{
    public function __invoke(Request $request, Response $response): Response
    {
        $input = (array)$request->getParsedBody();

		$row = $this->offerExtService->updateOrCreate($input);

        return $this->jsonResponse($response, 'success', $row, 201);
    }
}

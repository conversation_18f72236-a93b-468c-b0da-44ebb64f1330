drop table if exists offer_customer;

drop table if exists offer_ext;

CREATE TABLE `offer_ext`
(
    `id`          int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'PK',
    `offer_no`    varchar(15)      NOT NULL COMMENT 'Offer No',
    `notes`       longtext DEFAULT NULL COMMENT 'Offer notes',
    `notes_ek_vk` longtext DEFAULT NULL COMMENT 'Enter Internal (EK/VK)',
    `created_on`  datetime DEFAULT NULL,
    `created_by`  int(11)  DEFAULT NULL COMMENT 'Creator',
    `updated_on`  datetime DEFAULT NULL,
    `updated_by`  int(11)  DEFAULT NULL COMMENT 'Updater',
    PRIMARY KEY (`id`),
    UNIQUE KEY `UQ_offer_ext_offer_no` (`offer_no`),
    KEY `IDX_offer_ext_updated_on` (`updated_on`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;

ALTER TABLE `customer_call`
    COMMENT ='Offer Notes table: `customer_id` has no meaning';


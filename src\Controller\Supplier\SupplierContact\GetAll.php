<?php

declare(strict_types=1);

namespace App\Controller\Supplier\SupplierContact;

use Slim\Http\Request;
use Slim\Http\Response;

final class GetAll extends Base
{
    public function __invoke(Request $request, Response $response): Response
    {
        $page = $request->getQueryParam('page', null);
        $perPage = $request->getQueryParam('perPage', null);
        $params = $request->getParams();

        $data = $this->supplierContactService->getSupplierContactsByPage((int)$page, (int)$perPage, $params);

        return $this->jsonResponse($response, 'success', $data, 200);
    }
}

<?php

namespace App\Models\Offer;

use App\Models\BaseModel;
use App\Models\File;

/**
 * @property integer $id
 * @property string $offer_no
 * @property string $title
 * @property string $description
 * @property string $details
 * @property number $status
 * @property string $created_on
 * @property string $updated_on
 * @property integer $created_by
 * @property integer $updated_by
 *
 * @property File[] $files
 */
class OfferBlog extends BaseModel
{
    public $timestamps = true;

    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'offer_blog';

    /**
     * @var array
     */
    protected $fillable = ['offer_no', 'title', 'description', 'status', 'created_on', 'updated_on', 'created_by', 'updated_by'];

    protected $casts = [
        'details' => 'json',
    ];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function files()
    {
        return $this->belongsToMany('App\Models\File', 'offer_blog_file');
    }
}

<?php

namespace App\Controller\FileBrowser;

use App\Exception\BaseException;
use App\Lib\FileBrowserLib;
use App\Lib\FileLib;
use App\Lib\Func;
use Slim\Container;
use Slim\Http\Request;
use Slim\Http\Response;

class GetFile extends Base
{
    public function __construct(Container $container)
    {
        parent::__construct($container);
    }

    /**
     * @throws \Exception
     */
    public function __invoke(Request $request, Response $response, array $args): Response
    {
        $params = $request->getParams();
        $id = $params['id'] ?? '';
        $basePath = FileBrowserLib::id2path($id);
        if ($basePath == '!') $basePath = '';
        if (!$basePath) BaseException::raiseInvalidRequest('Path invalid.');

        $absPath = Func::pathJoin($this->fileBrowserService->getRootPath() ?? '', $basePath);
        if ($absPath && file_exists($absPath)) {
            FileLib::downloadFile($absPath);
        } else {
            BaseException::raiseInvalidRequest('Path invalid.');
        }

        return $this->jsonResponse($response, 'success',$params, 200);
    }
}
<?php

namespace App\Lib;

use App\Models\SyncLog;
use App\Models\Sys\Dict;

/**
 * Class Func for model related stuffs.
 *
 * @package App\Lib
 * <AUTHOR>
 */
final class FuncModel
{
    // application level session data
    protected static ?array $data = [];

    public static ?string $uuid = null;

    public static ?int $batchCode = null;

    // system dict data
    protected static ?array $dictMap = null;

    public static function reloadDictList()
    {
        self::$dictMap = Dict::query()->active()->get()->keyBy('code')->toArray();
    }

    public static function getDict($type = null, $code = null, $valueOnly = false)
    {
        if (self::$dictMap == NULL) {
            self::reloadDictList();
        }

        if ($type != null && $code == null) {
            $ret = array();
            foreach (self::$dictMap as $code => $row) {
                if ($row['type'] == $type) {
                    if ($valueOnly)
                        $ret[$code] = $row['value'];
                    else
                        $ret[$code] = $row;
                }
            }
            return $ret;
        }

        if ($code == null) {
            return self::$dictMap;
        } else {
            return $valueOnly ? self::$dictMap[$code]['value'] ?? '' : self::$dictMap[$code];
        }
    }

    public static function getDictValue($code = null, $useLabel = false)
    {
        if (self::$dictMap == NULL) {
            self::reloadDictList();
        }
        $dict = self::getDict(null, $code);
        return $dict ? ($useLabel ? $dict['label'] ?? null : $dict['value'] ?? null) : null;
    }

    public static function setData(?string $key, $data = null)
    {
        if ($key === null) {
            if ($data === null) {
                self::$uuid = uniqid();
            }
            self::$data = $data ?? [];
        } else {
            self::$data[$key] = $data;
        }
    }

    public static function getDataValue(string $key)
    {
        return self::$data[$key] ?? null;
    }

    /**
     * Create a magento sync log.
     *
     * @param int $syncType Sync type
     * @param string $name Name
     * @param array|string $attributesOrNote Extra data
     * @return int
     */
    public static function createSyncLog(int $syncType, string $name, array|string $attributesOrNote = []): int
    {
        $row = new SyncLog($attributesOrNote && is_array($attributesOrNote) ? $attributesOrNote : []);
        $row->sync_type = $syncType;
        $row->name = $name . (Func::isCLI() ? ' (cron)' : '');
        $row->action_type = Func::isCLI() ? SyncLog::ACTION_TYPE_CRON : SyncLog::ACTION_TYPE_SYSTEM_UI;
        $row->status = SyncLog::STATUS_STARTED;

        if (is_array($attributesOrNote) && isset($attributesOrNote['batch_code'])) {
            $row->batch_code = $attributesOrNote['batch_code'];
        } else {
            if (self::$batchCode) {
                $row->batch_code = self::$batchCode;
            } else {
                $row->batch_code = Func::randomTs();
            }
        }

        if (is_string($attributesOrNote)) {
            $row->note = $attributesOrNote;
        } else {
            if ($attributesOrNote['status'] ?? null)
                $row->status = $attributesOrNote['status'];
        }
        $row->save();

        // Update the system level data.
        self::setData(SyncLog::class . '.lastId', $row->id);             // Keep last Ids.
        self::setData(SyncLog::class . '.batchCode', $row->batch_code);  // Keep for later logs to be bounded
        return $row->id;
    }

    /**
     * Update Magento sync log data
     *
     * @param int $id
     * @param string $status
     * @param array|string $attributesOrNote
     * @return SyncLog
     */
    public static function updateSyncLog(int $id, string $status = SyncLog::STATUS_SUCCESS, array|string $attributesOrNote = []): SyncLog
    {
        $id = $id ?? self::getDataValue(SyncLog::class . '.lastId');

        /** @var SyncLog $row */
        $row = SyncLog::findOrFail($id);
        $row->status = $status;

        if (is_string($attributesOrNote)) {
            $row->note = $attributesOrNote;
            $row->save();
        } else {
            $row->update($attributesOrNote);
        }

        return $row;
    }

    public static function cleanUp()
    {

    }

    /**
     * Important.
     *
     * @return void
     */
    public static function setGroupConcatLimit()
    {
        Func::getDb()->statement("SET GLOBAL table_definition_cache = 4096");
    }

    public static function likeValue($str, $prefix = '', $suffix = '%', $quote = false)
    {
        $val = $prefix . str_replace('_', '\_', $str ?? '') . $suffix;
        if ($quote) {
            $val = Func::dbQuote($val);
        }
        return $val;
    }

    /**
     * Get Country ISO3 Code by caching mechanism.
     *
     * @param string $code
     * @return mixed
     */
    public static function getCountIso3Code(string $code)
    {
        $cachedCode3 = self::getDataValue('C_' . $code);
        if ($cachedCode3) {
            return $cachedCode3;
        } else {
            $cachedCode3 = \App\Models\Sys\Country::query()->where('code', $code)->value('iso3_code');
            self::setData('C_' . $code, $cachedCode3);

            return $cachedCode3;
        }
    }


}

import { request } from "umi";
import { paramsSerializer } from "../api";
import { RequestConfig } from "@umijs/max";
import { RequestOptionsType } from "@ant-design/pro-components";

const urlPrefix = "/api/supplier/supplier";

/**
 * Get Magento Suppliers list
 *
 * GET /api/supplier/supplier
 */
export async function getSupplierListByPage(params: API.PageParams, sort?: any, filter?: any) {
  return request<API.ResultObject<API.PaginatedResult<API.Supplier> & { productCategoryKv?: Record<number, string> }>>(`${urlPrefix}`, {
    method: "GET",
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == "success",
    total: res.message.pagination.totalRows,
    pagination: res.message.pagination, // For total row pagination hack.
  }));
}

/**
 * Get InvoiceCheck stats from WHC_Task
 *
 * GET /api/supplier/supplier/getOrgInvoiceCheckStats
 */
export async function getSupplierLoInvoiceCheckStatsByPage(params: API.PageParams, sort?: any, filter?: any) {
  return request<API.ResultObject<API.PaginatedResult<APITask.Supplier & APITask.FinDetail> & { productCategoryKv?: Record<number, string> }>>(
    `${urlPrefix}/getOrgInvoiceCheckStats`,
    {
      method: "GET",
      params: {
        ...params,
        perPage: params.pageSize,
        page: params.current,
        sort,
        filter,
      },
      withToken: true,
      paramsSerializer,
    },
  ).then((res) => ({
    data: res.message.data,
    success: res.status == "success",
    total: res.message.pagination.totalRows,
    pagination: res.message.pagination,
    summary: res.message.summary,
  }));
}

/**
 * Get Details from WHC_Task
 *
 * GET /api/supplier/supplier/getOrgInvoiceCheckStatsDetailsList
 */
export async function getOrgInvoiceCheckStatsDetailsList(params: API.PageParams, sort?: any, filter?: any) {
  return request<API.ResultObject<API.PaginatedResult<API.Supplier> & { productCategoryKv?: Record<number, string> }>>(
    `${urlPrefix}/getOrgInvoiceCheckStatsDetailsList`,
    {
      method: "GET",
      params: {
        ...params,
        perPage: params.pageSize,
        page: params.current,
        sort,
        filter,
      },
      withToken: true,
      paramsSerializer,
    },
  ).then((res) => ({
    data: res.message.data,
    success: res.status == "success",
    total: res.message.pagination.totalRows,
    pagination: res.message.pagination,
  }));
}

export async function getSupplier(id?: number, params?: API.Supplier & API.PageParams) {
  return getSupplierListByPage({ id, ...params, pageSize: 1, page: 1 }).then((res) => {
    return res.data?.[0];
  });
}

/**
 * Get Magento Supplier x Email matrix
 *
 * GET /api/supplier/supplier/email-matrix
 */
export async function getSupplier2EmailMatrix(params: API.PageParams, sort?: any, filter?: any) {
  return request<API.ResultObject<API.PaginatedResult<API.Supplier> & { templates?: API.EmailTemplate[] }>>(`${urlPrefix}/email-matrix`, {
    method: "GET",
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == "success",
    total: res.message.pagination.totalRows,
    pagination: res.message.pagination, // For total row pagination hack.
    templates: res.message.templates, // For total row pagination hack.
  }));
}

/**
 * Get OrgA kv List from WHC_ORG
 *
 * GET /api/supplier/supplier/getOrgAList
 */
export async function getOrgAList(params: API.PageParams) {
  return request<API.ResultObject<Record<string, string>>>(`${urlPrefix}/getOrgAList`, {
    method: "GET",
    params: {
      ...params,
      perPage: 200,
      page: params.current,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message);
}

/**
 * Get all offers for a specific supplier
 *
 * GET /api/supplier/supplier/getSupplierOffers
 */
export async function getSupplierOffers(params: API.PageParams & { supp_supplier_id: number; status?: number }, sort?: any, filter?: any) {
  return request<API.ResultList<APIOrg.Offer>>(`${urlPrefix}/getSupplierOffers`, {
    method: "GET",
    params: {
      ...params,
      perPage: params.pageSize,
      page: params.current,
      sort,
      filter,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => ({
    data: res.message.data,
    success: res.status == "success",
    total: res.message.pagination.totalRows,
    pagination: res.message.pagination,
  }));
}

/**
 * Down syncing
 *
 * PUT /api/supplier/supplier/dsSupplier
 */
export async function dsSupplier(options?: { [key: string]: any }) {
  return request<API.BaseResult>(`${urlPrefix}/dsSupplier`, {
    method: "PUT",
    ...(options || {}),
  });
}

/**
 * Create supplier.
 *
 *  POST /api/supplier/supplier */
export async function addSupplier(data?: API.Supplier | FormData, options?: { [key: string]: any }) {
  const config: RequestConfig = {
    method: "POST",
    data,
    ...(options || {}),
  };
  return request<API.ResultObject<API.Supplier>>(`${urlPrefix}`, config).then((res) => res.message);
}

/**
 * Update magSupplier extension data.
 *
 *  PUT /api/supplier/supplier/{id}/update */
export async function updateSupplier(id?: number, data?: API.Supplier | FormData, options?: { [key: string]: any }) {
  const config: RequestConfig = {
    method: "PUT",
    data,
    ...(options || {}),
  };
  return request<API.ResultObject<API.Supplier>>(`${urlPrefix}/${id}/update`, config).then((res) => res.message);
}

/**
 * Update magSupplier extension data.
 *
 *  PUT /api/supplier/supplier/{id}/meta */
export async function updateSupplierMetaSupplierId(
  id?: number,
  data?: (Omit<API.Supplier, "meta"> & { meta: Record<string, any> }) | FormData,
  options?: { [key: string]: any },
) {
  const config: RequestConfig = {
    method: "PUT",
    data,
    ...(options || {}),
  };
  return request<API.ResultObject<API.Supplier>>(`${urlPrefix}/${id}/meta`, config).then((res) => res.message);
}

/**
 * Get sales area list
 *
 * GET /api/supplier/supplier/getSalesAreaACList
 */
export async function getSalesAreaACList(params: API.PageParams) {
  return request<API.ResultObject<RequestOptionsType[]>>(`${urlPrefix}/getSalesAreaACList`, {
    method: "GET",
    params: {
      ...params,
      perPage: 200,
      page: params.current,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message);
}

/**
 * Get sales area list
 *
 * GET /api/supplier/supplier/getSupplierLangACList
 */
export async function getSupplierLangACList(params: API.PageParams) {
  return request<API.ResultObject<RequestOptionsType[]>>(`${urlPrefix}/getSupplierLangACList`, {
    method: "GET",
    params: {
      ...params,
      perPage: 200,
      page: params.current,
    },
    withToken: true,
    paramsSerializer,
  }).then((res) => res.message);
}

/** delete DELETE /api/supplier/supplier/{id} */
export async function deleteSupplier(id?: string | number, options?: { [key: string]: any }) {
  return request<Record<string, any>>(`${urlPrefix}/${id}`, {
    method: "DELETE",
    ...(options || {}),
  });
}

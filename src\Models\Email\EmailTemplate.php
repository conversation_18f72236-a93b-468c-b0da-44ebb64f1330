<?php

namespace App\Models\Email;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Builder;

/**
 * @property integer $id
 * @property integer $category_id
 * @property integer $sort
 * @property string $title
 * @property string $subject
 * @property string $text_html
 * @property string $text_plain
 * @property string $created_on
 * @property string $updated_on
 *
 * @property EmailTemplateCategory $category
 */
class EmailTemplate extends BaseModel
{
    public $timestamps = true;

    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'email_template';

    /**
     * @var array
     */
    protected $fillable = ['category_id', 'sort', 'title', 'subject', 'text_html'
        , 'text_plain'
        , 'created_on'
        , 'updated_on'
    ];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function category()
    {
        return $this->belongsTo('App\Models\Email\EmailTemplateCategory', 'category_id');
    }

    public function scopeSortDefault($query, $dir='ascend'): Builder
    {
        $query->orderBy(EmailTemplateCategory::query()->whereColumn('email_template_category.id', 'email_template.category_id')->select('sort'), ($dir == 'descend') ? 'DESC' : 'ASC');
        $query->orderBy('email_template.sort', ($dir == 'descend') ? 'DESC' : 'ASC');

        return $query;
    }
}

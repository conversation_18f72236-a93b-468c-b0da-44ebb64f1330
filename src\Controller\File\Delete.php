<?php

declare(strict_types=1);

namespace App\Controller\File;

use App\Models\File;
use Slim\Http\Request;
use Slim\Http\Response;

/**
 * Delete a single file in Uploaded path
 *
 */
final class Delete extends Base
{
    public function __invoke(Request $request, Response $response, array $args): Response
    {
        $id = $args['id'] ?? '';
        if (!$id) {
            \App\Exception\Base::raiseInvalidRequest();
        }
        $fileObj = File::findOrFail($id);

        $this->fileService->removeFile($fileObj);

        $params = $request->getParams();
        if ($params['ref_type'] ?? null) {

        }

        return $this->jsonResponse($response, 'success', true, 200);
    }
}

<?php

declare(strict_types=1);

namespace App\Service\User;

final class Find extends Base
{
    public function getUsersByPage(
        int $page,
        int $perPage,
        ?array $params
    ): array {
        if ($page < 1) {
            $page = 1;
        }
        if ($perPage < 1) {
            $perPage = self::DEFAULT_PER_PAGE_PAGINATION;
        }

        return $this->userRepository->getUsersByPage(
            $page,
            $perPage,
            $params
        );
    }

    public function getAll(): array
    {
        return $this->userRepository->getAll();
    }

    public function getOne(int $userId): array
    {
        if (self::isRedisEnabled() === true) {
            $user = $this->getUserFromCache($userId);
        } else {
            $user = $this->getUserFromDb($userId);
        }

        return $user;
    }

    public function getOneByAddress(string $address, int $selectType): array
    {
        if (self::isRedisEnabled() === true) {
            $user = $this->getUserFromCacheByAddress($address, $selectType);
        } else {
            $user = $this->getUserFromDbByAddress($address, $selectType);
        }
        return $user;
    }
}

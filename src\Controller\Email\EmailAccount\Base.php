<?php

declare(strict_types=1);

namespace App\Controller\Email\EmailAccount;

use App\Controller\BaseController;
use App\Service\Email\EmailAccount\EmailAccountService;
use Respect\Validation\Validator as v;
use Slim\Container;

abstract class Base extends BaseController
{
    public EmailAccountService $emailAccountService;

    public function __construct(Container $container)
    {
        parent::__construct($container);
        $this->emailAccountService = $container->get(EmailAccountService::class);
    }

    public function validate(array $input): bool
    {
        $validator = v::key('email', v::stringType()->length(1, 255));

        if (!$validator->validate($input)) {
            \App\Exception\Base::raiseInvalidRequest();
        }
        return true;
    }
}

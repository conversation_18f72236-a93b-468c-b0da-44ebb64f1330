<?php

declare(strict_types=1);

namespace App\Service\Supplier\SupplierContact;

use App\Service\BaseService;
use App\Repository\Supplier\SupplierContactRepository;
use Slim\Container;

abstract class Base extends BaseService
{
    private const REDIS_KEY = 'Supplier:%s';
    protected SupplierContactRepository $supplierContactRepository;

    public function __construct(Container $container)
    {
        $this->supplierContactRepository = $container->get(SupplierContactRepository::class);
    }

    public function getSupplierContactRepository()
    {
        return $this->supplierContactRepository;
    }
}


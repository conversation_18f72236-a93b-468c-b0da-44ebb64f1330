CREATE TABLE `offer_blog`
(
    `id`          int(11)     NOT NULL AUTO_INCREMENT,
    `offer_no`    varchar(15) NOT NULL,
    `title`       varchar(255) DEFAULT NULL COMMENT 'Blog Title',
    `description` longtext     DEFAULT NULL COMMENT 'Blog Description',
    `created_on`  datetime     DEFAULT NULL,
    `updated_on`  datetime     DEFAULT NULL,
    `created_by`  int(11)      DEFAULT NULL,
    `updated_by`  int(11)      DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `UQ_offer_blog_offer_no` (`offer_no`),
    <PERSON>EY `IDX_offer_blog_title` (`title`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;

CREATE TABLE `offer_blog_file`
(
    `offer_blog_id` int(11)             NOT NULL,
    `file_id`       bigint(20) unsigned NOT NULL,
    <PERSON>IMARY <PERSON>Y (`offer_blog_id`, `file_id`),
    <PERSON><PERSON>Y `FK_offer_blog_file_file_id` (`file_id`),
    CONSTRAINT `FK_offer_blog_file_file_id` FOREIGN KEY (`file_id`) REFERENCES `file` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `FK_offer_blog_file_offer_blog_id` FOREIGN KEY (`offer_blog_id`) REFERENCES `offer_blog` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;


CREATE TABLE `offer_newsletter`
(
    `id`            int(11)     NOT NULL AUTO_INCREMENT,
    `offer_no`      varchar(15) NOT NULL,
    `product_title` varchar(255)   DEFAULT NULL COMMENT 'Product title',
    `case_qty`      int            DEFAULT NULL COMMENT 'Pcs / Case',
    `ve_pallet`     int            DEFAULT NULL COMMENT 'Cases / Pallet',
    `price`         decimal(20, 4) DEFAULT 0 COMMENT 'Price',
    `sort_order`    int            DEFAULT 0 COMMENT 'Sort order',
    `details`       longtext       DEFAULT 0 COMMENT 'Details info in JSON',
    `created_on`    datetime       DEFAULT NULL,
    `updated_on`    datetime       DEFAULT NULL,
    `created_by`    int(11)        DEFAULT NULL,
    `updated_by`    int(11)        DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `IDX_offer_newsletter_offer_no` (`offer_no`),
    KEY `IDX_offer_newsletter_product_title` (`product_title`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;


drop table if exists offer_newsletter_file;

CREATE TABLE `offer_newsletter_file`
(
    `offer_newsletter_id` int(11)             NOT NULL,
    `file_id`             bigint(20) unsigned NOT NULL,
    PRIMARY KEY (`offer_newsletter_id`, `file_id`),
    KEY `FK_offer_newsletter_file_file_id` (`file_id`),
    CONSTRAINT `FK_offer_newsletter_file_file_id` FOREIGN KEY (`file_id`) REFERENCES `file` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `FK_offer_newsletter_file_offer_newsletter_id` FOREIGN KEY (`offer_newsletter_id`) REFERENCES `offer_newsletter` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;

<?php

declare(strict_types=1);

namespace App\Controller\Sys\Dict;

use Slim\Http\Request;
use Slim\Http\Response;

final class Update extends Base
{
    public function __invoke(Request $request, Response $response, array $args): Response
    {
        $input = (array) $request->getParsedBody();
        $code = $input['code'] ?? null;
        if (!$code) {
            \App\Exception\Base::raiseNotFound('Code is required.');
        }

        $row = $this->dictService->update($code, $input);

        return $this->jsonResponse($response, 'success', $row, 200);
    }
}

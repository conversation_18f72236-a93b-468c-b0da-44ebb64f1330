<?php

declare(strict_types=1);

namespace App\Service\Email\EmailAccount;

use App\Service\BaseService;
use App\Repository\Email\EmailAccountRepository;
use Slim\Container;

abstract class Base extends BaseService
{
    private const REDIS_KEY = 'Email:%s';
    protected EmailAccountRepository $emailAccountRepository;

    public function __construct(Container $container)
    {
        $this->emailAccountRepository = $container->get(EmailAccountRepository::class);
    }

    public function getEmailAccountRepository()
    {
        return $this->emailAccountRepository;
    }
}


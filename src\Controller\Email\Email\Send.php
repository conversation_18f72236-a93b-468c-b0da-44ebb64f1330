<?php

declare(strict_types=1);

namespace App\Controller\Email\Email;

use App\Models\Offer\OfferTemplate;
use App\Service\Email\Email\EmailSendByOrgOfferService;
use Slim\Http\Request;
use Slim\Http\Response;

final class Send extends Base
{
    public function __invoke(Request $request, Response $response, array $args): Response
    {
        $input = $request->getParsedBody();
        $mode = $input['mode'] ?? null;

        $uploadedFiles = $request->getUploadedFiles();

        if ($mode == 'byOrgOffer') {
            $offerNo = $input['offer_no'];
            /** @var OfferTemplate $offerTemplate */
            $offerTemplate = OfferTemplate::query()->where('offer_no', $offerNo)->with('files')->firstOrFail();

            /** @var EmailSendByOrgOfferService $service */
            $service = $this->container->get(EmailSendByOrgOfferService::class);

            $files = $uploadedFiles['files'] ?? [];
            foreach ($offerTemplate->files as $file) {
                $files[] = $file;
            }
            $service->send($input, $files);
        } else {
            $this->emailService->send($input, $uploadedFiles['files'] ?? []);
        }

        return $this->jsonResponse($response, 'success', true, 200);
    }
}

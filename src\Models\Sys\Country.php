<?php

namespace App\Models\Sys;

use App\Models\BaseModel;

/**
 * @property integer $id
 * @property string $code
 * @property string $iso3_code
 * @property string $name
 * @property string $dial_code
 * @property string $gsl_code
 *
 * @property CountryRegion[] $countryRegions
 */
class Country extends BaseModel
{
    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'sys_country';

    /**
     * @var array
     */
    protected $fillable = ['code', 'iso3_code', 'name', 'dial_code', 'gsl_code'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function countryRegions()
    {
        return $this->hasMany('App\Models\Sys\CountryRegion', 'country_code', 'code');
    }
}

<?php

namespace App\Lib;

use JetBrains\PhpStorm\ArrayShape;
use PhpOffice\PhpSpreadsheet\Exception;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ExcelExportLib
{
    // XLS Data types
    const DATA_TYPE_STRING = 'string';
    const DATA_TYPE_NUM = 'number';
    const DATA_TYPE_INT = 'INT';
    const DATA_TYPE_DATE = 'date';
    const DATA_TYPE_DATETIME = 'datetime';

    // Number formatting in Excel.
    const FORMAT_I = "_(* #,##0_);_(* (#,##0);_(* \"\"??_);_(@_)";
    const FORMAT_I_NORM = "# ?/?";
    const FORMAT_STRING_DIGIT = "0";
    const FORMAT_D = '_(* #,##0.00_);_(* (#,##0.00);_(* ""??_);_(@_)';
    const FORMAT_DATE = 'mmm yy';
    const FORMAT_DATE_DE = 'dd.mm.yyyy';
    const FORMAT_DATE_TIME = 'mmm yy';
    const DOWNLOAD_DIR = DS . "export" . DS;

    public static array $borderThin = array(
        'borders' => array(
            'outline' => array(
                'borderStyle' => Border::BORDER_THIN,
                'color' => array('argb' => '55555500'),
            ),
        )
    );
    public static array $borderThick = array(
        'borders' => array(
            'outline' => array(
                'borderStyle' => Border::BORDER_MEDIUM,
                'color' => array('argb' => '55555500'),
            ),
        )
    );
    public static array $borderTopThin = array(
        'borders' => array(
            'top' => array(
                'borderStyle' => Border::BORDER_THIN,
                'color' => array('argb' => '55555500'),
            ),
        )
    );

    public static array $headerStyle = array(
        'alignment' => array(
            'horizontal' => Alignment::HORIZONTAL_CENTER,
            'vertical' => Alignment::VERTICAL_CENTER,
        ),
        'font' => array(
            'bold' => true,
        ),
    );

    public static array $styleBold = array(
        'font' => array(
            'bold' => true,
        ),
    );

    public function __construct()
    {
        global $container;
        $uploadPath = $container->get('settings')['upload_dir'];
        if (!file_exists($uploadPath . self::DOWNLOAD_DIR)) {
            mkdir($uploadPath . self::DOWNLOAD_DIR, 0777, true);
        }
    }

    public static function init()
    {
        global $container;
        $uploadPath = $container->get('settings')['upload_dir'];
        if (!file_exists($uploadPath . self::DOWNLOAD_DIR)) {
            mkdir($uploadPath . self::DOWNLOAD_DIR, 0777, true);
        }
    }

    /**
     * @param $docTitle
     * @param array $options [ArrayShape(['creator' => "", 'keyword' => ""])]
     * @return Spreadsheet
     * @throws Exception
     */
    public static function createSpreadsheet($docTitle, array $options=[]): Spreadsheet
    {
        /*$tpl_path = $model->get_sys_config(null, CODE_XLSM_TPL_PATH);
        if (file_exists($tpl_path))
            $spreadsheet = IOFactory::load($tpl_path);
        else*/
        $spreadsheet = new Spreadsheet();

        $spreadsheet->getProperties()->setCreator($options['creator'] ??  'WHC')
            ->setLastModifiedBy($options['creator'] ??  'WHC')
            ->setTitle($docTitle)
            ->setSubject($docTitle)
            ->setDescription($docTitle)
            ->setKeywords($options['keyword'] ??  'office 2007 openxml php')
            ->setCategory($docTitle);

        $spreadsheet->setActiveSheetIndex(0);

        return $spreadsheet;
    }

    /**
     * Get active sheet with page setting up
     *
     * @param Spreadsheet $spreadsheet
     * @return Worksheet
     */
    public static function getActiveSheet(Spreadsheet &$spreadsheet): Worksheet
    {
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->getPageSetup()->setFitToWidth(1);
        $sheet->getPageSetup()->setFitToHeight(null);
        return $sheet;
    }
}
<?php

namespace App\Controller\FileBrowser;

use App\Controller\BaseController;
use App\Lib\FuncModel;
use App\Models\Sys\Dict;
use App\Service\FileBrowser\FileBrowserService;
use Slim\Container;

class Base extends BaseController
{
    protected FileBrowserService $fileBrowserService;

    public function __construct(Container $container)
    {
        parent::__construct($container);

        $this->fileBrowserService = $container->get(FileBrowserService::class);

        $this->fileBrowserService->setRootPath(FuncModel::getDictValue(Dict::CODE_FM_DATA_FILE_BASE_PATH));
    }
}
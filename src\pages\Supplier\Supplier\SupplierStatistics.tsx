import { Card, Col, Row, Space, Tag, Typography } from "antd";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { PageContainer } from "@ant-design/pro-layout";
import type { ProColumns, ActionType } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";

import { CURRENT_YEAR, DEFAULT_PER_PAGE_PAGINATION, DT_FORMAT_DM, DT_FORMAT_DMY_SHORT } from "@/constants";
import Util, { DateRangeType, nf2, sn } from "@/util";
import type { ProFormInstance } from "@ant-design/pro-form";
import { ProFormSelect } from "@ant-design/pro-form";
import ProForm from "@ant-design/pro-form";
import { getSupplierListByPage, getSupplierLoInvoiceCheckStatsByPage } from "@/services/app/Supplier/supplier";
import { ProFormCheckbox, ProFormDigit, ProFormRadio } from "@ant-design/pro-components";
import useOrgAOptions from "@/hooks/BasicData/useOrgAOptions";
import SupplierStatisticsDetailListModal from "./components/SupplierStatisticsDetailListModal";
import { useModel } from "@umijs/max";
import { getUserSettingOrgStatsItemOptionsByLevel, UserSettingOrgStatsItemEnum } from "@/pages/UsersList/components/UserSettingForm";
import dayjs from "dayjs";

const FyListOptions = Util.dtBuildFyList(Util.dtCurrentFy(), 5);
const fyMonthsRange = Util.dtBuildRangesInFy(new Date(), false, true);

type RecordType = APITask.Supplier & APITask.FinDetail & { org_supplier?: APIOrg.VSupplier };
type SearchFormValueType = Partial<API.Supplier> & { view_mode: "monthly" | "yearly" };
const DefaultSearchFormValues = {
  view_mode: "monthly",
  years_count: 6,
};

const SupplierStatistics: React.FC = () => {
  const { initialState } = useModel("@@initialState");
  console.log(initialState?.currentUser);

  const searchFormRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();

  const [loading, setLoading] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<{ supp_supplier_id?: number; y?: number; ym?: string }>();
  const [totalRow, setTotalRow] = useState<RecordType>();

  const { setSelectedOrgA, orgAOptions, orgAList, selectedOrgA } = useOrgAOptions();

  // stats related
  const [statsMode, setStatsMode] = useState("EK_Summe"); // Stats mode to show Summe or EK_Summe or Ertrag from WHC_Task
  const [viewMode, setViewMode] = useState("monthly");
  const [yearsCount, setYearsCount] = useState(6);
  const [months, setMonths] = useState<DateRangeType[]>(fyMonthsRange);

  // Detail modal
  const [detailModalVisible, handleDetailModalVisible] = useState<boolean>(false);

  const columnsMonthly: ProColumns<RecordType>[] = useMemo(() => {
    return [
      {
        title: "Avg",
        dataIndex: `${statsMode}_prev`,
        search: false,
        width: 70,
        align: "right",
        tooltip: "AVG in 12 months in old years + curr.Qty.ofMonth in cur year. (current month is excluded)",
        className: "bl2 b-gray text-sm c-grey",
        render: (__: any, record: any) => {
          const value = sn(record[`${statsMode}_prev`]);
          return nf2(value);
        },
      },
      ...(months.map((month, mInd) => ({
        title: month.title,
        dataIndex: `${statsMode}_${mInd}`,
        search: false,
        width: 75,
        align: "right",
        className: mInd == 0 ? "bl2 b-gray cursor-pointer" : "cursor-pointer",
        render: (__: any, record: any) => {
          const value = sn(record[`${statsMode}_${mInd}`]);
          return nf2(value);
        },
        onCell: (record: RecordType) => {
          return {
            onClick: () => {
              setCurrentRow({ supp_supplier_id: sn(record.id), ym: month.from?.substring(0, 7) });
              handleDetailModalVisible(true);
            },
          };
        },
      })) as any),
      {
        title: "Total",
        dataIndex: "bal_total",
        search: false,
        width: 85,
        align: "right",
        className: "bl2 b-gray",
        render: (__: any, record: any) => {
          const value = sn(record[`${statsMode}_total`]);
          return nf2(value);
        },
      },
    ];
  }, [months, statsMode]);

  const YEARS = useMemo(() => {
    return Util.dtBuildFyList(Util.dtCurrentFy(), yearsCount, true);
  }, [yearsCount]);

  const columnsYearly: ProColumns<RecordType>[] = useMemo(() => {
    return [
      ...(YEARS.map((x, mInd) => ({
        title: x.label,
        dataIndex: `${statsMode}_${mInd}`,
        search: false,
        width: 75,
        align: "right",
        className: mInd == 0 ? "bl2 b-gray cursor-pointer" : "cursor-pointer",
        render: (__: any, record: any) => {
          const value = sn(record[`${statsMode}_${mInd}`]);
          return nf2(value);
        },
        onCell: (record: RecordType) => {
          return {
            onClick: () => {
              setCurrentRow({ supp_supplier_id: sn(record.id), y: sn(x.value) });
              handleDetailModalVisible(true);
            },
          };
        },
      })) as any),
      {
        title: "Total",
        dataIndex: `${statsMode}_total`,
        search: false,
        width: 85,
        align: "right",
        className: "bl2 b-gray",
        render: (__: any, record: any) => {
          const value = sn(record[`${statsMode}_total`]);
          return nf2(value);
        },
      },
    ];
  }, [YEARS, statsMode]);

  const columns: ProColumns<RecordType>[] = useMemo(
    () => [
      /* {
        title: "OrgA",
        dataIndex: ["org_a"], // org_supplier
        width: 50,
        tooltip: "OrgA in WHC_Org",
        align: "center",
      }, */
      {
        title: "Name",
        dataIndex: "name",
        width: 200,
        showSorterTooltip: false,
        render(__, entity) {
          const supplier = entity.supp_supplier;

          return (
            <Row wrap={false} gutter={4}>
              <Col flex="auto">
                {supplier ? (
                  <Typography.Link
                    copyable={false}
                    href={`${PUBLIC_PATH}suppliers/detail/${supplier.id}`}
                    target="_blank"
                    title={`WHC_Task: ${entity.name || ""}`}
                  >
                    {supplier.name || " - "}
                  </Typography.Link>
                ) : (
                  <Typography.Text copyable={false}>{"-"}</Typography.Text>
                )}
              </Col>
            </Row>
          );
        },
      },
      ...(viewMode == "yearly" ? columnsYearly : columnsMonthly),
      {
        dataIndex: ["org_supplier", "offers"],
        title: "Offers",
        className: "bl2 b-gray",
        width: 300,
        render(dom, entity) {
          return entity.org_supplier?.offers?.length
            ? entity.org_supplier?.offers?.map((x) => {
                const dt = x?.updated_on ?? x?.created_on;
                return (
                  <Row key={x.id} wrap={false} className={x.status == 1 ? "" : "c-grey"}>
                    <Col flex="50px">{x.offer_sid}</Col>
                    <Col flex="2">{x.offer}</Col>
                    {!!dt && (
                      <Col flex="50px" style={{ fontSize: 10 }}>
                        {Util.dtToDMY(dt, dt.includes(`${CURRENT_YEAR}`) ? DT_FORMAT_DM : DT_FORMAT_DMY_SHORT)}
                      </Col>
                    )}
                  </Row>
                );
              })
            : null;
        },
      },
      {
        dataIndex: ["org_supplier", "created_on"],
        title: "Last Success",
        width: 70,
        align: "center",
        render(dom, entity) {
          return entity.org_supplier?.created_on ? Util.dtToDMY(entity.org_supplier?.created_on, DT_FORMAT_DMY_SHORT) : null;
        },
      },
      {
        dataIndex: ["org_supplier", "updated_on"],
        title: "Last Contact",
        width: 70,
        align: "center",
        render(dom, entity) {
          // return entity.org_supplier?.updated_on ? Util.dtToDMY(entity.org_supplier?.updated_on, DT_FORMAT_DMY_SHORT) : null;
          return entity.org_supplier?.updated_on ? dayjs(entity.org_supplier?.updated_on).fromNow() : null;
        },
      },

      {
        dataIndex: ["org_supplier", "created_by_disp_name"],
        title: "Last User",
        width: 40,
        align: "center",
      },

      {
        dataIndex: ["org_supplier", "status_name"],
        title: "Last Status",
        width: 90,
        render(dom, entity) {
          return entity.org_supplier?.status_name ? (
            <Tag style={{ whiteSpace: "break-spaces", lineHeight: 1.1, textAlign: "center" }} bordered={false} color="cyan-inverse">
              {dom}
            </Tag>
          ) : null;
        },
      },
      {
        dataIndex: "top3_comments",
        title: "Latest Comments",
        width: 300,
        render(__, entity, index, action, schema) {
          return entity.org_supplier?.top3_comments?.map((x) => (
            <Row key={x.id} gutter={0} wrap={false} style={{ lineHeight: 1.1, fontSize: 11 }}>
              <Col flex="40px">{Util.dtToDMY(x.created_on, DT_FORMAT_DM)}</Col>
              <Col flex="20px" title={x.creator?.username || ""}>
                ({x.creator?.display_name || "-"})
              </Col>
              <Col flex={"auto"} title={x.comment}>
                <Typography.Text ellipsis style={{ lineHeight: 1.1, fontSize: 11 }}>
                  {x.comment}
                </Typography.Text>
              </Col>
            </Row>
          ));
        },
      },
    ],
    [columnsMonthly, columnsYearly, viewMode],
  );

  useEffect(() => {
    searchFormRef.current?.setFieldValue(
      "org_stats_user_level",
      initialState?.currentUser?.settings?.org_stats_item ?? UserSettingOrgStatsItemEnum.Option3,
    );
  }, [initialState?.currentUser?.settings?.org_stats_item]);

  useEffect(() => {
    const lastSf = Util.getSfValues("supplier_stats", DefaultSearchFormValues);
    if (!lastSf.fy) {
      lastSf.fy = Util.dtCurrentFy();
    }
    setViewMode(lastSf.view_mode ?? "monthly");
    setMonths(Util.dtBuildRangesInFy(+lastSf.fy, false, true));
    setYearsCount(lastSf.years_count ?? 6);
    setStatsMode(lastSf.stats_mode ?? "EK_Summe");
    setSelectedOrgA(lastSf.in_org_a || []);

    searchFormRef.current?.setFieldsValue(lastSf);
    Util.setSfValues(lastSf);
  }, [setSelectedOrgA]);

  useEffect(() => {
    if (orgAList) {
      const formValues = Util.getSfValues("supplier_stats", {});
      if (!("in_org_a" in formValues)) {
        searchFormRef.current?.setFieldValue("in_org_a", orgAList);
        setSelectedOrgA(orgAList);
        actionRef.current?.reload();
      }
    }
  }, [orgAList, setSelectedOrgA]);

  return (
    <PageContainer>
      <Card style={{ marginBottom: 16 }}>
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          submitter={{
            render(props, dom) {
              return <div style={{ marginLeft: "auto" }}>{dom}</div>;
            },
            searchConfig: { submitText: "Search" },
            submitButtonProps: { loading, htmlType: "submit", style: { marginLeft: 8 } },
            onSubmit: () => actionRef.current?.reload(),
            onReset: () => {
              searchFormRef.current?.resetFields();
              actionRef.current?.reload();
            },
          }}
        >
          <ProFormCheckbox.Group
            name="in_org_a"
            label="Org A"
            options={orgAOptions as any}
            fieldProps={{
              onChange(checkedValues) {
                const oldCheckedValues = [...selectedOrgA];
                // const currentSelectedValues = checkedValues.filter((x) => !oldCheckedValues.includes(x));
                const currentRemovedValues = oldCheckedValues.filter((x: string) => !checkedValues.includes(x));

                // when `all` is unchecked
                if (currentRemovedValues.includes("all")) {
                  const newCheckedValues = ["-"];
                  setSelectedOrgA(newCheckedValues);
                } else {
                  setSelectedOrgA(checkedValues);
                }
                actionRef.current?.reload();
              },
            }}
          />

          <ProFormRadio.Group
            options={[
              { value: "Summe", label: "Turnover" },
              { value: "EK_Summe", label: "BP" },
              { value: "Ertrag", label: "Ertrag" },
            ]}
            radioType="button"
            fieldProps={{
              defaultValue: statsMode,
              buttonStyle: "solid",
              onChange(e) {
                setStatsMode(e.target.value);
              },
            }}
          />

          <ProFormRadio.Group
            name="view_mode"
            options={[
              { value: "monthly", label: "Monthly" },
              { value: "yearly", label: "Yearly" },
            ]}
            radioType="button"
            fieldProps={{
              value: viewMode,
              buttonStyle: "solid",
              onChange(e) {
                setViewMode(e.target.value);
                actionRef.current?.reload();
              },
            }}
          />
          {viewMode == "monthly" && (
            <ProFormSelect
              key="year"
              name="fy"
              label="Fiscal Year"
              width={100}
              placeholder="Fiscal year"
              formItemProps={{ style: { marginBottom: 0 } }}
              options={FyListOptions}
              fieldProps={{
                onChange(value, option) {
                  const fy = value ?? Util.dtCurrentFy();
                  const newMonths = Util.dtBuildRangesInFy(+fy, false, true);
                  setMonths(newMonths);
                  actionRef.current?.reload();
                },
              }}
            />
          )}
          {viewMode == "yearly" && (
            <ProFormDigit
              name="years_count"
              label="Last N Year"
              width={100}
              fieldProps={{
                value: yearsCount,
                onChange(value) {
                  setYearsCount(sn(value));
                  actionRef.current?.reload();
                },
              }}
              formItemProps={{ style: { marginBottom: 0 } }}
            />
          )}
          <ProFormSelect
            name="supp_supplier_id"
            label="Supplier"
            width="md"
            showSearch
            allowClear
            request={async (params) => {
              return getSupplierListByPage(params)
                .then((res) => {
                  return res.data?.map((x) => ({ ...x, value: x.id, label: x.name }));
                })
                .catch((err) => {
                  Util.error(err);
                  return [];
                });
            }}
            fieldProps={{
              onChange(value, option) {
                actionRef.current?.reload();
              },
            }}
          />
          {/* <ProFormText name={"keyWords"} label="KeyWords" width={200} placeholder={"Search by Company Name / Contacts / Email"} /> */}

          <ProFormSelect
            name={["org_stats_user_level"]}
            label="Statistic Level"
            options={getUserSettingOrgStatsItemOptionsByLevel(
              initialState?.currentUser?.settings?.org_stats_item ?? UserSettingOrgStatsItemEnum.Option1,
            )}
            width="sm"
            fieldProps={{
              popupMatchSelectWidth: false,
              onChange(value, option) {
                actionRef.current?.reload();
              },
            }}
          />
        </ProForm>
      </Card>

      <ProTable<RecordType, API.PageParams>
        headerTitle={
          <Space size={32}>
            <span>Supplier Statistics</span>
          </Space>
        }
        actionRef={actionRef}
        rowKey="id"
        size="small"
        revalidateOnFocus={false}
        options={{ fullScreen: true }}
        search={false}
        sticky
        scroll={{ x: 800 }}
        pagination={{
          defaultPageSize: sn(Util.getSfValues("supplier_stats_p")?.pageSize ?? DEFAULT_PER_PAGE_PAGINATION),
        }}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues("supplier_stats", searchFormValues);
          Util.setSfValues("supplier_stats_p", params);

          setLoading(true);
          return getSupplierLoInvoiceCheckStatsByPage(
            {
              ...params,
              ...searchFormValues,
              months: months.map((x) => ({ from: x.from?.substring(0, 7) })),
              with: "contacts,address,address.country,ext,ext.user,meta,metaLabel,calls_phone_count,org_supplier.details",
            },
            sort,
            filter,
          )
            .then((res) => {
              setTotalRow(res.summary?.[0]);
              return res as RecordType;
            })
            .finally(() => setLoading(false));
        }}
        summary={(dataParam) => {
          const totalRowLocal: any = totalRow ?? {};

          return (
            <ProTable.Summary fixed="top">
              <ProTable.Summary.Row style={{ fontWeight: "bold" }}>
                {columns
                  .filter((c, ind) => {
                    if (c.hideInTable) return false;
                    return true;
                  })
                  .map((c, index) => {
                    const tmpKey = typeof c.dataIndex === "string" ? c.dataIndex : ((c.dataIndex || []) as any[]).join(".");
                    let value: any = null;
                    let align: any = "left";

                    if (tmpKey == "name") value = "Total";
                    else if (tmpKey != "org_a") {
                      value = nf2(totalRowLocal[tmpKey]);
                      align = "right";
                    }

                    // CSS class
                    const cls = `bg-green1 ${c.className || ""}`;
                    return (
                      <ProTable.Summary.Cell key={tmpKey} index={index} align={align} className={cls}>
                        {value}
                      </ProTable.Summary.Cell>
                    );
                  })}
              </ProTable.Summary.Row>
            </ProTable.Summary>
          );
        }}
        onRequestError={Util.error}
        columns={columns}
        tableAlertRender={false}
        columnEmptyText=""
        locale={{ emptyText: <></> }}
      />

      <SupplierStatisticsDetailListModal
        handleModalVisible={handleDetailModalVisible}
        modalVisible={detailModalVisible}
        searchParams={{ ...currentRow, org_stats_user_level: searchFormRef.current?.getFieldValue("org_stats_user_level") }}
      />
    </PageContainer>
  );
};

export default SupplierStatistics;

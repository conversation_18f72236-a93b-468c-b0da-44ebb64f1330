import React, { useRef, useState } from "react";
import { Modal, Tag } from "antd";
import { ActionType, ProColumns, ProTable } from "@ant-design/pro-components";
import { getSupplierOffers } from "@/services/app/Supplier/supplier";
import Util from "@/util";
import { DEFAULT_PER_PAGE_PAGINATION, DT_FORMAT_DMY_SHORT } from "@/constants";

type RowType = APIOrg.Offer;

interface SupplierOffersModalProps {
  visible: boolean;
  onCancel: () => void;
  suppSupplierId?: number;
  supplierName?: string;
}

const SupplierOffersModal: React.FC<SupplierOffersModalProps> = ({ visible, onCancel, suppSupplierId, supplierName }) => {
  const actionRef = useRef<ActionType>();
  const [loading, setLoading] = useState<boolean>(false);

  const columns: ProColumns<RowType>[] = [
    {
      title: "Offer No",
      dataIndex: "offer_sid",
      width: 100,
      sorter: true,
      render: (dom, entity) => {
        return <Tag color="green">{dom}</Tag>;
        /* return (
          <Typography.Link href={`${PUBLIC_PATH}offers/detail/${entity.offer_sid}`} target="_blank">
            {entity.offer_sid}
          </Typography.Link>
        ); */
      },
    },
    {
      title: "Offer",
      dataIndex: "offer",
      width: 200,
      ellipsis: true,
    },
    {
      title: "Status",
      dataIndex: "status",
      width: 80,
      align: "center",
      render: (__, record) => <Tag color={record.status === 1 ? "green" : "default"}>{record.status === 1 ? "Active" : "Inactive"}</Tag>,
    },
    {
      title: "WE",
      dataIndex: "we",
      width: 80,
      align: "center",
    },
    {
      title: "Brand",
      dataIndex: "brand",
      width: 80,
      align: "center",
      render: (__, record) => (record.brand ? "Y" : ""),
    },
    {
      title: "Warehouse",
      dataIndex: "warehouse",
      width: 100,
      align: "center",
      render: (__, record) => (record.warehouse ? "Y" : ""),
    },
    {
      title: "Created",
      dataIndex: "created_on",
      width: 100,
      align: "center",
      render: (__, record) => (record.created_on ? Util.dtToDMY(record.created_on, DT_FORMAT_DMY_SHORT) : null),
    },
    {
      title: "Updated",
      dataIndex: "updated_on",
      width: 100,
      align: "center",
      render: (__, record) => (record.updated_on ? Util.dtToDMY(record.updated_on, DT_FORMAT_DMY_SHORT) : null),
    },
  ];

  return (
    <Modal title={`All Offers - ${supplierName || "Supplier"}`} open={visible} onCancel={onCancel} width={1000} footer={null} destroyOnClose>
      <ProTable<RowType, API.PageParams>
        actionRef={actionRef}
        rowKey="id"
        size="small"
        revalidateOnFocus={false}
        options={{ density: false, setting: false, reload: false }}
        search={false}
        scroll={{ x: 800 }}
        pagination={{
          defaultPageSize: DEFAULT_PER_PAGE_PAGINATION,
          showSizeChanger: true,
          showQuickJumper: true,
        }}
        request={async (params, sort, filter) => {
          if (!suppSupplierId) {
            return { data: [], success: false, total: 0 };
          }

          setLoading(true);
          return getSupplierOffers({ ...params, supp_supplier_id: suppSupplierId }, sort, filter).finally(() => setLoading(false));
        }}
        onRequestError={Util.error}
        columns={columns}
        columnEmptyText=""
      />
    </Modal>
  );
};

export default SupplierOffersModal;

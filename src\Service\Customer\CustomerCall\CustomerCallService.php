<?php

declare(strict_types=1);

namespace App\Service\Customer\CustomerCall;

use App\Lib\Func;
use App\Lib\FuncModel;
use App\Models\Customer\Customer;
use App\Models\Customer\CustomerCall;
use App\Models\Customer\CustomerContact;
use App\Models\Email\Email;
use App\Service\BaseService;
use App\Service\File\FileService;
use Illuminate\Database\Eloquent\Builder;
use Slim\Container;

final class CustomerCallService extends Base
{
    public function __construct(Container $container)
    {
        parent::__construct($container);
    }

    private function processInputData(&$input)
    {
        // CustomerCall::bindOfferId($input);

        if (key_exists('note', $input)) {
            $input['note'] = BaseService::extractInlineImage($input['note'], 'cust_call');
        }
    }

    private function saveRelations(CustomerCall &$row, &$input)
    {
        // todo later
    }

    /**
     * @throws \Throwable
     * @throws \App\Exception\File
     */
    public function create(array $input): CustomerCall
    {
        $this->processInputData($input);

        $db = $this->getCustomerCallRepository()->getDb();
        try {
            $db->beginTransaction();

            /** @var CustomerCall $row */
            $row = CustomerCall::create($input);
            $this->saveRelations($row, $input);

            $db->commit();
        } catch (\Exception $exception) {
            $db->rollBack();
            throw $exception;
        }

        return $row;
    }

    public function update($id, $input): CustomerCall
    {
        $row = CustomerCall::findOrFail($id);

        $this->processInputData($input);


        $db = $this->getCustomerCallRepository()->getDb();
        try {
            $db->beginTransaction();

            $row->update($input);
            $this->saveRelations($row, $input);

            $db->commit();
        } catch (\Exception $exception) {
            $db->rollBack();
            throw $exception;
        }

        return $row;
    }

    public function getOne(int $id, array $params = []): CustomerCall
    {
        return $this->customerCallRepository->getQueryBuilder()->where('id', $id)->first();
    }

    public function getCustomerCallsByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($perPage < 1) {
            $perPage = self::DEFAULT_PER_PAGE_PAGINATION;
        }

        return $this->customerCallRepository->getCustomerCallsByPage(
            $page,
            $perPage,
            $params
        );
    }

    /**
     * Get Merged list with Emails and Magento quotes info, etc
     *
     * @param int $page
     * @param int $perPage
     * @param array|null $params
     * @return array
     */
    public function getCustomerCallsByPageMerged(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($perPage < 1) {
            $perPage = self::DEFAULT_PER_PAGE_PAGINATION;
        }

        /*$customer_id = $params['customer_id'] ?? null;
        if (!$customer_id) {
            // BaseException::raiseInvalidRequest('Shop Customer ID is required!');
            return [
                'pagination' => [
                    'totalRows' => 0,
                    'totalPages' => 0,
                    'currentPage' => 1,
                    'hasMore' => false,
                ],
                'data' => [],
            ];
        }
        $emails = CustomerContact::query()->where('id', $customer_id)->pluck('email')?->toArray();*/

        // 1. customer call log
        $qbCall = CustomerCall::query();
        $qbCall->select([])
            ->selectRaw("type")
            ->selectRaw("CONCAT('call_', id) AS uid")
            ->selectRaw("id AS id")
            ->selectRaw("direction")
            ->selectRaw("offer_no")
            ->selectRaw("note")
            ->selectRaw("customer_id")
            ->selectRaw("created_on AS date")
            ->selectRaw("NULL AS email_ids")
            ->selectRaw("NULL AS email_list")
            /*->selectSub(
                Customer::query()
                    ->select('customer.name')
                    ->whereColumn('customer.id', 'customer_call.customer_id')
                , 'customer_name'
            )*/
            // ->selectRaw("NULL AS email_open_count")
            // ->selectRaw("NULL AS email_open_first_seen_on")
            // ->selectRaw("NULL AS email_open_seen_updated_on")
        ;

        // 2. Email logs
        $qbEmail = Email::query();
        $qbEmail->select([])
            ->selectRaw(CustomerCall::TYPE_EMAIL . " AS type")
            ->selectRaw("CONCAT('email_', id, '_', email_receiver.email) AS uid")
            ->selectRaw("id AS id")
            ->selectRaw("IF(email.box='SENT', '" . CustomerCall::DIRECTION_OUT . "', '" . CustomerCall::DIRECTION_IN . "') AS direction")
            ->selectRaw("offer_no AS offer_no")
            ->selectRaw("NULL AS note")
            ->selectRaw("customer_id")
            ->selectRaw("LEFT(email.date, 10) AS date")
            ->selectRaw("GROUP_CONCAT(DISTINCT CONCAT(email_receiver.email, ':', email.id)) AS email_ids")
            ->selectRaw("GROUP_CONCAT(email_receiver.email) AS email_list")
            /*->selectSub(
                Customer::query()
                    ->select('customer.name')
                    ->join('customer_contact', 'customer_contact.customer_id', '=', 'customer.id')
                    ->whereColumn('customer_contact.email', 'email_receiver.email')
                , 'customer_name'
            )*/
        ;
        // ->selectRaw("email_receiver.open_count AS email_open_count")
        // ->selectRaw("email_receiver.open_first_seen_on AS email_open_first_seen_on")
        // ->selectRaw("email_receiver.open_seen_updated_on AS email_open_seen_updated_on");


        $qbEmail->join('email_receiver', 'email_receiver.email_id', '=', 'email.id');

        $qbEmail->groupByRaw('LEFT(email.date, 10)');

        // $qbCall->where('customer_id', $customer_id);
        if ($params['offer_no'] ?? null) {
            $qbCall->where('offer_no', $params['offer_no']);
            $qbEmail->where('offer_no', $params['offer_no']);
        }

        if ($params['ft_note'] ?? null) {
            $qbCall->where('note', 'like', FuncModel::likeValue($params['ft_note'], '%'));
            $qbEmail->where('subject', 'like', FuncModel::likeValue($params['ft_note'], '%'));
        }

        if ($params['customer_id'] ?? null) {
            $qbCall->where('customer_id', $params['customer_id']);
            $qbEmail->where('customer_id', $params['customer_id']);
        }

        if ($params['like_customer_name'] ?? null) {
            $qbCall->whereHas('customer', function($builder) use (&$params) {
                $builder->where('name', 'like', FuncModel::likeValue($params['like_customer_name'], '%'));
            });
            $qbEmail->whereHas('receivers', function(Builder $builder) use (&$params) {
               $builder->whereHas('customerContacts', function(Builder $builder) use (&$params) {
                   $builder->whereHas('customer', function(Builder  $builder) use (&$params) {
                       $builder->where('name', 'like', FuncModel::likeValue($params['like_customer_name'], '%'));
                   });
               });
            });
        }

        if ($offerMode = $params['offer_mode'] ?? 'inc') {
            switch ($offerMode) {
                case 'inc':
                    break;
                case 'exc':
                    $qbCall->whereNotIn('type', [CustomerCall::TYPE_OFFER]);
                    break;
                case 'only':
                    $qbCall->whereIn('type', [CustomerCall::TYPE_OFFER]);
                    $qbEmail->whereRaw("0");
                    break;
            }
        }

        // todo
        $union = $qbCall->unionAll($qbEmail);

        $builder = CustomerCall::query()->from($union, 'main');

        $this->customerCallRepository->applyOrderBy($builder, $params);

        $total = $this->customerCallRepository->getCountByQuery($builder);

        $result = $this->customerCallRepository->getResultsWithPagination(
            $builder,
            $page,
            $perPage,
            $total
        );

        if ($total) {
            $emailsKv = [];
            $email2EmailId = [];
            foreach ($result['data'] as &$x) {
                if ($x['email_list'] ?? '') {
                    $arr = Func::csvToArr($x['email_list'] ?? '');
                    $x['email_list'] = $arr;
                    foreach ($arr as $email) {
                        $emailsKv[$email] = true;
                    }
                }

                if ($x['email_ids'] ?? '') {
                    $arr = Func::csvToArr($x['email_ids'] ?? '');
                    $x['email_ids'] = $arr;
                    foreach ($arr as $str) {
                        list($email, $id) = explode(':', $str);
                        $email2EmailId[$email] = $id;
                    }
                }
            }

            if ($emailsKv) {
                $email2Customer = Customer::query()
                    ->select('customer.*')
                    ->addSelect('customer_contact.email')
                    ->join('customer_contact', 'customer_contact.customer_id', '=', 'customer.id')
                    ->whereIn('customer_contact.email', array_keys($emailsKv))
                    ->get()
                    ->keyBy('email')?->toArray();

                foreach ($result['data'] as &$x2) {
                    $arr = $x2['email_list'] ?? [];
                    $tmpCustomersKv = [];
                    foreach ($arr as $email) {
                        $custId = $email2Customer[$email]['id'];
                        $tmpCustomersKv[$custId] = $email2Customer[$email];

                        if (isset($email2EmailId[$email])) {
                            // append email Ids to this customer.
                            if (!isset($tmpCustomersKv[$custId]['email_kv'])) {
                                $tmpCustomersKv[$custId]['email_kv'] = [];
                            }
                            $tmpCustomersKv[$custId]['email_kv'][$email] = $email2EmailId[$email];
                        }
                    }
                    $x2['customers'] = array_values($tmpCustomersKv);
                }
            }
        }

        return $result;
    }

    public function delete($ids): void
    {
        $arr = is_array($ids) ? $ids : explode(',', $ids);

        // delete files first
        /** @var CustomerCall[] $calls */
        $calls = CustomerCall::query()
            ->select('id')
            ->whereIn('id', $arr)
            ->with('files')->get();

        /** @var FileService $fileService */
        $fileService = Func::getContainer()->get('file_service');

        foreach ($calls as &$call) {
            if ($call->files) {
                foreach ($call->files as &$file) {
                    $fileService->removeFile($file);
                }
            }
        }

        $this->customerCallRepository->getQueryBuilder()->whereIn('id', $arr)->delete();
    }
}

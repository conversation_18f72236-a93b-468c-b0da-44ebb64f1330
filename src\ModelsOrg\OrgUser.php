<?php

namespace App\ModelsOrg;


/**
 * @property string $id
 * @property string $username
 * @property string $display_name
 * @property string $password
 * @property boolean $is_superuser
 */
class OrgUser extends BaseModel
{
    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'users';

    /**
     * The "type" of the auto-incrementing ID.
     * 
     * @var string
     */
    protected $keyType = 'string';

    /**
     * Indicates if the IDs are auto-incrementing.
     * 
     * @var bool
     */
    public $incrementing = false;

    /**
     * @var array
     */
    protected $fillable = ['username', 'display_name', 'password', 'is_superuser'];
}

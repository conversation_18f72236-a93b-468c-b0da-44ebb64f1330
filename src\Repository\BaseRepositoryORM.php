<?php

declare(strict_types=1);

namespace App\Repository;

use App\Lib\Func;
use Illuminate\Database\Capsule\Manager;
use \Illuminate\Database\Connection;
use \Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use JetBrains\PhpStorm\ArrayShape;
use Monolog\Logger;


abstract class BaseRepositoryORM
{
    protected Manager $em;

    protected Connection $database;

    protected Logger $logger;

    public function __construct(Manager $em, Logger $logger = null)
    {
        $this->em = $em;
        $this->database = $em->getConnection();
        $this->logger = $logger;
    }

    public function getDb($name = null): Connection
    {
        return $this->em->getConnection($name);
    }

    public function changeDb($name=null) {
        $this->database = $this->em->getConnection($name);
    }

    public abstract function getQueryBuilder(): Builder;

    /**
     * @throws \Exception
     */
    public function beginTransaction()
    {
        $this->database->beginTransaction();
    }

    public function commit()
    {
        $this->database->commit();
    }

    /**
     * @throws \Exception
     */
    public function rollback()
    {
        $this->database->rollBack();
    }

    public function getCount(): int
    {
        return $this->getQueryBuilder()->count();
    }

    /**
     * @param Builder $query
     * @param int $page
     * @param int $perPage
     * @param int $total
     * @return array
     */
    #[ArrayShape(['pagination' => "array", 'data' => "array"])]
    public function getResultsWithPagination(
        Builder|\Illuminate\Database\Query\Builder $query,
        int                                        $page,
        int                                        $perPage,
        int                                        $total,
        bool                                       $returnArray = true
    ): array
    {
        if (ceil($total / $perPage) < $page) {
            $page = ceil($total / $perPage);
        }
        $offset = ($page - 1) * $perPage;

        $query->offset($offset)->limit($perPage);
        $data = $returnArray ? $query->get()->toArray() : $query->get();

        return [
            'pagination' => [
                'totalRows' => $total,
                'totalPages' => ceil($total / $perPage),
                'currentPage' => $page,
                'perPage' => $perPage,
                'hasMore' => $page < ceil($total / $perPage),
            ],
            'data' => $data,
        ];
    }

    public function getCountByQuery(string|Builder|\Illuminate\Database\Query\Builder $query): int
    {
        if ($query instanceof Builder || $query instanceof \Illuminate\Database\Query\Builder)
            return $query->count();
        else
            return $this->database->query()->fromRaw($query)->count();
    }

    public function applyOrderBy(Builder|\Illuminate\Database\Query\Builder $builder, ?array $params): Builder|\Illuminate\Database\Query\Builder
    {
        $sortDetail = $params['sort_detail'] ?? $params['sort'] ?? '';

        if ($sortDetail) {
            if (!is_array($sortDetail))
                $sortDetail = Func::safeJson($sortDetail);
            if ($sortDetail) {
                foreach ($sortDetail as $filed => $dir) {
                    $fieldName = str_replace(',', '.', $filed);
                    $builder->orderBy($fieldName, ($dir == 'descend') ? 'DESC' : 'ASC');
                }
            }
        }
        return $builder;
    }

    protected function getFiltersInParam(?array $params): array
    {
        if ($params && isset($params['filter_detail'])) {
            $filterDetail = Func::safeJson($params['filter_detail']);
            return $filterDetail ?? [];
        }
        return [];
    }

    /**
     * Get a quoted value
     *
     * @param mixed $val
     * @return string|null
     */
    public function safeValue($val): ?string
    {
        return $this->database->getPdo()->quote($val);
    }

    public function getByPk($id, $select = ['*'], $table = null): Model
    {
        if ($table)
            return $this->database->table($table)->find($id, $select);
        return $this->getQueryBuilder()->find($id, $select);
    }
}

<?php

namespace App\Models\Supplier;

use App\Models\BaseModel;
use App\Models\Customer\Customer;
use App\Models\File;
use App\Models\Offer\OfferExt;
use App\Models\Sys\Product;
use App\Models\Sys\Trademark;
use App\Models\User;
use App\ModelsOrg\OrgOffer;
use App\ModelsOrg\OrgSupplier;

/**
 * @property integer $id
 * @property integer $supplier_id
 * @property integer $type
 * @property string $direction
 * @property integer $offer_id
 * @property integer $offer_no
 * @property string $note
 * @property string $comment
 * @property string $created_on
 * @property integer $created_by
 * @property string $updated_on
 * @property integer $updated_by
 *
 * @property Supplier $supplier
 * @property SupplierExt $supplierExt
 * @property OfferExt $offerExt
 *
 * @property File[] $files
 */
class SupplierCall extends BaseModel
{
    public const TYPE_PHONE = 1;
    public const TYPE_NOTE = 2;
    public const TYPE_EMAIL = 3;
    public const TYPE_OFFER = 4;
    public const TYPE_MEETING = 5;

    public const DIRECTION_IN = 'I'; // incoming to WHC
    public const DIRECTION_OUT = 'O';  // outgoing from WHC
    public const DIRECTION_WHC = 'W'; // between WHC staffs

    public const TYPE_LABELS = [
        self::TYPE_PHONE  => 'Phone',
        self::TYPE_NOTE  => 'Note',
    ];

    public $timestamps = true;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'supplier_call';

    /**
     * @var array
     */
    protected $fillable = ['supplier_id', 'type', 'direction', 'offer_id', 'offer_no', 'note', 'comment', 'created_on', 'created_by', 'updated_on', 'updated_by'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function supplier()
    {
        return $this->belongsTo('App\Models\Supplier\Supplier', 'supplier_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function supplierExt()
    {
        return $this->belongsTo('App\Models\Supplier\SupplierExt', 'supplier_id', 'supplier_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function files()
    {
        return $this->belongsToMany('App\Models\File', 'supplier_call_file', 'call_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'created_by', 'user_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function offerExt()
    {
        return $this->belongsTo(OfferExt::class, 'offer_no', 'offer_no');
    }

    /**
     * Org Supplier in WHC_Org
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function orgSupplier()
    {
        return $this->hasOne(OrgSupplier::class, 'supp_supplier_id', 'supplier_id');
    }

    /**
     * Dummy relation to be used in Union query
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function customer()
    {
        return $this->hasOne(Customer::class, 'id', 'customer_id');
    }

    /**
     * Dummy relation to be used in Union query
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function orgOffer()
    {
        return $this->hasOne(OrgOffer::class, 'offer_sid', 'offer_no');
    }

}

<?php

declare(strict_types=1);

namespace App\Controller\Offer\OfferTemplate;

use App\Exception\BaseException;
use App\Lib\Func;
use App\Models\File;
use App\Models\Offer\OfferTemplate;
use App\Models\Offer\OfferTemplateLang;
use App\Service\BaseService;
use Slim\Http\Request;
use Slim\Http\Response;

final class UpdateBulk extends Base
{
    public function __invoke(Request $request, Response $response, array $args): Response
    {
        $input = (array)$request->getParsedBody();
        $datakv = $input['data'] ?? null;
        $offer_no = $input['offer_no'] ?? null;
        if (!$offer_no) {
            BaseException::raiseInvalidRequest('Offer No is required!');
        }

        $uploadedFilesMap = $request->getUploadedFiles();

        /** @var OfferTemplate $offerTemplate */
        $offerTemplate = OfferTemplate::query()->where('offer_no', $offer_no)->first();
        if (!$offerTemplate) {
            $offerTemplate = OfferTemplate::create(['offer_no' => $offer_no, 'title' => 'Template ' . (intval(OfferTemplate::query()->max('id')) + 1)]);
        }

        $langs = [];
        foreach ($datakv as $lang => $langData) {
            if ($lang != 'DE' && $lang != 'EN') continue;

            $x = OfferTemplateLang::getBoundData($langData);

            if (key_exists('body', $langData)) {
                $x['body'] = BaseService::extractInlineImage($langData['body']);
            }

            $x['offer_template_id'] = $offerTemplate->id;
            $x['lang'] = $lang;
            $langs[] = $x;
        }

        $offerTemplate->offerTemplateLangs()->upsert($langs, ['offer_template_id', 'lang']);

        $offerTemplate->load('offerTemplateLangs');


        $uploadedFiles = $uploadedFilesMap['data']['files'] ?? [];
        if ($uploadedFiles) {
            $pathRel = DS . File::CAT_OFFER_TEMPLATE_LANG_FILE . DS . date('Y') . DS . date('m');
            $pathAbs = File::getBasePath(File::CAT_OFFER_TEMPLATE_LANG_FILE) . $pathRel;
            if (!file_exists($pathAbs)) {
                mkdir($pathAbs, 0755, true);
            }
            $fileCategory = File::CAT_OFFER_TEMPLATE_LANG_FILE;

            foreach ($uploadedFiles as &$file) {
                // Uploading file
                $fileType = $file->getClientMediaType();
                $fileRow = [
                    'category' => $fileCategory,
                    'type' => $fileType,
                    'file_name' => Func::getSafeFilePath($file->getClientFilename()),
                    'clean_file_name' => $file->getClientFilename(),
                    'size' => $file->getSize()
                ];
                $fileRow['org_path'] = $fileRow['path'] = Func::pathToUrl($pathRel) . '/' . Func::getSafeFilePath($fileRow['file_name']);
                $file->moveTo($pathAbs . DS . $fileRow['file_name']);

                // Save file data
                /** @var File $fileObj */
                $fileObj = File::create($fileRow);

                $offerTemplate->files()->save($fileObj);
            }
        }

        return $this->jsonResponse($response, 'success', $offerTemplate, 200);
    }
}

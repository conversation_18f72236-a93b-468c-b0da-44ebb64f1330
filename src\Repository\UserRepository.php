<?php

declare(strict_types=1);

namespace App\Repository;

use App\Lib\Func;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;


final class UserRepository extends BaseRepositoryORM
{
    public const USER_SELECTS = [
        // 1 => 'user_id, role, username, email, status, name, password, nonce',
        1 => ['user_id', 'initials', 'role', 'username', 'email', 'status', 'name', 'password', 'nonce', 'settings'],
    ];

    public function getUser(int $userId): array
    {
        $user = $this->getByPk($userId);
        if (!$user) {
            throw new \App\Exception\User('User not found.', 404);
        }

        return $user->toArray();
    }

    public static function hasRole($user, int $roleId): bool
    {
        if (!$user) return false;
        if (is_array($user)) {
            if (!($user['status'] ?? null)) return false;
            $userRole = Func::safeInt($user['role'] ?? 0);
        } else {
            $userRole = Func::safeInt($user);
        }

        if ($userRole && $userRole === $roleId) return true;

        return ($userRole & $roleId) > 0;
    }

    public static function isAdmin($user): bool
    {
        return self::hasRole($user, User::ROLE_ADMIN);
    }

    public static function isAdminByRequestParam($params = []): bool
    {
        return $params && self::isAdmin($params['user'] ?? []);
    }

    public function getUserByUsername(string $username, int $selectType): array
    {
        $builder = User::query()->where('username', $username)
            ->select(self::USER_SELECTS[(int)$selectType] ?? '*');
        if ($selectType == 1) {
            $builder->addSelect('password');

        }
        $user = $builder->firstOrFail();

        if ($selectType == 1) {
            $user->makeVisible('password');
        }
        return $user->toArray();
    }

    public function getUserByEmail(string $email, int $selectType): array
    {
        $builder = User::query()->where('email', $email)
            ->select(self::USER_SELECTS[(int)$selectType] ?? '*');

        $user = $builder->firstOrFail();

        if ($selectType == 1) {
            $user->makeVisible('password');
        }
        return $user->toArray();
    }

    public function checkUserByEmail(string $email): void
    {
        // $user = $this->getByField('email', $email);
        $exists = User::query()->where('email', $email)->count() > 0;
        if ($exists) {
            throw new \App\Exception\User('Email already exists.', 400);
        }
    }

    public function checkUserByField(string $fieldName, mixed $fieldValue): void
    {
        // $user = $this->getByField($fieldName, $fieldValue);
        $exists = User::query()->where($fieldName, $fieldValue)->count() > 0;
        if (!$exists) {
            throw new \App\Exception\User('User does not exists.', 400);
        }
    }

    public function getUsersByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        $query = $this->getQueryUsersByPage($params ?? []);
        $total = $this->getCountByQuery($query);

        $result = $this->getResultsWithPagination(
            $query,
            $page,
            $perPage,
            $total
        );

        return $result;
    }

    public function getQueryUsersByPage($params = []): Builder
    {
        $builder = $this->getQueryBuilder();

        if (!empty($params)) {
            if (isset($params['keyword']) && ($keyword = $params['keyword'])) {
                $params['search'] = $keyword;
            }

            if (isset($params['search']) && ($search = $params['search'])) {
                $builder->where(function (Builder $builder) use ($search) {
                    $builder->where('name', 'LIKE', "%{$search}%")
                        ->orWhere('email', 'LIKE', "%{$search}%");
                });
            }

            if (isset($params['user_id'])) {
                $builder->where('user_id', $params['user_id']);
            }
            if (isset($params['name'])) {
                $builder->where('name', 'LIKE', "%{$params['name']}%");
            }

            if (isset($params['email'])) {
                $builder->where('email', 'LIKE', "%{$params['email']}%");
            }

            if (key_exists('status', $params)) {
                $status = intval($params['status'] ?? 0);
                $builder->where('status', $status);
            }

            if (key_exists('role', $params)) {
                $role = intval($params['role'] ?? 0);
                $builder->where('role', $role);
            }

            // filters
            $extraFilters = $this->getFiltersInParam($params);
            if ($extraFilters) {
                foreach ($extraFilters as $field => $arr) {
                    if ($field && $arr && is_array($arr))
                        $builder->whereIn($field, $arr);
                }
            }

            $this->applyOrderBy($builder, $params);
        }

        return $builder;
    }

    public function getAll(): array
    {
        return User::query()->select(['user_id', 'name', 'email'])->orderBy('id')->get()->toArray();
    }

    public function create(array $user): array
    {
        // $lastId = $this->insert($user, true, false, true);
        // return $this->getUser((int)$lastId);
        $newUser = User::create($user);
        return $newUser->toArray();
    }

    public function update($input, $userId): array
    {
        /** @var User $user */
        $user = User::findOrFail($userId);

        if (key_exists('settings', $input)) {
            $user->settings = array_replace_recursive($user->settings ?? [], $input['settings']);
        }

        $user->update($input);

        return $user->toArray();
    }

    public function makeRandomString($bits = 16)
    {
        // return bin2hex(openssl_random_pseudo_bytes(16));
        return Func::randomPassword(16);
    }

    public function getNonce(): string
    {
        return $this->makeRandomString();
    }

    public function updateOneTimeNonce($userId): string
    {
        $nonce = $this->getNonce();
        $result = $this->update(['nonce' => $nonce], $userId);
        if ($result)
            return $nonce;
        else
            throw new \App\Exception\User('Failed to update nonce.', 500);

    }

    public function getQueryBuilder(): Builder
    {
        return User::query();
    }
}

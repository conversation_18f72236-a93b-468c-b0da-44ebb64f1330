import Util, { sn } from "@/util";
import { ActionType, ProColumns, ProForm, ProFormInstance, ProFormText, ProTable } from "@ant-design/pro-components";
import { <PERSON>ton, Card, Modal, Space } from "antd";
import { Dispatch, SetStateAction, useEffect, useRef, useState } from "react";
import { getCustomerListByPage } from "@/services/app/Customer/customer";

export type CustomerSelectModalSearchParamsType = {
  byOfferNo?: number | string;
};

export type SearchFormValueType = Partial<API.Customer> & API.PageParams;

type RowType = API.Customer;

type CustomerSelectModalProps = {
  searchParams?: CustomerSelectModalSearchParamsType;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  addCustomer: (customerIds: number[], mode: "add" | "remove") => Promise<void>;
};

const CustomerSelectModal: React.FC<CustomerSelectModalProps> = (props) => {
  const { searchParams, modalVisible, handleModalVisible, addCustomer } = props;

  const searchFormRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();

  const [loading, setLoading] = useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<RowType>();
  const [selectedRows, setSelectedRows] = useState<RowType[]>([]);

  const columns: ProColumns<RowType>[] = [
    {
      title: "Customer",
      dataIndex: ["name"],
      width: 170,
      showSorterTooltip: false,
      copyable: true,
    },
    {
      title: "OrgA",
      dataIndex: "org_a",
      width: 50,
      align: "center",
    },
    {
      title: "Rel.",
      dataIndex: "relevance",
      width: 50,
      showSorterTooltip: false,
      align: "center",
      className: "p-0",
      render(__, entity) {
        const relevance = entity.ext?.relevance;
        return `${relevance ?? ""}` ? Util.numTo2Digits(relevance) : "";
      },
    },
    {
      title: "",
      valueType: "option",
      width: 80,
      render: (_, record) => {
        const exists = record.existsOfferNoStatus;
        return (
          <Button
            type={exists ? "default" : "primary"}
            onClick={() => {
              addCustomer([sn(record.id)], exists ? "remove" : "add").then(() => {
                actionRef.current?.reload();
              });
            }}
          >
            {exists ? "Deselect" : "Select"}
          </Button>
        );
      },
    },
  ];

  useEffect(() => {
    if (modalVisible && searchParams?.byOfferNo) {
      actionRef.current?.reload();
    }
  }, [modalVisible, searchParams?.byOfferNo]);

  return (
    <Modal
      title={<>Customers List</>}
      open={modalVisible}
      onCancel={() => handleModalVisible(false)}
      width="900px"
      footer={false}
      styles={{ body: { paddingTop: 0 } }}
    >
      <Card variant="borderless">
        <ProForm<SearchFormValueType>
          layout="inline"
          formRef={searchFormRef}
          isKeyPressSubmit
          className="search-form"
          submitter={{
            searchConfig: { submitText: "Search" },
            submitButtonProps: { htmlType: "submit" },
            onSubmit: () => actionRef.current?.reload(),
            onReset: () => {
              searchFormRef.current?.resetFields();
              actionRef.current?.reload();
            },
          }}
        >
          <ProFormText name={"keyWords"} label="KeyWords" width={140} placeholder={"Email / Name / Contact Names"} />
        </ProForm>
      </Card>

      <ProTable<RowType, API.PageParams>
        headerTitle={false}
        toolBarRender={() => []}
        actionRef={actionRef}
        rowKey="id"
        size="small"
        revalidateOnFocus={false}
        options={false}
        search={false}
        sticky
        scroll={{ x: 800 }}
        pagination={{
          defaultPageSize: 20,
        }}
        request={async (params, sort, filter) => {
          const searchFormValues = searchFormRef.current?.getFieldsValue();
          Util.setSfValues("cu_sf_customer_m", searchFormValues);
          Util.setSfValues("cu_sf_customer_m_p", params);

          setLoading(true);
          return getCustomerListByPage(
            {
              ...searchFormValues,
              ...params,
              byOfferNo: searchParams?.byOfferNo,
              with: "existsOfferNoStatus",
            },
            sort,
            filter,
          )
            .then((res) => {
              if (currentRow?.id) {
                setCurrentRow(res.data.find((x) => x.id == currentRow.id));
              }
              return res;
            })
            .finally(() => setLoading(false));
        }}
        /* rowSelection={{
          onChange: (_, selectedRows) => {
            setSelectedRows(selectedRows);
          },
        }} */
        onRequestError={Util.error}
        columns={columns}
        columnEmptyText=""
        tableAlertOptionRender={false}
      />
    </Modal>
  );
};

export default CustomerSelectModal;

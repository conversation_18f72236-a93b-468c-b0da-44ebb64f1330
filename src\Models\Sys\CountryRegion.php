<?php

namespace App\Models\Sys;

use App\Models\BaseModel;
use App\Models\Sys\Country;

/**
 * @property integer $id
 * @property string $country_code
 * @property string $code
 * @property string $default_name
 * @property Country $country
 */
class CountryRegion extends BaseModel
{
    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'sys_country_region';

    /**
     * @var array
     */
    protected $fillable = ['country_code', 'code', 'default_name'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function country()
    {
        return $this->belongsTo('App\Models\Sys\Country', 'country_code', 'code');
    }
}

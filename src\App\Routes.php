<?php

declare(strict_types=1);

use App\Controller\Email\Email;
use App\Controller\Email\EmailAccount;
use App\Controller\Email\EmailServer;
use App\Controller\Email\EmailTemplate;
use App\Controller\Email\EmailTemplateCategory;
use App\Controller\File;
use App\Controller\Supplier\Supplier;
use App\Controller\Customer\Customer;
use App\Controller\Supplier\SupplierAddress;
use App\Controller\Supplier\SupplierCall;
use App\Controller\Supplier\SupplierExt;
use App\Controller\Supplier\SupplierContact;
use App\Controller\Customer\CustomerAddress;
use App\Controller\Customer\CustomerContact;
use App\Controller\Customer\CustomerExt;
use App\Controller\Customer\CustomerCall;
use App\Controller\Offer\OfferTemplate;
use App\Controller\Offer\OfferTemplateLang;
use App\Controller\Offer\OfferExt;
use App\Controller\Offer\OfferBlog;
use App\Controller\Offer\OfferNewsletter;
use App\Controller\Sys\Country;
use App\Controller\Sys\CountryRegion;
use App\Controller\Sys\Product;
use App\Controller\Sys\ProductCategory;
use App\Controller\Sys\Trademark;
use App\Controller\Sys\Dict;
use App\Controller\Sys\SysTextModule;
use App\Controller\Sys\SysLog;
use App\Controller\User;
use App\Controller\User\Login;
use App\Middleware\AdminAuth;
use App\Middleware\Auth;

/** @var \Slim\App $app */

$app->get('/status', 'App\Controller\DefaultController:getStatus');


/** @var \Slim\Container $container */
$container = $app->getContainer();

$app->group('/api', function () use ($app): void {
    $app->post('/user/login', Login::class);
    $app->get('/currentUser', User\GetCurrentUser::class)->add(new Auth($app->getContainer()));

    $app->group('/users-list', function () use ($app): void {
        $app->get('', User\GetAll::class)->add(new Auth($app->getContainer()));
        $app->post('', User\Create::class)->add(new AdminAuth($app->getContainer()));
        $app->delete('/{id}', User\Delete::class)->add(new AdminAuth($app->getContainer()));
    });

    $app->group('/users', function () use ($app): void {
        $app->get('/{id}', User\GetOne::class);
        $app->get('/profile/{address}', User\GetProfile::class);
        $app->put('/{id:[0-9]+}', User\Update::class);
        $app->put('/{id:[0-9]+}/partial', User\UpdatePartial::class);
        $app->put('/{id:[0-9]+}/change-password', User\UpdatePassword::class);
    })->add(new Auth($app->getContainer()));

    $app->group('/basic-data', function () use ($app): void {

    })->add(new Auth($app->getContainer()));

    $app->group('/customer', function () use (&$app): void {
        $app->group('/customer', function () use ($app): void {
            $app->get('', Customer\GetAll::class);
            $app->post('', Customer\Create::class);
            $app->put('/{id}/update', Customer\Update::class);
            // $app->put('/assignCustomersToOffer', Customer\AssignCustomersToOffer::class);
            $app->delete('/{id}', Customer\Delete::class);
        });
        $app->group('/customer-address', function () use ($app): void {
            $app->get('', CustomerAddress\GetAll::class);
            $app->get('/{id:[0-9]+}', CustomerAddress\GetAll::class);
            $app->post('', CustomerAddress\Create::class);
            $app->put('/{id:[0-9]+}', CustomerAddress\Update::class);
            $app->delete('/{id}', CustomerAddress\Delete::class);
        });
        $app->group('/customer-contact', function () use ($app): void {
            $app->get('', CustomerContact\GetAll::class);
        });
        $app->group('/customer-ext', function () use ($app): void {
            $app->get('', CustomerExt\GetAll::class);
            $app->get('/{id}', CustomerExt\GetAll::class);

            $app->put('/{id}/update', CustomerExt\Update::class);
            $app->put('/{id}/customer-id', CustomerExt\UpdateByCustomerId::class);

            $app->post('', CustomerExt\Create::class);
        });

        $app->group('/customer-call', function () use ($app): void {
            $app->get('', CustomerCall\GetAll::class);
            $app->get('/{id}', CustomerCall\GetAll::class);
            $app->post('', CustomerCall\Create::class);
            $app->post('/{id:[0-9]+}', CustomerCall\Update::class);
            $app->delete('/{id}', CustomerCall\Delete::class);
        });
    })->add(new Auth($app->getContainer()));

    $app->group('/offer', function () use (&$app): void {
        $app->get('/org-offer/{offer_no}', \App\Controller\Offer\Offer\GetOrgOffer::class);
        $app->get('/org-offer-list', \App\Controller\Offer\Offer\GetOrgOfferList::class);

        $app->group('/template', function () use ($app): void {
            $app->get('', OfferTemplate\GetAll::class);
            $app->get('/{id:[0-9]+}', OfferTemplate\GetAll::class);
            $app->post('', OfferTemplate\Create::class);
            $app->post('/bulk', OfferTemplate\UpdateBulk::class);
            $app->delete('/{id}', OfferTemplate\Delete::class);
        });

        $app->group('/template-lang', function () use ($app): void {
            $app->get('', OfferTemplateLang\GetAll::class);
            $app->get('/{id:[0-9]+}', OfferTemplateLang\GetAll::class);

            $app->post('', OfferTemplateLang\Create::class);
            // $app->post('/upload-file', OfferTemplateLang\UploadFile::class);
            $app->post('/{id:[0-9]+}', OfferTemplateLang\Update::class);
            $app->delete('/{id}', OfferTemplateLang\Delete::class);
        });

        $app->group('/offer-ext', function () use ($app): void {
            $app->get('', OfferExt\GetAll::class);
            $app->get('/{id:[0-9]+}', OfferExt\GetAll::class);
            $app->post('', OfferExt\Create::class);
            $app->post('/updateOrCreate', OfferExt\UpdateOrCreate::class);
            $app->put('/{id}', OfferExt\Update::class);
            $app->delete('/{id}', OfferExt\Delete::class);
        });

        $app->group('/blog', function () use ($app): void {
            $app->get('', OfferBlog\GetAll::class);
            $app->get('/{id:[0-9]+}', OfferBlog\GetAll::class);
            $app->post('', OfferBlog\Create::class);
            $app->post('/updateOrCreate', OfferBlog\UpdateOrCreate::class);
            $app->put('/copyFileAndResize', OfferBlog\CopyFileAndResize::class);
            $app->delete('/{id}', OfferBlog\Delete::class);
        });
        $app->group('/newsletter', function () use ($app): void {
            $app->get('', OfferNewsletter\GetAll::class);
            $app->get('/{id:[0-9]+}', OfferNewsletter\GetAll::class);
            $app->post('', OfferNewsletter\Create::class);
            $app->post('/updateOrCreate', OfferNewsletter\UpdateOrCreate::class);
            $app->post('/usToWhcCust', OfferNewsletter\UsToWhcCust::class);
            $app->put('/copyFileAndResize', OfferNewsletter\CopyFileAndResize::class);
            $app->delete('/{id}', OfferNewsletter\Delete::class);
        });
    })->add(new Auth($app->getContainer()));

    $app->group('/supplier', function () use (&$app): void {
        $app->group('/supplier-call', function () use ($app): void {
            $app->get('', SupplierCall\GetAll::class);
            $app->get('/{id:[0-9]+}', SupplierCall\GetAll::class);

            $app->post('', SupplierCall\Create::class);
            // $app->post('/upload-file', SupplierCall\UploadFile::class);
            $app->post('/{id:[0-9]+}', SupplierCall\Update::class);

            $app->delete('/{id}', SupplierCall\Delete::class);

            // For org DB
            $app->get('/getOrgOfferCommentAll', SupplierCall\GetOrgOfferCommentAll::class);
        });

        $app->group('/supplier-ext', function () use ($app): void {
            $app->get('', SupplierExt\GetAll::class);
            $app->get('/{id}', SupplierExt\GetAll::class);

            $app->put('/{id}/update', SupplierExt\Update::class);
            $app->put('/{id}/supplier-id', SupplierExt\UpdateBySupplierId::class);

            $app->post('', SupplierExt\Create::class);
        });

        $app->group('/supplier', function () use ($app): void {
            $app->get('', Supplier\GetAll::class);
            $app->get('/email-matrix', Supplier\GetSupplier2EmailMatrix::class);
            $app->get('/getOrgAList', Supplier\GetOrgAListInOrg::class);
            $app->get('/getOrgInvoiceCheckStats', Supplier\GetTaskInvoiceCheckStats::class);
            $app->get('/getOrgInvoiceCheckStatsDetailsList', Supplier\GetTaskInvoiceCheckStatsDetailsList::class);
            $app->get('/getSupplierOffers', Supplier\GetSupplierOffers::class);
            // $app->get('/getSalesAreaACList', Supplier\GetSalesAreaACList::class);
            // $app->get('/getSupplierLangACList', Supplier\GetSupplierLangACList::class);

            $app->post('', Supplier\Create::class);

            $app->put('/{id}/update', Supplier\Update::class);
            $app->put('/{id}/meta', Supplier\UpdateMeta::class);
            $app->put('/dsSupplier', Supplier\DsSupplier::class);

            $app->delete('/{id}', Supplier\Delete::class);
        });

        $app->group('/supplier-address', function () use ($app): void {
            $app->get('', SupplierAddress\GetAll::class);
            $app->get('/{id:[0-9]+}', SupplierAddress\GetAll::class);
            $app->post('', SupplierAddress\Create::class);
            $app->put('/{id:[0-9]+}', SupplierAddress\Update::class);
            $app->delete('/{id}', SupplierAddress\Delete::class);
        });

        $app->group('/supplier-contact', function () use ($app): void {
            $app->get('', SupplierContact\GetAll::class);
        });
    })->add(new Auth($app->getContainer()));

    $app->group('/email', function () use ($app): void {
        $app->get('', Email\GetAll::class);
        $app->get('/{id:[0-9]+}', Email\GetOne::class);

        $app->get('/nextEmail', Email\GetNextEmail::class);
        $app->get('/reasonableSenders', Email\GetReasonableSenders::class);
        $app->get('/{id:[0-9]+}/getTrackingList', Email\GetTrackingList::class);

        $app->post('', Email\Create::class);
        $app->post('/ds', Email\DsPull::class);
        $app->post('/send', Email\Send::class);

        $app->put('/{id:[0-9]+}', Email\Update::class);

        $app->delete('/{id}', Email\Delete::class);
    })->add(new Auth($app->getContainer()));

    $app->group('/email-account', function () use ($app): void {
        $app->get('', EmailAccount\GetAll::class);
        $app->get('/{id}', EmailAccount\GetAll::class);
        $app->post('', EmailAccount\Create::class);
        $app->put('/{id:[0-9]+}', EmailAccount\Update::class);
        $app->delete('/{id}', EmailAccount\Delete::class);
    })->add(new Auth($app->getContainer()));

    $app->group('/email-server', function () use ($app): void {
        $app->get('', EmailServer\GetAll::class);
        $app->get('/{id}', EmailServer\GetAll::class);
        $app->post('', EmailServer\Create::class);
        $app->put('/{id:[0-9]+}', EmailServer\Update::class);
        $app->delete('/{id}', EmailServer\Delete::class);
    })->add(new Auth($app->getContainer()));

    $app->group('/email-template', function () use ($app): void {
        $app->get('', EmailTemplate\GetAll::class);
        $app->get('/{id:[0-9]+}', EmailTemplate\GetAll::class);
        $app->get('/getACList', EmailTemplate\GetACList::class);

        $app->post('', EmailTemplate\Create::class);
        $app->put('/{id}', EmailTemplate\Update::class);
        $app->delete('/{id}', EmailTemplate\Delete::class);
    })->add(new Auth($app->getContainer()));

    $app->group('/email-template-category', function () use ($app): void {
        $app->get('', EmailTemplateCategory\GetAll::class);
        $app->get('/getCat1ACList', EmailTemplateCategory\GetCat1ACList::class);

        $app->post('', EmailTemplateCategory\Create::class);
        $app->put('/{id:[0-9]+}', EmailTemplateCategory\Update::class);
        $app->delete('/{id}', EmailTemplateCategory\Delete::class);
    })->add(new Auth($app->getContainer()));

    $app->group('/country', function () use ($app): void {
        $app->get('', Country\GetAll::class);
        $app->get('/{id}', Country\GetAll::class);
        $app->post('', Country\Create::class);
        $app->put('/{id}', Country\Update::class);
        $app->delete('/{id}', Country\Delete::class);
    })->add(new Auth($app->getContainer()));

    $app->group('/country-region', function () use ($app): void {
        $app->get('', CountryRegion\GetAll::class);
        $app->get('/{id}', CountryRegion\GetAll::class);
        $app->post('', CountryRegion\Create::class);
        $app->put('/{id}', CountryRegion\Update::class);
        $app->delete('/{id}', CountryRegion\Delete::class);
    })->add(new Auth($app->getContainer()));

    $app->get('/countries', \App\Controller\DefaultController::class . ':getCountries')->add(new Auth($app->getContainer()));
    $app->get('/app-settings', \App\Controller\GetAppSettings::class)->add(new Auth($app->getContainer()));

    // DB meatadata
    $app->get('/db/tables', \App\Controller\DefaultController::class . ':getDBTables')->add(new Auth($app->getContainer()));
    $app->get('/db/mappable-tables', \App\Controller\DefaultController::class . ':getMappableDBTables')->add(new Auth($app->getContainer()));
    $app->get('/db/fields', \App\Controller\DefaultController::class . ':getDBTableFields')->add(new Auth($app->getContainer()));

    $app->group('/file', function () use ($app): void {
        $app->get('/{id:[0-9]+}', File\GetOne::class);
        $app->delete('/{id:[0-9]+}', File\Delete::class);
    })->add(new Auth($app->getContainer()));

    // download by file table.
    $app->get('/file/download', File\DownloadFile::class);
    // download by sub path.
    $app->get('/download', \App\Controller\DefaultController::class . ':downloadFile');

    // System routes
    $app->group('/sys', function () use ($app): void {
        $app->group('/dict', function () use ($app): void {
            $app->get('', Dict\GetAll::class);
            $app->put('', Dict\Update::class);
            $app->post('', Dict\Create::class);
        })->add(new Auth($app->getContainer()));

        $app->group('/text-module', function () use ($app): void {
            $app->get('', SysTextModule\GetAll::class);
            $app->get('/{id}', SysTextModule\GetOne::class);
            $app->post('', SysTextModule\Create::class);
            $app->put('/{id}', SysTextModule\Update::class);
            $app->delete('/{id}', SysTextModule\Delete::class);
        })->add(new Auth($app->getContainer()));

        $app->get('/file-log', \App\Controller\DefaultController::class . ':getLastLog')
            ->add(new AdminAuth($app->getContainer()));

        $app->group('/sys-log', function () use ($app): void {
            $app->get('', SysLog\GetAll::class);
            $app->post('', SysLog\Create::class);
            $app->delete('/{id}', SysLog\Delete::class);

            // down syncing email tracking history from Media Server
            $app->put('/dsEmailTrackingData', SysLog\DsEmailTrackingData::class);
        })->add(new Auth($app->getContainer()));

        $app->group('/product-category', function () use ($app): void {
            $app->get('', ProductCategory\GetAll::class);
            $app->post('', ProductCategory\Create::class);
            $app->put('/{id:[0-9]+}', ProductCategory\Update::class);
            $app->delete('/{id}', ProductCategory\Delete::class);
        })->add(new Auth($app->getContainer()));

        /*$app->group('/product', function () use ($app): void {
            $app->get('', Product\GetAll::class);
            $app->post('', Product\Create::class);
            $app->put('/{id:[0-9]+}', Product\Update::class);
            $app->delete('/{id}', Product\Delete::class);
        })->add(new Auth($app->getContainer()));

        $app->group('/trademark', function () use ($app): void {
            $app->get('', Trademark\GetAll::class);
            $app->post('', Trademark\Create::class);
            $app->put('/{id:[0-9]+}', Trademark\Update::class);
            $app->delete('/{id}', Trademark\Delete::class);
        })->add(new Auth($app->getContainer()));*/
    });
})->add(new \App\Middleware\SlimRequestLogger($container));

// Catch-all route to serve a 404 Not Found page if none of the routes match
// NOTE: make sure this route is defined last
$app->map(['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'], '/{routes:.+}', function ($req, $res) {
    $handler = $this->notFoundHandler; // handle using the default Slim page not found handler
    return $handler($req, $res);
});

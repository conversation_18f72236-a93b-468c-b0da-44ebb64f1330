<?php

declare(strict_types=1);

namespace App\Service\Customer\CustomerContact;

use App\Service\BaseService;
use App\Repository\Customer\CustomerContactRepository;
use Slim\Container;

abstract class Base extends BaseService
{
    private const REDIS_KEY = 'Customer:%s';
    protected CustomerContactRepository $customerContactRepository;

    public function __construct(Container $container)
    {
        $this->customerContactRepository = $container->get(CustomerContactRepository::class);
    }

    public function getCustomerContactRepository()
    {
        return $this->customerContactRepository;
    }
}


<?php

declare(strict_types=1);

namespace App\Controller;

use App\Exception\File;
use App\Lib\SysMsg;
use App\Models\User;
use App\Repository\UserRepository;
use Monolog\Logger;
use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\UploadedFileInterface;
use Slim\Container;
use Slim\Http\Body;
use Slim\Http\Response;
use Slim\Views\PhpRenderer;

abstract class BaseController
{
    protected Container $container;

    protected Logger $logger;

    public PhpRenderer $view;

    public UserRepository $userRepository;

    /**
     * @var UploadedFileInterface[] $uploadedFiles
     */
    public ?array $uploadedFiles;

    public function __construct(Container $container)
    {
        $this->container = $container;
        $this->logger = $container->get('logger');
        $container->get('dbORM');   // we initialize for static access without injecting repositories
        $this->view = $container->get('view');
        $this->userRepository = $container->get(UserRepository::class);
    }

    /**
     * @param array|object|null | boolean $data
     */
    protected function jsonResponse(
        Response $response,
        string   $status,
                 $data,
        int      $code
    ): Response
    {
        $data = [
            'code' => $code,
            'status' => $status,
            'message' => $data,
        ];
        $messages = SysMsg::get_instance()->get_msg_list();
        if ($messages) {
            $data['messageFlash'] = $messages;
        }
        return $response->withJson($data, $code, JSON_PRETTY_PRINT | JSON_INVALID_UTF8_SUBSTITUTE);
    }

    /**
     * @param array|object|null | boolean $data
     */
    protected function xmlResponse(Response $response, int $code, $xmlData): Response
    {
        $body = new Body(fopen('php://temp', 'r+'));
        $response = $response->withBody($body);
        $body->write($xmlData);

        return $response->withHeader('Content-Type', 'text/xml');
    }

    protected static function isRedisEnabled(): bool
    {
        return filter_var($_SERVER['REDIS_ENABLED'], FILTER_VALIDATE_BOOLEAN);
    }

    protected function getUserFromToken($request): array
    {
        $object = (array)$request->getParsedBody();
        return $object['user'] ?? [];
    }

    protected function isAdminUser($request): bool
    {
        $object = (array)$request->getParsedBody();
        return $object && UserRepository::hasRole($object['user'] ?? null, User::ROLE_ADMIN);
    }

    protected function isLoggedIn($request): bool
    {
        $object = (array)$request->getParsedBody();
        return $object && !!($object['user']['user_id'] ?? null);
    }

    protected function getUserAddressFromRequest($request): string
    {
        $object = (array)$request->getParsedBody();
        return $object['user']['address'] ?? '';
    }

    protected function getCurrentUserId($request)
    {
        $object = (array)$request->getParsedBody();
        return $object['user']['user_id'] ?? null;
    }

    /**
     * @throws File
     */
    public function validateUploadedFiles(RequestInterface &$request, $key='files')
    {
        $this->uploadedFiles = $request->getUploadedFiles()[$key] ?? null;
        if ($this->uploadedFiles) {
            foreach ($this->uploadedFiles as $x) {
                if ($x->getError() !== UPLOAD_ERR_OK) {
                    throw new \App\Exception\File('Upload Error! Code: ' . $x->getError(), 500);
                }
            }
        }

        return $this->uploadedFiles;
    }
}

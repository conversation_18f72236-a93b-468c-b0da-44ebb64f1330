<?php

declare(strict_types=1);

namespace App\Controller\File;

use App\Controller\BaseController;
use App\Lib\FileLib;
use App\Service\File\FileService;
use Intervention\Image\ImageManager;
use League\Flysystem\Local;
use League\Flysystem\Filesystem;
use League\Flysystem\UnixVisibility\PortableVisibilityConverter;
use Slim\Container;


abstract class Base extends BaseController
{
    protected FileService $fileService;

    protected Filesystem $fs;

    protected ImageManager $im;

    protected FileLib $fileLib;

    protected string $baseImagePath;

    public function __construct(Container $container)
    {
        parent::__construct($container);
        $this->fileService = $this->getFileService();
        $this->baseImagePath = $this->getUploadDir();
        $this->fs = $this->getFilesystem($this->baseImagePath);
        $this->im = $this->getImageManager();
        $this->fileLib = new FileLib($this->baseImagePath);
    }

    protected function getFileService(): FileService
    {
        return $this->container->get('file_service');
    }

    protected function getUploadDir(): string
    {
        return $this->container->get('settings')['upload_dir'];
    }

    public function getFilesystem($basePath)
    {
        return new Filesystem(
            new Local\LocalFilesystemAdapter($basePath, PortableVisibilityConverter::fromArray([
                'file' => [
                    'public' => 0644,
                    'private' => 0644,
                ],
                'dir' => [
                    'public' => 0755,
                    'private' => 0755,
                ],
            ]))
        );
    }

    /**
     * @return ImageManager
     */
    public function getImageManager()
    {
        $driver = 'gd';
        if (isset($this->config['driver'])) {
            $driver = $this->config['driver'];
        }

        return new ImageManager([
            'driver' => $driver,
        ]);
    }


}

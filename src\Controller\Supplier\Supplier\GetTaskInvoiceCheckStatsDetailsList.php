<?php

declare(strict_types=1);

namespace App\Controller\Supplier\Supplier;

use App\App\Constants;
use App\Lib\Func;
use App\Models\Supplier\Supplier;
use App\ModelsOrg\OrgSupplier;
use App\ModelsTask\LexOffice\LoInvoiceCheck;
use Slim\Http\Request;
use Slim\Http\Response;

/**
 * Getting v_findetails_all stats from WHC_Task
 */
final class GetTaskInvoiceCheckStatsDetailsList extends Base
{
    public function __invoke(Request $request, Response $response): Response
    {
        $page = intval($request->getQueryParam('page', 1));
        $perPage = intval($request->getQueryParam('perPage', Constants::DEFAULT_PER_PAGE_PAGINATION));
        $params = $request->getParams();

        $qb = LoInvoiceCheck::query()->from('v_findetails_all', 'fd');
        $qb->select('supplier.supp_supplier_id');
        $qb->addSelect('supplier.name');
        $qb->addSelect('supplier.org_a');
        // $qb->selectSub(OrgSupplier::query()->select('org_a')->whereColumn('supplier.supp_supplier_id', 'suppliers.supp_supplier_id'), 'org_a');
        $qb->addSelect('fd.*');

        // $qb->leftJoin('supplier', 'supplier.name', '=', 'fd.supplier_name');
        $qb->leftJoinSub('select distinct `name`, `org_a`, `supp_supplier_id` from `supplier`', 'supplier', 'supplier.name', '=', 'fd.supplier_name');

        if (key_exists('supp_supplier_id', $params)) {
            if ($params['supp_supplier_id'] ?? null) {
                $qb->where('supplier.supp_supplier_id', $params['supp_supplier_id']);
            } else {
                $qb->whereNull('supplier.supp_supplier_id');
            }
        }
        if ($params['y'] ?? null) {
            // This is fiscal year!!!
            $from = sprintf('%s-%02d-01', $params['y'], FY_START_MONTH);
            $to = sprintf('%s-%02d-01', $params['y'] + 1, FY_START_MONTH);
            $qb->whereRaw('fd.Datum >= ? AND fd.Datum < ?', [$from, $to]);
        }
        if ($params['ym'] ?? null) {
            $qb->whereRaw('LEFT(fd.Datum, 7) = ?', [$params['ym']]);
        }

        // user permission
        $user = Func::getSesUser();
        $userPermOption = $user['settings']["org_stats_item"] ?? 'option1';
        $permOption = $userPermOption;
        $requestedPermOption = $params['org_stats_user_level'] ?? 'option1';
        if ($requestedPermOption <= $userPermOption) {
            $permOption = $requestedPermOption;
        }
        if ($permOption == 'option1') {
            $qb->where('fd.Artikelnummer', '<', 50)
                ->where('fd.Artikelnummer', '!=', 21);
        } else if ($permOption == 'option2') {
            $qb->where('fd.Artikelnummer', '<', 50);
        }
        // Always view >= 10 since 2025-08-04
        $qb->where('fd.Artikelnummer', '>=', 10);

        $totalCount = $qb->count();

        $result = $this->supplierService->getSupplierRepository()->getResultsWithPagination($qb, $page, $perPage, $totalCount);

        if ($totalCount) {
            $supp_supplier_ids = array_map(function ($x) {
                return $x['supp_supplier_id'];
            }, $result['data']);

            if ($supp_supplier_ids) {
                $kv = Supplier::query()->whereIn('id', $supp_supplier_ids)->get()->keyBy('id')->toArray();
                foreach ($result['data'] as &$x) {
                    $x['supp_supplier'] = $kv[$x['supp_supplier_id']] ?? null;
                }
            }
        }

        return $this->jsonResponse($response, 'success', $result, 200);
    }
}

drop table if exists offer_template_lang_file;
drop table if exists offer_template_lang;
drop table if exists offer_template;

CREATE TABLE `offer_template`
(
    `id`         int(11)     NOT NULL AUTO_INCREMENT,
    `offer_no`   varchar(15) NOT NULL,
    `title`      varchar(255) DEFAULT NULL COMMENT 'Template Title',
    `created_on` datetime     DEFAULT NULL,
    `updated_on` datetime     DEFAULT NULL,
    `created_by` int(11)      DEFAULT NULL,
    `updated_by` int(11)      DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `IDX_offer_template_title` (`title`),
    UNIQUE KEY `UQ_offer_template_offer_no` (`offer_no`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;

CREATE TABLE `offer_template_lang`
(
    `id`                int(11)                   NOT NULL AUTO_INCREMENT,
    `offer_template_id` int(11)                   NOT NULL COMMENT 'FK: template ID',
    `lang`              varchar(3)   DEFAULT 'DE' NOT NULL COMMENT 'FK: Lang',
    `subject`           varchar(255) DEFAULT NULL COMMENT 'Subject',
    `header`            TEXT         DEFAULT NULL COMMENT 'Header',
    `body`              longtext     DEFAULT NULL COMMENT 'Subject',
    `footer`            TEXT         DEFAULT NULL COMMENT 'Footer',
    `created_on`        datetime     DEFAULT NULL,
    `updated_on`        datetime     DEFAULT NULL,
    `created_by`        int(11)      DEFAULT NULL,
    `updated_by`        int(11)      DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `UQ_offer_template_lang_mix` (`offer_template_id`, `lang`),
    KEY `IDX_offer_template_lang_subject` (`subject`),
    CONSTRAINT `FK_offer_template_lang_offer_template_id` FOREIGN KEY (`offer_template_id`) REFERENCES `offer_template` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;

CREATE TABLE `offer_template_file`
(
    `offer_template_id` int(11)             NOT NULL,
    `file_id`           bigint(20) unsigned NOT NULL,
    PRIMARY KEY (`offer_template_id`, `file_id`),
    KEY `FK_offer_template_file_file_id` (`file_id`),
    CONSTRAINT `FK_offer_template_file_file_id` FOREIGN KEY (`file_id`) REFERENCES `file` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `FK_offer_template_file_offer_template_id` FOREIGN KEY (`offer_template_id`) REFERENCES `offer_template_lang` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;



insert into `sys_dict`(`code`, `type`, `label`, `sort`, `value`, `settings`)
values ('OFFER_SALUTATION_DE', 'Offer', 'Offer Salutation (DE)', 1, 'Dear', '{"htmlField":"textarea"}')
     , ('OFFER_SALUTATION_EN', 'Offer', 'Offer Salutation (EN)', 2, 'Dear', '{"htmlField":"textarea"}')
     , ('OFFER_FOOTER_DE', 'Offer', 'Offer Footer (DE)', 3, 'footer DE', '{"htmlField":"textarea"}')
     , ('OFFER_FOOTER_EN', 'Offer', 'Offer Footer (EN)', 4, 'footer EN', '{"htmlField":"textarea"}')
;

insert into `sys_dict`(`code`, `type`, `label`, `sort`, `value`)
values ('OFFER_HIDE_COMPANY', 'Offer', 'Hide company column in offer list', 5, '1');


ALTER TABLE `customer_address`
    CHANGE `street1` `street1` VARCHAR(255) CHARSET utf8 COLLATE utf8_general_ci NULL COMMENT 'Street Address';


CREATE TABLE `offer_customer`
(
    `offer_no`    varchar(15)      NOT NULL,
    `customer_id` int(10) unsigned NOT NULL,
    PRIMARY KEY (`offer_no`, `customer_id`),
    -- KEY `FK_offer_customer_customer_id` (`customer_id`),
    -- KEY `FK_offer_customer_offer_no` (`offer_no`),
    CONSTRAINT `FK_offer_customer_customer_id` FOREIGN KEY (`customer_id`) REFERENCES `customer` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;

<?php

declare(strict_types=1);

namespace App\Service\Offer\OfferExt;

use App\Service\BaseService;
use App\Repository\Offer\OfferExtRepository;
use Slim\Container;

abstract class Base extends BaseService
{
    private const REDIS_KEY = 'Offer:%s';
    protected OfferExtRepository $offerExtRepository;

    public function __construct(Container $container)
    {
        $this->offerExtRepository = $container->get(OfferExtRepository::class);
    }

    public function getOfferExtRepository()
    {
        return $this->offerExtRepository;
    }
}


<?php

declare(strict_types=1);

namespace App\Service\Offer\OfferBlog;

use App\Exception\BaseException;
use App\Lib\Func;
use App\Models\File;
use App\Models\Offer\OfferBlog;
use App\Service\BaseService;
use App\Service\File\FileService;
use Intervention\Image\ImageManager;
use Psr\Http\Message\UploadedFileInterface;

final class OfferBlogService extends Base
{
    public function create(array $input): OfferBlog
    {
        return OfferBlog::create($input);
    }

    public function update($id, $input): OfferBlog
    {
        $row = OfferBlog::findOrFail($id);
        $row->update($input);
        return $row;
    }

    /**
     * @param array $input
     * @param UploadedFileInterface[] $files
     * @return OfferBlog
     */
    public function updateOrCreate(array $input, $files = []): OfferBlog
    {
        $offer_no = $input['offer_no'];
        /** @var OfferBlog $blog */
        $blog = OfferBlog::query()->firstOrNew(['offer_no' => $input['offer_no']]);

        $blog->fill($input);

        if (key_exists('description', $input)) {
            $blog->description = BaseService::extractInlineImage($input['description'], 'supplier_blog');
        }
        $blog->save();

        // files
        if ($files) {
            $pathRel = DS . File::CAT_OFFER_BLOG_FILE . DS . $offer_no;

            $dbFiles = [];
            foreach ($files as &$file) {
                $dbFiles[] = FileService::uploadFile(File::CAT_OFFER_BLOG_FILE, $file, $pathRel);
            }
            $blog->files()->saveMany($dbFiles);
        }

        return $blog;
    }

    public function copyFileAndResize($offer_no, $file_id)
    {
        /** @var OfferBlog $blog */
        $blog = OfferBlog::query()->where('offer_no', $offer_no)->firstOrFail();
        /** @var File $orgFile */
        $orgFile = $blog->files()->where('id', $file_id)->firstOrFail();

        if (!str_starts_with($orgFile->type, 'image')) {
            BaseException::raiseInvalidRequest("File is not image!");
        }

        $pathInfo = pathinfo($orgFile->org_path);
        $newFileName = uniqid() . '.' . $pathInfo['extension'];
        $newAbsPath = File::getBasePath(File::CAT_OFFER_BLOG_FILE) . $pathInfo['dirname'] . DS . $newFileName;
        copy($orgFile->abs_path, $newAbsPath);

        // Resize image
        $orgFileFullPath = $newAbsPath;
        $im = new ImageManager(['driver' => 'gd',]);
        $orgImage = $im->make($orgFileFullPath);
        $orgImage->resize(400, 400, function ($constraint) {
            $constraint->aspectRatio();
            $constraint->upsize();
        });
        $orgImage->resizeCanvas(400, 400, 'center', false, '#ffffff');
        $orgImage->save($orgFileFullPath);
        $orgImage->destroy();

        $file = new File();
        $file->category = File::CAT_OFFER_BLOG_FILE;
        $file->file_name = $newFileName;
        $file->clean_file_name = $newFileName;
        $file->path = $file->org_path = $pathInfo['dirname'] . '/' . $newFileName;
        $file->type = $orgFile->type;
        clearstatcache();
        $file->size = @filesize($orgFileFullPath);

        $blog->files()->save($file);

        return $file;
    }

    public function getOne(int $id, array $params = []): OfferBlog
    {
        return $this->offerBlogRepository->getQueryBuilder()->where('id', $id)->first();
    }

    public function getOfferBlogsByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($perPage < 1) {
            $perPage = self::DEFAULT_PER_PAGE_PAGINATION;
        }

        return $this->offerBlogRepository->getOfferBlogsByPage(
            $page,
            $perPage,
            $params
        );
    }

    public function delete($ids): void
    {
        $arr = is_array($ids) ? $ids : explode(',', $ids);
        $this->offerBlogRepository->getQueryBuilder()->whereIn('id', $arr)->delete();
    }
}

<?php

declare(strict_types=1);

namespace App\Repository\Customer;

use App\Lib\Func;
use App\Lib\FuncModel;
use App\Models\Customer\Customer;
use App\Models\Email\Email;
use App\Repository\BaseRepositoryORM;
use App\Service\BaseService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;

final class CustomerRepository extends BaseRepositoryORM
{
    public function getQueryCustomersByPage($params = []): Builder
    {
        $qb = $this->getQueryBuilder();

        if (!empty($params)) {
            BaseService::trimInputData($params);

            if ($params['id'] ?? null) {
                $qb->where('id', $params['id']);
            }
            if ($params['ids'] ?? null) {
                $qb->whereIn('id', $params['ids']);
            }


            if ($params['firstname'] ?? null) {
                $qb->where('firstname', 'like', FuncModel::likeValue($params['firstname']));
            }
            if ($params['org_a'] ?? null) {
                $qb->where('org_a', $params['org_a']);
            }
            if (($params['relevance'] ?? '') !== '') {
                $qb->whereHas('ext', function ($qb) use (&$params) {
                    $qb->where('relevance', sprintf("%03d", $params['relevance']));
                });
            }
            if ($params['user_id'] ?? null) {
                $qb->whereHas('ext', function ($qb) use (&$params) {
                    $qb->where('user_id', $params['user_id']);
                });
            }


            if ($keyWords = $params['keyWords'] ?? null) {
                $qb->where(function (Builder $builder) use (&$keyWords) {
                    $builder
                        ->where('name', 'like', FuncModel::likeValue($keyWords, '%'))
                        ->orWhereHas('contacts', function (Builder $builder) use (&$keyWords) {
                            $builder
                                ->where('firstname', 'like', FuncModel::likeValue($keyWords, '%'))
                                ->orWhere('email', 'like', FuncModel::likeValue($keyWords, '%'))
                                ->orWhere('lastname', 'like', FuncModel::likeValue($keyWords, '%'));
                        });;
                });
            }

            if ($params['product_trademark'] ?? null) {
                $qb->whereHas('productTrademarks', function ($builder) use (&$params) {
                    $builder->where('value', $params['product_trademark']);
                });
            }

            if ($params['customer_type'] ?? null) {
                $qb->whereHas('customerType', function ($builder) use (&$params) {
                    $builder->where('value', $params['customer_type']);
                });
            }

            // Relations
            if (Func::keyExistsInWithParam($params, 'address')) {
                $qb->with('address', function ($builder) use (&$params) {
                    if (Func::keyExistsInWithParam($params, 'address.country')) {
                        $builder->with('country');
                    }
                    if (Func::keyExistsInWithParam($params, 'address.countryRegion')) {
                        $builder->with('countryRegion');
                    }
                });
            }

            if (Func::keyExistsInWithParam($params, 'calls')) {
                $qb->with('calls');
            }

            if (Func::keyExistsInWithParam($params, 'info')) {
                $qb->with('info');
            }

            if (Func::keyExistsInWithParam($params, 'ext')) {
                $qb->with('ext');
            }

            if (Func::keyExistsInWithParam($params, 'contacts')) {
                $qb->with('contacts', function (HasMany $builder) use (&$params) {
                    $builder->select("customer_contact.*");
                    if (Func::keyExistsInWithParam($params, 'contacts.sentOfferStatus') && ($offerNo = $params['offerNo'] ?? null)) {
                        $builder->selectSub(
                            Email::query()
                                ->selectRaw("1")
                                ->join('email_receiver', 'email_receiver.email_id', '=', 'email.id')
                                ->whereColumn('email_receiver.email', 'customer_contact.email')
                                ->where('email.offer_no', $offerNo)
                                ->limit(1)
                            , 'is_offer_sent');
                    }

                    $builder->sortDefault();
                });
            }

            if (Func::keyExistsInWithParam($params, 'contactsAll')) {
                $qb->with('contactsAll', function (HasMany $builder) {
                    $builder->sortDefault();
                });
            }

            $qb = $this->applyOrderBy($qb, $params);
        }

        return $qb;
    }

    /**
     * Get lists by pagination.
     *
     * @param int $page
     * @param int $perPage
     * @param array|null $params
     * @return array
     */
    public function getCustomersByPage(
        int    $page,
        int    $perPage,
        ?array $params
    ): array
    {
        $query = $this->getQueryCustomersByPage($params);
        $total = $this->getCountByQuery($query);

        $result = $this->getResultsWithPagination(
            $query,
            $page,
            $perPage,
            $total
        );

        if ($total) {

        }

        return $result;
    }

    public function getQueryBuilder(): Builder
    {
        return Customer::query();
    }
}
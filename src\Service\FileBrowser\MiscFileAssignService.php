<?php

namespace App\Service\FileBrowser;

use App\Exception\BaseException;
use App\Lib\FileBrowserLib;
use App\Lib\Func;
use App\Lib\FuncModel;
use App\Models\Sys\Dict;
use App\Service\BaseService;
use Slim\Container;

class MiscFileAssignService extends BaseService
{
    protected Container $container;

    protected ?string $rootPath = null;

    public function __construct(Container $container)
    {
        $this->container = $container;
        // $this->rootPath = FuncModel::getDictValue(Dict::CODE_FM_MISC_FILE_BASE_PATH);

        if (!$this->rootPath) {
            BaseException::raiseInvalidRequest('Scannable base path is not defined in system config!');
        }

        if (!file_exists($this->rootPath)) {
            BaseException::raiseInvalidRequest('Scannable base path ' . $this->rootPath . ' does not exist!');
        }
    }

    public function getRootPath()
    {
        return $this->rootPath;
    }

    public function getAllFiles($options = []): array
    {
        $basePath = $this->rootPath;

        if (!file_exists($basePath)) {
            BaseException::raiseInvalidRequest('Base path ' . $basePath . ' not found!');
        }

        $list = array_diff(scandir($basePath), array('..', '.'));
        $ret = [];
        if ($list) {
            foreach ($list as $fileName) {
                // skip directories
                if (is_dir($basePath . DS . $fileName)) continue;
                $ret[] = $this->getFileDetail('', $fileName);
            }
        }
        return $ret;
    }

    public function getFileDetail($parentPath, $fileName)
    {
        $isValidFileName = Func::validPath($fileName);
        $absPath = $isValidFileName ? Func::pathJoin($this->rootPath, $parentPath, $fileName) : Func::pathJoin($this->rootPath, $parentPath);
        $stat = stat($absPath);
        $fileArr = [
            'id' => FileBrowserLib::encode(Func::pathJoin($parentPath, $isValidFileName ? $fileName : '!')),
            'name' => $isValidFileName ? $fileName : 'Root',
            'createdTime' => $stat ? date(DATE_RFC3339_EXTENDED, $stat['ctime']) : null,
            'modDate' => $stat ? date( DATE_RFC3339_EXTENDED, $stat['mtime']) : null,
            'size' => $stat ? $stat['size'] : null,
        ];
        return $fileArr;
    }
}
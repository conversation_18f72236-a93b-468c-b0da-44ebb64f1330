<?php

namespace App\Models\Email;

use App\Models\BaseModel;

/**
 * @property integer $id
 * @property string $domain
 * @property string $imap_host
 * @property integer $imap_port
 * @property integer $imap_port_ssl
 * @property integer $imap_ssl
 * @property string $pop_host
 * @property integer $pop_port
 * @property integer $pop_port_ssl
 * @property string $smtp_host
 * @property integer $smtp_port
 * @property integer $smtp_user
 * @property integer $smtp_password
 * @property string $settings
 * @property boolean $is_oauth
 * @property EmailAccount[] $emailAccounts
 */
class EmailServer extends BaseModel
{
    protected $casts = [
        'settings' => 'array'
    ];

    
    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'email_server';

    protected $hidden = ['smtp_password'];

    /**
     * Indicates if the IDs are auto-incrementing.
     * 
     * @var bool
     */
    public $incrementing = false;

    /**
     * @var array
     */
    protected $fillable = ['domain', 'imap_host', 'imap_port', 'imap_port_ssl', 'imap_ssl', 'pop_host', 'pop_port', 'pop_port_ssl', 'smtp_host', 'smtp_port', 'smtp_user', 'smtp_password', 'settings', 'is_oauth'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function emailAccounts()
    {
        return $this->hasMany('App\Models\Email\EmailAccount', 'server_id');
    }
}

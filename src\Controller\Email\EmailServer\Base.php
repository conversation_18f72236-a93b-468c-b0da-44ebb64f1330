<?php

declare(strict_types=1);

namespace App\Controller\Email\EmailServer;

use App\Controller\BaseController;
use App\Exception\BaseException;
use App\Service\Email\EmailServer\EmailServerService;
use Respect\Validation\Validator as v;
use Slim\Container;

abstract class Base extends BaseController
{
    public EmailServerService $emailServerService;

    public function __construct(Container $container)
    {
        parent::__construct($container);
        $this->emailServerService = $container->get(EmailServerService::class);
    }

	public function validate(array $input): bool {
        $validator = v::key('imap_host', v::stringType()->length(1, 255));

        if (!$validator->validate($input)) {
            \App\Exception\Base::raiseInvalidRequest();
        }

        return true;
    }
}

<?php

declare(strict_types=1);

namespace App\Service\User;

use App\Models\User;

final class Update extends Base
{
    public function update(array $input, int $userId): array
    {
        /** @var User $user */
        $user = $this->userRepository->update($input, $userId);
        return $user;
    }

    private function validateUserData(array $input, int $userId): User
    {
        $user = $this->getUserFromDb($userId);
        $data = json_decode((string) json_encode($input), false);
        if (! isset($data->name) && ! isset($data->email)) {
            throw new \App\Exception\User('Enter the data to update the user.', 400);
        }
        if (isset($data->name)) {
            $user->updateName(self::validateUserName($data->name));
        }
        if (isset($data->email) && $data->email !== $user->getEmail()) {
            $this->userRepository->checkUserByEmail($data->email);
        }
        if (isset($data->email)) {
            $user->updateEmail(self::validateEmail($data->email));
        }

        return $user;
    }
}

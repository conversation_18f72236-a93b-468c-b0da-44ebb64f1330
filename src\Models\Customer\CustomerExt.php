<?php

namespace App\Models\Customer;

use App\Models\BaseModel;

/**
 * @property integer $id
 * @property integer $customer_id
 * @property string $note
 * @property integer $user_id
 * @property integer $relevance
 * @property string $created_on
 * @property integer $created_by
 * @property string $updated_on
 * @property integer $updated_by
 */
class CustomerExt extends BaseModel
{
    public $timestamps = true;

    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'customer_ext';

    /**
     * @var array
     */
    protected $fillable = ['customer_id', 'note', 'user_id', 'relevance', 'created_on', 'created_by', 'updated_on', 'updated_by'];
}

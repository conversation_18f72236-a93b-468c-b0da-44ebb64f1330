import type { Dispatch, SetStateAction } from "react";
import React, { useEffect, useMemo, useRef, useState } from "react";
import type { UploadFile } from "antd";
import { Avatar, Button, Col, Image, Dropdown, message, Popover, Row, Space, Spin, Typography, Alert } from "antd";
import type { ProFormInstance } from "@ant-design/pro-form";
import { ProFormUploadButton } from "@ant-design/pro-form";
import ProForm, { ProFormText, ModalForm } from "@ant-design/pro-form";
import { sendEmail } from "@/services/app/Email/email";
import Util, { nl2br, sEllipsed } from "@/util";
import HtmlEditor from "@/components/HtmlEditor";
import { DownOutlined, FileOutlined, FilePdfOutlined, FileTextOutlined, FileWordOutlined, InfoCircleOutlined } from "@ant-design/icons";
import { ProFormRadio, ProFormTextArea } from "@ant-design/pro-components";
import { useModel } from "@umijs/max";
import { DictCode } from "@/constants";
import useUserOptions from "@/hooks/BasicData/useUserOptions";
import { getOfferTemplateByOfferNo } from "@/services/app/Offer/offer-template";
import styles from "./CreateOfferEmailForm.less";

export type FormValueType = Partial<API.Email> & {
  files?: UploadFile[];
  internal?: boolean;
  offer_no?: string | number;
  lang?: string;
  mode?: "byOrgOffer";
};

const handleSend = async (fields: FormValueType) => {
  const hide = message.loading("Sending...", 0);

  const data = new FormData();

  if (fields.mode) {
    data.append("mode", `${fields.mode}`);
  }
  if (fields.offer_no) {
    data.append("offer_no", `${fields.offer_no}`);
  }
  if (fields.lang) {
    data.append("lang", `${fields.lang}`);
  }

  if (fields.id) {
    data.append("id", `${fields.id}`);
  }
  data.append("receiver", `${fields.receiver || ""}`);
  if (fields.bcc) {
    data.append("bcc", `${fields.bcc}`);
  }
  data.append("sender", `${fields.sender}`);
  data.append("subject", `${fields.subject || ""}`);
  data.append("text_html", `${fields.text_html}`);
  if (fields.customer_id) {
    data.append("customer_id", `${fields.customer_id}`);
  }
  if (fields.internal) {
    data.append("internal", `${fields.internal}`);
  }

  if (fields.files?.length) {
    fields.files.forEach((file, ind) => {
      data.append(`files[${ind}]`, file?.originFileObj ?? "");
    });
  }

  try {
    await sendEmail(data);
    hide();
    return true;
  } catch (error) {
    hide();
    Util.error(error);
    return false;
  }
};

export type CreateOfferEmailFormProps = {
  offerNo?: number | string;
  initialValues?: Partial<API.Email>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  htmlEditorId?: string;
  internal?: boolean; // Is this an internal email?
  onSubmit?: (formData: API.Email) => Promise<boolean | void>;
  onCancel?: (flag?: boolean, formValues?: FormValueType) => void;
};

const CreateOfferEmailForm: React.FC<CreateOfferEmailFormProps> = (props) => {
  const { initialValues, modalVisible, handleModalVisible, htmlEditorId, offerNo, internal } = props;

  const { initialState } = useModel("@@initialState");
  const { getDictByCode } = useModel("app-settings");

  const formRef = useRef<ProFormInstance>();

  // show/hide BCC
  const [showBcc, setShowBcc] = useState<boolean>(false);

  const defaultEmailFrom = useMemo(() => getDictByCode(DictCode.EMAIL_FROM), [getDictByCode]);

  const { userOptions } = useUserOptions();

  // offer template
  const [lang, setLang] = useState<"DE" | "EN">("DE");
  const [template, setTemplate] = useState<API.OfferTemplate>();
  const [langData, setLangData] = useState<API.OfferTemplateLang>();
  const [loadingTemplate, setLoadingTemplate] = useState(false);

  useEffect(() => {
    if (modalVisible) {
      setLoadingTemplate(true);
      getOfferTemplateByOfferNo(offerNo, { with: "offerTemplateLangs,files" })
        .then((res) => {
          setTemplate(res);
        })
        .catch(Util.error)
        .finally(() => setLoadingTemplate(false));
    }
  }, [modalVisible, offerNo]);

  useEffect(() => {
    if (modalVisible && lang && template) {
      setLangData(template?.offer_template_langs?.find((x) => x.lang == lang));
    } else {
      setLangData(undefined);
    }
  }, [lang, modalVisible, template, template?.offer_template_langs]);

  useEffect(() => {
    if (modalVisible && formRef && formRef.current) {
      formRef.current.resetFields();
      const newValues = { ...(initialValues || {}) };

      if (!newValues.sender) {
        if (internal) {
          newValues.sender = Util.emailBuildSender(initialState?.currentUser?.email, initialState?.currentUser?.name);
        } else {
          newValues.sender = defaultEmailFrom;
        }
      }

      let textHtml = "";
      if (internal) {
        if (langData?.subject) {
          if (!newValues.subject) {
            newValues.subject = `Internal Message to ${langData?.subject}`;
          }
        }
      } else {
        newValues.subject = langData?.subject || "";
      }

      if (langData) {
        if (langData.header) {
          textHtml += `${langData?.header}`;
        }
        if (langData.body) {
          textHtml += `<br />${langData?.body}`;
        }
        if (langData.footer) {
          textHtml += `<br />${langData?.footer}`;
        }
      }

      // We replace text_plain by <br />
      textHtml += (initialValues?.text_html ? initialValues?.text_html : nl2br(initialValues?.text_plain)) ?? "";
      newValues.text_html = textHtml;

      formRef.current.resetFields();
      formRef.current.setFieldsValue(newValues);
    }
  }, [defaultEmailFrom, initialState?.currentUser?.email, initialState?.currentUser?.name, initialValues, internal, modalVisible, langData]);

  return (
    <ModalForm<API.Email>
      title={
        <Row>
          <Col flex="auto">
            <Space size={32} style={{ width: "100%" }}>
              <span>New Email about Offer #{offerNo}</span>
            </Space>
          </Col>
          {internal ? null : (
            <Col>
              <Space size={12} style={{ marginRight: 24 }}>
                <ProFormRadio.Group
                  options={[
                    { value: "DE", label: "DE" },
                    { value: "EN", label: "EN" },
                  ]}
                  fieldProps={{
                    value: lang,
                    onChange(e) {
                      setLang(e.target.value);
                    },
                  }}
                />
              </Space>
            </Col>
          )}
        </Row>
      }
      width="1000px"
      open={modalVisible}
      onOpenChange={handleModalVisible}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 4 }}
      labelWrap={true}
      wrapperCol={{ span: 20 }}
      formRef={formRef}
      colon={false}
      onFinish={async (value) => {
        const success = await handleSend({
          ...value,
          id: initialValues?.id,
          internal,
          mode: "byOrgOffer",
          offer_no: offerNo,
          lang: lang,
        });

        if (success) {
          handleModalVisible(false);
          if (props.onSubmit) props.onSubmit(value);
        }
      }}
      submitter={{ searchConfig: { submitText: "Send" }, submitButtonProps: { loading: loadingTemplate, disabled: !langData } }}
    >
      <Spin spinning={loadingTemplate} className={styles.createOfferEmailForm}>
        {!langData && (
          <Alert
            description={`Offer template in ${lang} does not exist. Please create an offer in DE or EN.`}
            type="error"
            style={{ marginBottom: 24 }}
          />
        )}
        <ProFormText
          name="sender"
          label="From"
          placeholder={"Sender Email"}
          rules={[
            {
              required: true,
            },
          ]}
          width="md"
          addonAfter={
            <Space>
              <Button
                onClick={() => {
                  formRef.current?.setFieldValue("sender", `${initialState?.currentUser?.name} <${initialState?.currentUser?.email}>`);
                }}
              >
                Me
              </Button>
              <Button
                onClick={() => {
                  formRef.current?.setFieldValue("sender", defaultEmailFrom);
                }}
              >
                Default
              </Button>
            </Space>
          }
        />
        <ProFormTextArea
          name="receiver"
          label={
            <Space direction="horizontal" size={6}>
              <div>To</div>
              <Space direction="horizontal">
                <Dropdown
                  menu={{
                    onClick: (info) => {
                      const selectedUser = (userOptions as API.CurrentUser[]).find((x) => `${x.user_id}` == info.key);
                      if (selectedUser) {
                        const oldReceiverTmp = formRef.current?.getFieldValue("receiver");
                        const oldReceiverListKv: any = Util.emailParseStrListKv(oldReceiverTmp ?? "");

                        if (!oldReceiverListKv[`${selectedUser.email}`]) {
                          oldReceiverListKv[`${selectedUser.email}`] = [selectedUser.email, `${selectedUser.name}`];
                        }

                        // flatten receivers list
                        const tmp = [];
                        // eslint-disable-next-line guard-for-in
                        for (const email in oldReceiverListKv) {
                          let receiver = email;
                          if (oldReceiverListKv[email]?.[1]) {
                            receiver = `${oldReceiverListKv[email]?.[1]} <${receiver}>`;
                          }
                          tmp.push(receiver);
                        }

                        formRef.current?.setFieldValue("receiver", tmp.join(","));
                      }
                    },
                    items: (userOptions as API.CurrentUser[])?.map((user) => ({
                      ...user,
                      key: user.user_id,
                      label: `${user.initials} | ${user.name}`,
                    })) as any,
                  }}
                >
                  <Button>
                    <Space>S</Space>
                  </Button>
                </Dropdown>
              </Space>
            </Space>
          }
          placeholder={"To Emails: e.g. Aslam Doctor <<EMAIL>>,Julia <julia.hotmail.com>,<EMAIL>"}
          rules={[
            {
              required: true,
              message: "Type or select emails",
            },
          ]}
          fieldProps={{ rows: 2 }}
          width={740}
          allowClear
          addonAfter={
            showBcc ? (
              <></>
            ) : (
              <Button
                type="link"
                onClick={() => {
                  setShowBcc(true);
                }}
              >
                BCC
              </Button>
            )
          }
        />
        {showBcc ? (
          <ProFormTextArea
            name="bcc"
            label={
              <Space direction="horizontal" size={6}>
                <div>BCC</div>
                <Space direction="vertical">
                  <Dropdown
                    menu={{
                      onClick: (info) => {
                        const selectedUser = (userOptions as API.CurrentUser[]).find((x) => `${x.user_id}` == info.key);
                        if (selectedUser) {
                          const oldReceiverTmp = formRef.current?.getFieldValue("bcc");
                          const oldReceiverListKv: any = Util.emailParseStrListKv(oldReceiverTmp ?? "");

                          if (!oldReceiverListKv[`${selectedUser.email}`]) {
                            oldReceiverListKv[`${selectedUser.email}`] = [selectedUser.email, `${selectedUser.name}`];
                          }

                          // flatten receivers list
                          const tmp = [];
                          // eslint-disable-next-line guard-for-in
                          for (const email in oldReceiverListKv) {
                            let receiver = email;
                            if (oldReceiverListKv[email]?.[1]) {
                              receiver = `${oldReceiverListKv[email]?.[1]} <${receiver}>`;
                            }
                            tmp.push(receiver);
                          }

                          formRef.current?.setFieldValue("bcc", tmp.join(","));
                        }
                      },
                      items: (userOptions as API.CurrentUser[])?.map((user) => ({
                        ...user,
                        key: user.user_id,
                        label: `${user.initials} | ${user.name}`,
                      })) as any,
                    }}
                  >
                    <Button>
                      <Space>
                        Staff
                        <DownOutlined />
                      </Space>
                    </Button>
                  </Dropdown>
                </Space>
              </Space>
            }
            placeholder={"BCC Emails: e.g. Aslam Doctor <<EMAIL>>,Julia <julia.hotmail.com>,<EMAIL>"}
            fieldProps={{ rows: 1 }}
            width={740}
            allowClear
            addonAfter={
              <Space>
                <Button
                  type="link"
                  onClick={() => {
                    setShowBcc(false);
                  }}
                >
                  Hide
                </Button>
              </Space>
            }
          />
        ) : null}
        <ProFormText name="subject" label="Subject" />
        <ProFormUploadButton
          name="files"
          label="Attachments"
          title="Select Files"
          listType="text"
          formItemProps={{ wrapperCol: { span: 12 } }}
          fieldProps={{
            multiple: true,
            beforeUpload: () => {
              return false;
            },
            style: { marginBottom: 24 },
          }}
        />

        {template?.files ? (
          <Row>
            <Col span={20} offset={4}>
              <div className="ant-upload-list-item-container" style={{ marginTop: 0, marginBottom: 8 }}>
                <Row gutter={24} wrap={true}>
                  {template?.files?.map((a) => {
                    const fileUrl = `${API_URL}/api/${a.url}`;

                    const eleStyles = {
                      style: {
                        fontSize: 48,
                        color: "grey",
                      },
                    };
                    let ele = null;
                    if (a.type == "application/pdf") {
                      ele = <FilePdfOutlined {...eleStyles} />;
                    } else if (a.clean_file_name?.endsWith(".docx") || a.clean_file_name?.endsWith(".doc")) {
                      ele = <FileWordOutlined {...eleStyles} />;
                    } else if (a.clean_file_name?.endsWith(".txt")) {
                      ele = <FileTextOutlined {...eleStyles} />;
                    } else {
                      ele = <FileOutlined {...eleStyles} />;
                    }

                    return (
                      <Col key={a.id} span={8}>
                        <Space direction="horizontal" style={{ width: "100%" }}>
                          {a.type?.includes("image") ? (
                            <Avatar shape="square" size={48} src={<Image src={fileUrl} />} style={{ border: "1px solid #efefef", borderRadius: 8 }} />
                          ) : (
                            <div
                              style={{
                                textAlign: "center",
                                width: 48,
                                height: 48,
                                paddingTop: 0,
                              }}
                            >
                              {ele}
                            </div>
                          )}

                          <Typography.Link href={fileUrl} ellipsis target="_blank" className="text-sm" style={{ textAlign: "center" }}>
                            {sEllipsed(a.clean_file_name, 20, 8)}
                          </Typography.Link>
                        </Space>
                      </Col>
                    );
                  })}
                </Row>
              </div>
            </Col>
          </Row>
        ) : null}

        <ProForm.Item
          name={"text_html"}
          label={
            <>
              Body&nbsp;
              <Popover
                title="Usable keywords"
                content={
                  <>
                    <Typography.Text copyable={{ text: "%salutation%" }}>
                      <b>%salutation%</b>
                      {` e.g. Dear {firstName},`}
                    </Typography.Text>
                  </>
                }
              >
                <InfoCircleOutlined />
              </Popover>
            </>
          }
          style={{ width: "100%" }}
          labelCol={undefined}
          wrapperCol={{ span: 24 }}
        >
          <HtmlEditor id={htmlEditorId ?? `email_body_create`} initialFocus enableTextModule hideMenuBar height={400} />
        </ProForm.Item>
      </Spin>
    </ModalForm>
  );
};

export default CreateOfferEmailForm;

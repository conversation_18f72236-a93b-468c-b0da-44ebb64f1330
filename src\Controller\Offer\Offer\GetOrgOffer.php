<?php

declare(strict_types=1);

namespace App\Controller\Offer\Offer;

use App\Controller\BaseController;
use App\Exception\BaseException;
use App\Lib\Func;
use App\Models\Customer\Customer;
use App\Models\Offer\OfferBlog;
use App\Models\Offer\OfferExt;
use App\Models\Offer\OfferNewsletter;
use App\Models\Offer\OfferTemplate;
use App\Models\Supplier\Supplier;
use App\Models\Supplier\SupplierCall;
use App\ModelsOrg\OrgOffer;
use App\Service\BaseService;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Slim\Http\Request;
use Slim\Http\Response;

final class GetOrgOffer extends BaseController
{
    public function __invoke(Request $request, Response $response, array $args): Response
    {
        $offer_no = $args['offer_no'] ?? null;
        if (!$offer_no) {
            BaseException::raiseInvalidRequest('Offer No is required!');
        }

        /** @var OrgOffer $offer */
        $offer = OrgOffer::query()->where('offer_sid', $offer_no)->with('supplier')->firstOrFail();

        if ($offer->supplier?->supp_supplier_id) {
            $supplier = Supplier::query()->where('id', $offer->supplier->supp_supplier_id)->first();
            $offer->setAttribute('supp_supplier', $supplier);
        }

        $params = $request->getParams();
        if (Func::keyExistsInWithParam($params, 'offerTemplate')) {
            $qb = OfferTemplate::query()
                ->with('offerTemplateLangs', function (HasMany $builder) {
                })
                ->with('files')
                ->where('offer_no', $offer_no);

            $offer->setAttribute('offer_template', $qb->first());
        }

        if (Func::keyExistsInWithParam($params, 'ext')) {
            $qb = OfferExt::query()
                ->where('offer_no', $offer_no);

            $offer->setAttribute('ext', $qb->first());
        }

        if (Func::keyExistsInWithParam($params, 'blog_cnt')) {
            $offer->setAttribute('blog_cnt', OfferBlog::query()->where('offer_no', $offer_no)->count());
        }
        if (Func::keyExistsInWithParam($params, 'newsletter_cnt')) {
            $offer->setAttribute('newsletter_cnt', OfferNewsletter::query()->where('offer_no', $offer_no)->count());
        }


        return $this->jsonResponse($response, 'success', $offer, 200);
    }
}

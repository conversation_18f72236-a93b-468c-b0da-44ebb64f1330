<?php

declare(strict_types=1);

namespace App\Middleware;

use App\Lib\Func;
use Monolog\Logger;
use Psr\Http\Message\ResponseInterface;
use Slim\Http\Request;
use Slim\Http\Response;

final class SlimRequestLogger extends Base
{
    public function __invoke(
        Request $request,
        Response $response,
        $next
    ): ResponseInterface {
        /** @var Logger $logger */
        $logger = $this->container->get('logger');
        $loggerLevel = $this->container->get('settings')['app']['logLevel'];

        if (Logger::DEBUG >= $loggerLevel && !($this->container->get('settings')['logger']['skipRequest'] ?? null)) {
            $CLRF = "\n\r";
            $str = $CLRF;
            $str .= date(DATE_FORMAT_YMD_HIS) . $CLRF;
            $str .= '============================================= REQUEST =============================================' . $CLRF;
            $str .=  "[{$request->getMethod()}] " . $request->getUri() . $CLRF;
            $str .= 'Params:' . $CLRF;
            $str .= json_encode((array)$request->getParams(), JSON_PRETTY_PRINT) . $CLRF;
            $str .= 'Body:' . $CLRF;
            $str .= json_encode((array)$request->getParsedBody(), JSON_PRETTY_PRINT) . $CLRF;
            $str .= '============================================= END REQUEST =========================================' . $CLRF;
            $logger->debug($str);
        }

        $response = $next($request, $response);

        if (Logger::DEBUG >= $loggerLevel && !($this->container->get('settings')['logger']['skipResponse'] ?? null)) {
            $str = $CLRF;
            $str .= '~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ RESPONSE ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~' . $CLRF;
            $str .=  "[{$request->getMethod()}] [{$response->getStatusCode()}] " . $request->getUri() . $CLRF;
            $str .= json_encode((array)Func::safeJson($response->getBody()->__toString()), JSON_PRETTY_PRINT) . $CLRF;
            $str .= '~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ END RESPONSE ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~' . $CLRF;
            $str .= date(DATE_FORMAT_YMD_HIS) . $CLRF;
            $logger->debug($str);
        }
        return $response;
    }
}

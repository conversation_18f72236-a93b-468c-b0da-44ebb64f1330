<?php

namespace App\Models\Offer;

use App\Models\BaseModel;
use App\Models\File;

/**
 * @property integer $id
 * @property string $offer_no
 * @property string $title
 * @property string $created_on
 * @property string $updated_on
 * @property integer $created_by
 * @property integer $updated_by
 *
 * @property OfferTemplateLang[] $offerTemplateLangs
 * @property File[] $files
 */
class OfferTemplate extends BaseModel
{
    public $timestamps = true;

    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'offer_template';

    /**
     * @var array
     */
    protected $fillable = ['offer_no', 'title', 'created_on', 'updated_on', 'created_by', 'updated_by'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function offerTemplateLangs()
    {
        return $this->hasMany('App\Models\Offer\OfferTemplateLang');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function files()
    {
        return $this->belongsToMany('App\Models\File', 'offer_template_file');
    }
}
